[tox]
envlist =
    py310,
    mac,
    flake8,

[py]
deps=
    pytest-cov
    pytest-remove-stale-bytecode

[testenv:py310]
deps=
    {[py]deps}
extras = all
passenv = *
basepython = python3.10
commands = pytest -v --cov=tests/ --cov-report=term --cov-report=html

[testenv:mac]
deps=
    {[py]deps}
    python-dotenv
extras = all
basepython = python
commands =
    pytest -x -v --cov=tests/ --cov-report=term --cov-report=html

[testenv:flake8]
exclude = .tox/*
deps = flake8
commands = flake8 src
