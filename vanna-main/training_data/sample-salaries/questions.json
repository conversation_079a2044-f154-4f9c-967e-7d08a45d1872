[{"question": "What is the engineer to product manager ratio in Facebook, Amazon, Google ? ", "answer": "SELECT company,\n       count(case when title like '%Engineer%' then 1\n                  else null end) as engineer_count,\n       count(case when title like '%Product Manager%' then 1\n                  else null end) as product_manager_count,\n       count(case when title like '%Engineer%' then 1\n                  else null end) / count(case when title like '%Product Manager%' then 1\n                  else null end) as ratio\nFROM   salaries_data\nWHERE  company in ('Facebook', 'Amazon', 'Google')\nGROUP BY company;"}, {"question": "Which 3 cities has the highest average salary for software engineers at Google ? ", "answer": "SELECT location,\n       avg(totalyearlycompensation) as average_salary\nFROM   salaries_data\nWHERE  company = 'Google'\n   and title like '%Software Engineer%'\nGROUP BY location\nORDER BY average_salary desc limit 3;"}, {"question": "Which is the top 4 most common level of software engineers at amazon ?", "answer": "SELECT level,\n       count(*) as count_employees\nFROM   salaries_data\nWHERE  company = 'Amazon'\n   and title like '%Software Engineer%'\nGROUP BY level\nORDER BY count_employees desc limit 4;"}, {"question": "what is the most compensated title related to software engineering at Google ?", "answer": "SELECT title,\n       max(totalyearlycompensation) as max_compensation\nFROM   salaries_data\nWHERE  company = 'Google'\n   and title like '%Software Engineer%'\nGROUP BY title\nORDER BY max_compensation desc limit 1;"}, {"question": "What is average total years of experience related to the 5 most popular job titles?", "answer": "SELECT title,\n       avg(yearsofexperience) as average_years_of_experience\nFROM   salaries_data\nWHERE  title in (SELECT title\n                 FROM   (SELECT title,\n                                count(*) as title_count\n                         FROM   salaries_data\n                         GROUP BY title\n                         ORDER BY title_count desc limit 5))\nGROUP BY title;"}, {"question": "What 5 companies have the highest average total compensation salaries in the top 25 employers?", "answer": "SELECT company,\r\n       avg(totalyearlycompensation) as average_compensation\r\nFROM   salaries_data\r\nWHERE  company in (SELECT company\r\n                   FROM   salaries_data\r\n                   GROUP BY company\r\n                   ORDER BY count(*) desc limit 25)\r\nGROUP BY company\r\nORDER BY average_compensation desc limit 5;"}, {"question": "What company has the most analysts in the top 50 employers ?", "answer": "SELECT company,\r\n       count(*) as analyst_count\r\nFROM   salaries_data\r\nWHERE  title ilike '%Analyst%'\r\n   and company in (SELECT company\r\n                FROM   salaries_data\r\n                GROUP BY company\r\n                ORDER BY count(*) desc limit 50)\r\nGROUP BY company\r\nORDER BY analyst_count desc limit 1;"}, {"question": "what are 10 common designation for employees ? ", "answer": "SELECT title,\r\n       count(*) as count_employees\r\nFROM   salaries_Data\r\nGROUP BY 1\r\nORDER BY count_employees desc limit 10;"}, {"question": "What is average total years of experience related to the 5 most popular job titles  ? Load bar chart", "answer": "Apologies, but I'm unable to assist."}, {"question": "Which 5 company has the highest avg stock value grants among the top 40 employers? ", "answer": "SELECT company,\n       avg(stockgrantvalue) as average_stock_value\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 40)\nGROUP BY company\nORDER BY average_stock_value desc limit 5;"}, {"question": "which 7 companies have the highest % of asians  employees, in the top 20 employers by size ? Draw bar chart ", "answer": "SELECT company,\n       count(case when race = 'Asian' then 1\n                  else null end) * 100.0 / count(*) as asian_percentage\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 20)\nGROUP BY company\nORDER BY asian_percentage desc limit 7;"}, {"question": "compare the spearman's correlation between yearsofexperience and basesalary and yearsatcompany and basesalary ", "answer": "The corrected SQL query is:\n\n```\nSELECT corr(rank_experience, rank_salary) as spearman_correlation_experience_salary,\n       corr(rank_years, rank_salary) as spearman_correlation_years_salary\nFROM (\n  SELECT yearsofexperience,\n         basesalary,\n         yearsatcompany,\n         RANK() OVER (ORDER BY yearsofexperience) as rank_experience,\n         RANK() OVER (ORDER BY basesalary) as rank_salary,\n         RANK() OVER (ORDER BY yearsatcompany) as rank_years\n  FROM salaries_data\n) subquery;\n```\n\nThe error occurred because the query was starting with \"<PERSON><PERSON><PERSON>'s correlation is not directly available in SQL.\" This text is not valid SQL syntax and should be removed."}, {"question": "What % of amazon engineers have a doctorate , master or a bachelors degree ? ", "answer": "SELECT case when doctorate_degree = 1 then 'Doctorate Degree'\n            when masters_degree = 1 then 'Master''s Degree'\n            when bachelors_degree = 1 then 'Bachelor''s Degree'\n            else 'Other' end as education_level,\n       avg(totalyearlycompensation) as average_compensation\nFROM   salaries_data\nGROUP BY education_level;"}, {"question": "what is the avg compensation of a doctorate, a bachelor's and a master's employee ", "answer": "SELECT case when doctorate_degree = 1 then 'A. Doctorate Degree'\r\n            when masters_degree = 1 and doctorate_degree = 0 then 'B. Masters Degree' \r\n            when bachelors_degree = 1 and masters_degree = 0 and doctorate_degree= 0 then 'C. Bachelors Degree' \r\n            else ' D. Others' end as education_level,\r\n       avg(totalyearlycompensation) as average_compensation\r\nFROM   salaries_data\r\nGROUP BY 1;"}, {"question": "What are the total number of companies in the data ?", "answer": "SELECT count(distinct company) as total_companies\nFROM   salaries_data;"}, {"question": "Which 3 cities have the highest average salary for a software engineers at Google? ", "answer": "SELECT location,\n       avg(totalyearlycompensation) as average_salary\nFROM   salaries_data\nWHERE  company = 'Google'\n   and title like '%Software Engineer%'\nGROUP BY location\nORDER BY average_salary desc limit 3;"}, {"question": "compare the correlation coefficient of yearsofexperience, yearsatcompany with total compensation ", "answer": "SELECT corr(yearsofexperience,\n            totalyearlycompensation) as correlation_experience_compensation,\n       corr(yearsatcompany, totalyearlycompensation) as correlation_years_compensation\nFROM   salaries_data;"}, {"question": "what are different tags in the data ?", "answer": "SELECT DISTINCT tag\r\nFROM   salaries_data;"}, {"question": "what 3 locations have the highest avg yearly compensation at Apple for Software Engineers?", "answer": "SELECT location,\n       avg(totalyearlycompensation) as average_compensation\nFROM   salaries_data\nWHERE  company = 'Apple'\n   and title = 'Software Engineer'\nGROUP BY location\nORDER BY average_compensation desc limit 3;"}, {"question": "what are count of asian vs no asians in the data ", "answer": "SELECT race_asian, COUNT(*) as count_asian\r\nFROM   salaries_data\r\nGROUP By 1\r\n"}, {"question": "Which 7 companies have the highest % of asians  employees, in the top 20 employers by size?", "answer": "SELECT company,\n       count(case when race = 'Asian' then 1\n                  else null end) * 100.0 / count(*) as asian_percentage\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 20)\nGROUP BY company\nORDER BY asian_percentage desc limit 7;"}, {"question": "compare the correlation between years of experience and base salary and years at company and basesalary ", "answer": "SELECT corr(yearsofexperience, basesalary) as correlation_experience_salary,\n       corr(yearsatcompany, basesalary) as correlation_years_salary\nFROM   salaries_data;"}, {"question": "What is the engineer to product manager ratio in Facebook, Amazon and Google?", "answer": "SELECT company,\n       count(case when title like '%Engineer%' then 1\n                  else null end) as engineer_count,\n       count(case when title like '%Product Manager%' then 1\n                  else null end) as product_manager_count,\n       count(case when title like '%Engineer%' then 1\n                  else null end) / count(case when title like '%Product Manager%' then 1\n                  else null end) as ratio\nFROM   salaries_data\nWHERE  company in ('Facebook', 'Amazon', 'Google')\nGROUP BY company;"}, {"question": "Which 4 locations have the most employees ?", "answer": "SELECT location,\r\n       count(*) as employee_count\r\nFROM   salaries_data\r\nGROUP BY location\r\nORDER BY employee_count desc limit 4;"}, {"question": "what 5 companies have the highest average base salaries ?", "answer": "SELECT company,\r\n       avg(basesalary) as average_salary\r\nFROM   salaries_data\r\nGROUP BY company\r\nORDER BY average_salary desc limit 5;"}, {"question": "what are different levels related to software engineering roles at apple ?", "answer": "SELECT DISTINCT level\r\nFROM   salaries_data\r\nWHERE  company = 'Apple'\r\n   and title ilike '%Software%';"}, {"question": "Which is the most common level of software engineers at amazon ", "answer": "SELECT level,\n       count(*) as count_employees\nFROM   salaries_data\nWHERE  company = 'Amazon'\n   and title like '%Software Engineer%'\nGROUP BY level\nORDER BY count_employees desc limit 1;"}, {"question": "Which 3 cities have the highest average salary for software engineers at Google ? ", "answer": "SELECT location,\n       avg(totalyearlycompensation) as average_salary\nFROM   salaries_data\nWHERE  company = 'Google'\n   and title like '%Software Engineer%'\nGROUP BY location\nORDER BY average_salary desc limit 3;"}, {"question": "what are top 4 biggest employers ? ", "answer": "SELECT company,\n       count(*) as employee_count\nFROM   salaries_data\nGROUP BY company\nORDER BY employee_count desc limit 4;"}, {"question": "Which 5 company has the highest avg stock value grants among the top 25 employers? Load suitable bar chart", "answer": "SELECT company,\n       avg(stockgrantvalue) as average_stock_value\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 25)\nGROUP BY company\nORDER BY average_stock_value desc limit 5;"}, {"question": "How much do employees with a bachelor's, master's, and doctorate earn on average respectively? ", "answer": "SELECT case when doctorate_degree = 1 then 'Doctorate Degree'\r\n            when masters_degree = 1 then 'Master''s Degree'\r\n            when bachelors_degree = 1 then 'Bachelor''s Degree'\r\n            else 'Other' end as education_level,\r\n       avg(totalyearlycompensation) as average_compensation\r\nFROM   salaries_data\r\nGROUP BY education_level;"}, {"question": "Which is the top 4 most common level of software engineers and their median salary at amazon ?", "answer": "SELECT level,\n       COUNT(*),\n       median(totalyearlycompensation) as median_salary\nFROM   salaries_data\nWHERE level in  \n   (SELECT level\n    FROM salaries_data\n    WHERE company = 'Amazon'\n    GROUP BY level\n    ORDER BY COUNT(*) DESC\n    LIMIT 4)\n   and title like '%Software Engineer%'\nGROUP BY level\nORDER BY median_salary desc\nLIMIT 4;"}, {"question": "What is average total years of experience related to the 5 most popular titles?", "answer": "SELECT title,\n       avg(yearsofexperience) as average_years_of_experience\nFROM   salaries_data\nWHERE  title in (SELECT title\n                 FROM   (SELECT title,\n                                count(*) as title_count\n                         FROM   salaries_data\n                         GROUP BY title\n                         ORDER BY title_count desc limit 5))\nGROUP BY title;"}, {"question": "what are count of masters degree holders v/s non masters ", "answer": "SELECT masters_degree,\r\n       count(*) as count_education\r\nFROM   salaries_data\r\nGROUP BY 1;"}, {"question": "What is  the correlation coefficient of yearsofexperience, yearsatcompany with total compensation ", "answer": "SELECT corr(yearsofexperience,\n            totalyearlycompensation) as correlation_experience_compensation,\n       corr(yearsatcompany, totalyearlycompensation) as correlation_years_compensation\nFROM   salaries_data;"}, {"question": "what are count of hispanic and non hispanic people in the data ", "answer": "SELECT race_hispanic,\n       count(*) as count_hispanic\nFROM   salaries_data\nGROUP BY 1;"}, {"question": "which 7 companies have the highest % of asians  employees, in the top 5 employers by size ?", "answer": "SELECT company,\n       count(case when race = 'Asian' then 1\n                  else null end) * 100.0 / count(*) as asian_percentage\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 5)\nGROUP BY company\nORDER BY asian_percentage desc limit 7;"}, {"question": "compare the pearson's correlation between yearsofexperience and basesalary and yearsatcompany and basesalary ", "answer": "SELECT corr(yearsofexperience, basesalary) as correlation_experience_salary,\n       corr(yearsatcompany, basesalary) as correlation_years_salary\nFROM   salaries_data;"}, {"question": "what 3 locations have the highest avg yearly compensation at Apple for Software Engineers ?", "answer": "SELECT location,\n       avg(totalyearlycompensation) as average_compensation\nFROM   salaries_data\nWHERE  company = 'Apple'\n   and title = 'Software Engineer'\nGROUP BY location\nORDER BY average_compensation desc limit 3;"}, {"question": "what location has the highest avg yearly compensation at Apple for Software Engineers ?", "answer": "SELECT location,\n       avg(totalyearlycompensation) as average_compensation\nFROM   salaries_data\nWHERE  company = 'Apple'\n   and title = 'Software Engineer'\nGROUP BY location\nORDER BY average_compensation desc limit 1;"}, {"question": "What is the avg compensation of employees basis their race ? ", "answer": "SELECT race,\n       avg(totalyearlycompensation) as average_compensation\nFROM   salaries_data\nGROUP BY race;"}, {"question": "What 5 companies have the highest average total compensation salaries ?", "answer": "SELECT company,\n       avg(totalyearlycompensation) as average_compensation\nFROM   salaries_data\nGROUP BY company\nORDER BY average_compensation desc limit 5;"}, {"question": "what 3 most compensated employees in the data, what are their roles, companies, and locations  ?", "answer": "SELECT \r\n       title,\r\n       company,\r\n       location,\r\n       totalyearlycompensation\r\nFROM   salaries_data\r\nORDER BY totalyearlycompensation desc limit 3;"}, {"question": "Which company ahs the highest stock value grants among the top 25 employers ?", "answer": "SELECT company,\r\n       avg(stockgrantvalue) as total_stock_value\r\nFROM   salaries_data\r\nWHERE  company in (SELECT company\r\n                   FROM   salaries_data\r\n                   GROUP BY company\r\n                   ORDER BY count(*) desc limit 25)\r\nGROUP BY company\r\nORDER BY total_stock_value desc limit 1;"}, {"question": "What is the % of employees having a master's degree basis their race ? ", "answer": "SELECT race,\r\n       count(case when masters_degree = 1 then 1\r\n                  else null end) * 100.0 / count(*) as percentage_masters\r\nFROM   salaries_data\r\nGROUP BY race;"}, {"question": "what 3 most compensated title related to software engineering at Google ?", "answer": "SELECT title,\n       max(totalyearlycompensation) as max_compensation\nFROM   salaries_data\nWHERE  company = 'Google'\n   and title like '%Software Engineer%'\nGROUP BY title\nORDER BY max_compensation desc limit 3;"}, {"question": "what are different levels related to software engineering roles at apple, along with number of employees  ?", "answer": "SELECT level,\r\n       count(*) as count_employees\r\nFROM   salaries_data\r\nWHERE  company = 'Apple'\r\n   and title like '%Software Engineer%'\r\nGROUP BY level\r\nORDER BY count_employees DESC;"}, {"question": "which 7 companies have the highest % of asians  employees, in the top 20 employers by size ?", "answer": "SELECT company,\n       count(case when race = 'Asian' then 1\n                  else null end) * 100.0 / count(*) as asian_percentage\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 20)\nGROUP BY company\nORDER BY asian_percentage desc limit 7;"}, {"question": "Which 5 company has the highest avg stock value grants among the top 25 employers ?", "answer": "SELECT company,\n       avg(stockgrantvalue) as average_stock_value\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 25)\nGROUP BY company\nORDER BY average_stock_value desc limit 5;"}, {"question": "which 2 companies have the highest % of asians  employees, in the top 5 employers by size ?", "answer": "SELECT company,\n       count(case when race = 'Asian' then 1\n                  else null end) * 100.0 / count(*) as asian_percentage\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 5)\nGROUP BY company\nORDER BY asian_percentage desc limit 2;"}, {"question": "What is average total years of experience related to the 5 most popular job titles  ?", "answer": "SELECT title,\n       avg(yearsofexperience) as average_years_of_experience\nFROM   salaries_data\nWHERE  title in (SELECT title\n                 FROM   (SELECT title,\n                                count(*) as title_count\n                         FROM   salaries_data\n                         GROUP BY title\n                         ORDER BY title_count desc limit 5))\nGROUP BY title;"}, {"question": "Which location have the most employees ?", "answer": "SELECT location,\n       count(*) as employee_count\nFROM   salaries_data\nGROUP BY location\nORDER BY employee_count desc limit 1;"}, {"question": "What is average total years of experience related to the 5 most popular job titles ?", "answer": "SELECT title,\r\n       ROUND(avg(yearsofexperience),2) as average_years_of_experience\r\nFROM   salaries_data\r\nWHERE  title in (SELECT title\r\n                 FROM   (SELECT title,\r\n                                count(*) as title_count\r\n                         FROM   salaries_data\r\n                         GROUP BY title\r\n                         ORDER BY title_count desc limit 5))\r\nGROUP BY title;"}, {"question": "What is the average salary of employees?", "answer": "SELECT AVG(salary) FROM employees"}, {"question": "Which 3 cities have the highest average salary for a software engineers at Google ? ", "answer": "SELECT location,\n       avg(totalyearlycompensation) as average_salary\nFROM   salaries_data\nWHERE  company = 'Google'\n   and title like '%Software Engineer%'\nGROUP BY location\nORDER BY average_salary desc limit 3;"}, {"question": "What is the engineer to product manager ratio in FAANG companies?", "answer": "SELECT company,\n       count(case when title like '%Engineer%' then 1\n                  else null end) / count(case when title like '%Product Manager%' then 1\n                  else null end) as ratio\nFROM   salaries_data\nWHERE  company in ('Facebook', 'Amazon', 'Apple', 'Netflix', 'Google')\nGROUP BY company;"}, {"question": "what are different titles in the data ?", "answer": "SELECT DISTINCT title\r\nFROM   salaries_data;"}, {"question": "What is average total years of experience related to the most popular titles ?", "answer": "SELECT title,\r\n       avg(yearsofexperience) as average_years_of_experience\r\nFROM   salaries_data\r\nWHERE title in \r\n( SELECT title FROM\r\n   (\r\n      SELECT title, COUNT(*)\r\n      FROM salaries_data\r\n      GROUP BY 1 \r\n      ORDER BY 2 DESC \r\n      LIMIT 5\r\n   )\r\n)\r\nGROUP BY title;"}, {"question": "what 5 companies have the highest total yearly compensation ? ", "answer": "SELECT company,\r\n       avg(TOTALYEARLYCOMPENSATION) as total_compensation\r\nFROM   salaries_data\r\nGROUP BY company\r\nORDER BY total_compensation desc limit 5;"}, {"question": "What 5 companies have the most analysts in the top 50 employers ?", "answer": "SELECT company,\n       count(*) as analyst_count\nFROM   salaries_data\nWHERE  title like '%Analyst%'\n   and company in (SELECT company\n                FROM   salaries_data\n                GROUP BY company\n                ORDER BY count(*) desc limit 50)\nGROUP BY company\nORDER BY analyst_count desc limit 5;"}, {"question": "What is the engineer to product manager ratio in Facebook, Amazon and Google ? ", "answer": "SELECT company,\r\n       count(case when title like '%Engineer%' then 1\r\n                  else null end) /\r\n       count(case when title like '%Product Manager%' then 1\r\n                  else null end) as ratio\r\nFROM   salaries_data\r\nWHERE  company in ('Facebook', 'Amazon', 'Google')\r\nGROUP BY company;"}, {"question": "Which city has the highest average salary for software engineers at Google ? ", "answer": "SELECT location,\n       avg(totalyearlycompensation) as average_salary\nFROM   salaries_data\nWHERE  company = 'Google'\n   and title like '%Software Engineer%'\nGROUP BY location\nORDER BY average_salary desc limit 1;"}, {"question": "Which 5 company has the highest avg stock value grants among the top 40 employers? Load suitable bar chart", "answer": "SELECT company,\n       avg(stockgrantvalue) as average_stock_value\nFROM   salaries_data\nWHERE  company in (SELECT company\n                   FROM   salaries_data\n                   GROUP BY company\n                   ORDER BY count(*) desc limit 40)\nGROUP BY company\nORDER BY average_stock_value desc limit 5;"}]