[{"question": "Compare the mean monthly federal funds effective rate to the monthly inflation rate expressed as a percentage based on the consumer price index for urban consumers since 1970", "answer": "with cpi_data as (SELECT date,\n                         value,\n                         lag(value) OVER (ORDER BY date) as prev_value\n                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\n                  WHERE  variable_name ilike '%Consumer Price Index%Urban Consumers%'\n                     and date >= '1970-01-01'), inflation_rate as (SELECT date,\n                                                     (value - prev_value) / prev_value * 100 as monthly_inflation_rate\n                                              FROM   cpi_data\n                                              WHERE  prev_value is not null), mean_federal_funds_rate as (SELECT date_trunc('month', date) as month,\n                                                                   avg(value) as mean_federal_funds_rate\n                                                            FROM   financial_data_package.cybersyn.financial_fred_timeseries\n                                                            WHERE  variable_name = 'Federal Funds Effective Rate'\n                                                               and date >= '1970-01-01'\n                                                            GROUP BY month)\nSELECT inflation_rate.date,\n       inflation_rate.monthly_inflation_rate,\n       mean_federal_funds_rate.mean_federal_funds_rate\nFROM   inflation_rate join mean_federal_funds_rate\n        ON inflation_rate.date = mean_federal_funds_rate.month\nORDER BY inflation_rate.date;"}, {"question": "What are the bank branches in the zip code with the most number of bank branches, excluding none values?", "answer": "SELECT *\r\nFROM   financial_data_package.cybersyn.financial_branch_entities\r\nWHERE  id_zip = (SELECT id_zip\r\n                 FROM   financial_data_package.cybersyn.financial_branch_entities\r\n                 WHERE  end_date is null\r\n                    and id_zip is not null\r\n                 GROUP BY id_zip\r\n                 ORDER BY count(*) desc limit 1) and end_date is null and id_zip is not null;"}, {"question": "Since 1990, how many bank branches are there annually?", "answer": "SELECT count(*) as n_branches,\n       date_trunc('YEAR', start_date) as start_year\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  start_date >= '1990-01-01'\nGROUP BY start_year\nORDER BY start_year asc"}, {"question": "Calculate the annualized quarterly inflation rate, as a percentage, based on the consumer price index for urban consumers since 1970", "answer": "with cpi_data as (SELECT date,\n                         value,\n                         lag(value) OVER (ORDER BY date) as prev_value\n                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\n                  WHERE  variable_name ilike '%Consumer Price Index%Urban Consumers%'\n                     and date >= '1970-01-01'), quarterly_inflation as (SELECT date,\n                                                          value,\n                                                          prev_value,\n                                                          extract(year\n                                                   FROM   date) as year, extract(quarter\n                                                   FROM   date) as quarter\n                                                   FROM   cpi_data\n                                                   WHERE  prev_value is not null), quarterly_inflation_rate as (SELECT year,\n                                                                    quarter,\n                                                                    (power((value / prev_value), 1.0/3) - 1) * 100 as quarterly_inflation_rate\n                                                             FROM   quarterly_inflation)\nSELECT year,\n       quarter,\n       quarterly_inflation_rate\nFROM   quarterly_inflation_rate\nORDER BY year, quarter;"}, {"question": "Which are the top 10 banks by the number of branches open since 1980?", "answer": "with branch_counts as (SELECT id_rssd_parent,\n                              count(*) as branch_count\n                       FROM   financial_data_package.cybersyn.financial_branch_entities\n                       WHERE  start_date >= '1980-01-01'\n                       GROUP BY id_rssd_parent)\nSELECT name,\n       branch_count\nFROM   branch_counts join financial_data_package.cybersyn.financial_institution_entities\n        ON financial_institution_entities.id_rssd = branch_counts.id_rssd_parent\nORDER BY branch_count desc limit 10;"}, {"question": "What are the first 10 rows in the FINANCIAL_INSTITUTION_ATTRIBUTES table?", "answer": "SELECT VARIABLE, VARI<PERSON><PERSON>_NAME, <PERSON>FINITION, FRE<PERSON><PERSON><PERSON><PERSON>, UNIT\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_INSTITUTION_ATTRIBUTES\r\nLIMIT 10"}, {"question": "How many CFPB complaints were made each year?", "answer": "SELECT date_trunc('YEAR', date_received) as year,\n       count(*) as count\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nGROUP BY year\nORDER BY year asc"}, {"question": "Are the differences in the states' mean monthly not seasonally adjusted unemployment rates in 2008-2010 versus 2020-2022 statistically significant?", "answer": "with unemployment_rate_2008_2010 as (SELECT geo_name,\n                                            avg(value) as avg_unemployment_rate_2008_2010\n                                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\n                                             ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\n                                     WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n                                        and date between to_date('2008-01-01')\n                                        and to_date('2010-12-31')\n                                        and level = 'State'\n                                     GROUP BY geo_name), unemployment_rate_2020_2022 as (SELECT geo_name,\n                                                           avg(value) as avg_unemployment_rate_2020_2022\n                                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\n                                                            ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\n                                                    WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n                                                       and date between to_date('2020-01-01')\n                                                       and to_date('2022-12-31')\n                                                       and level = 'State'\n                                                    GROUP BY geo_name)\nSELECT avg(difference) as avg_difference,\n       stddev_pop(difference) / sqrt(count(*)) as standard_error,\n       avg(difference) / (stddev_pop(difference) / sqrt(count(*))) as t_statistic,\n       count(*) as num_states\nFROM   (SELECT u1.geo_name,\n               u1.avg_unemployment_rate_2008_2010,\n               u2.avg_unemployment_rate_2020_2022,\n               u2.avg_unemployment_rate_2020_2022 - u1.avg_unemployment_rate_2008_2010 as difference\n        FROM   unemployment_rate_2008_2010 u1 join unemployment_rate_2020_2022 u2\n                ON u1.geo_name = u2.geo_name) as differences\nWHERE  not difference is null;"}, {"question": "What are the names of the countries in the European region?", "answer": "SELECT n.n_name AS country\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\nWHERE r.r_name = 'EUROPE'"}, {"question": "Which are the states with the highest mean monthly not seasonally adjusted unemployment rates in 2008-2010 and 2020-2022?", "answer": "SELECT geo_name,\n       avg(value) as avg_unemployment_rate\nFROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\n        ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\nWHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n   and date between to_date('2008-01-01')\n   and to_date('2010-12-31')\n    or date between to_date('2020-01-01')\n   and to_date('2022-12-31')\n   and level = 'State'\nGROUP BY geo_name having count(distinct case when date between to_date('2008-01-01') and to_date('2010-12-31') then 1 end) = 36 and count(distinct case when date between to_date('2020-01-01') and to_date('2022-12-31') then 1 end) = 36\nORDER BY avg_unemployment_rate desc;"}, {"question": "What is the number of customers in each region?", "answer": "SELECT r.r_name AS region, COUNT(DISTINCT c.c_custkey) AS number_of_customers\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\nGROUP BY region"}, {"question": "Calculate the monthly inflation rate as a percentage from the consumer price index for urban consumers since 1965", "answer": "with cpi_data as (SELECT date,\r\n                         value,\r\n                         lag(value) OVER (ORDER BY date) as prev_value\r\n                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\r\n                  WHERE  variable_name ilike '%Consumer Price Index%Urban Consumers%'\r\n                     and date >= '1965-01-01')\r\nSELECT date,\r\n       value as cpi,\r\n       (value - prev_value) / prev_value * 100 as monthly_inflation_rate\r\nFROM   cpi_data\r\nWHERE  prev_value is not null\r\nORDER BY date;"}, {"question": "Count credit card complaints by month by company since 2012", "answer": "SELECT company,\r\n       DATE_TRUNC('month', date_received) AS month,\r\n       COUNT(1)                           AS credit_card_complaint\r\nFROM financial_data_package.cybersyn.financial_cfpb_complaint\r\nWHERE product ILIKE '%card%'\r\n  AND date_received >= '2012-01-01'\r\nGROUP BY company, month"}, {"question": "How many new branches were opened by which banks in 2022", "answer": "SELECT id_rssd_parent,\n       count(*)\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  start_date >= '2022-01-01'\nGROUP BY id_rssd_parent\nORDER BY count(*) desc;"}, {"question": "What are the names of the top six customers with the highest total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 6"}, {"question": "Compare the states with the mean monthly not seasonally adjusted unemployment rates in 2008-2010 versus 2020-2022?", "answer": "with unemployment_rate_2008_2010 as (SELECT geo_name,\r\n                                            avg(value) as avg_unemployment_rate\r\n                                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\r\n                                             ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\r\n                                     WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n                                        and date between to_date('2008-01-01')\r\n                                        and to_date('2010-12-31')\r\n                                        and level = 'State'\r\n                                     GROUP BY geo_name), unemployment_rate_2020_2022 as (SELECT geo_name,\r\n                                                           avg(value) as avg_unemployment_rate\r\n                                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\r\n                                                            ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\r\n                                                    WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n                                                       and date between to_date('2020-01-01')\r\n                                                       and to_date('2022-12-31')\r\n                                                       and level = 'State'\r\n                                                    GROUP BY geo_name)\r\nSELECT a.geo_name,\r\n       a.avg_unemployment_rate as avg_unemployment_rate_2008_2010,\r\n       b.avg_unemployment_rate as avg_unemployment_rate_2020_2022\r\nFROM   unemployment_rate_2008_2010 a join unemployment_rate_2020_2022 b\r\n        ON a.geo_name = b.geo_name\r\nORDER BY a.geo_name;"}, {"question": "Which banks have the most uninsured deposits?", "answer": "with latest_uninsured as (SELECT id_rssd,\n                                 1 - value as pct_uninsured\n                          FROM   financial_data_package.cybersyn.financial_institution_timeseries\n                          WHERE  variable = 'ASSET'\n                             and date = '2022-12-31')\nSELECT name,\n       pct_uninsured,\n       ent.is_active\nFROM   financial_data_package.cybersyn.financial_institution_timeseries as ts\n    INNER JOIN financial_data_package.cybersyn.financial_institution_attributes as att\n        ON (ts.variable = att.variable)\n    INNER JOIN financial_data_package.cybersyn.financial_institution_entities as ent\n        ON (ts.id_rssd = ent.id_rssd)\n    INNER JOIN latest_uninsured\n        ON (latest_uninsured.id_rssd = ts.id_rssd)\nWHERE  ts.date = '2022-12-31'\n   and att.variable_name = '% Insured (Estimated)'\n   and att.frequency = 'Quarterly'\n   and ent.is_active = true\nORDER BY pct_uninsured desc limit 10;"}, {"question": "How many CFPB complaints of each product were made each year?", "answer": "SELECT date_trunc('YEAR', date_received) as year,\n       product,\n       count(*) as count\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nGROUP BY year, product\nORDER BY year asc, count desc"}, {"question": "Sample 10 states in the USA and compare their mean monthly not seasonally adjusted unemployment rates in 2008-2010 versus 2020-2022", "answer": "with unemployment_rate_2008_2010 as (SELECT geo_name,\r\n                                            avg(value) as avg_unemployment_rate\r\n                                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\r\n                                             ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\r\n                                     WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n                                        and date between '2008-01-01'\r\n                                        and '2010-12-31'\r\n                                        and level = 'State'\r\n                                     GROUP BY geo_name), unemployment_rate_2020_2022 as (SELECT geo_name,\r\n                                                           avg(value) as avg_unemployment_rate\r\n                                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\r\n                                                            ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\r\n                                                    WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n                                                       and date between '2020-01-01'\r\n                                                       and '2022-12-31'\r\n                                                       and level = 'State'\r\n                                                    GROUP BY geo_name), sample_states as (SELECT geo_name\r\n                                      FROM   financial_data_package.cybersyn.geography_index sample (10)\r\n                                      WHERE  level = 'State')\r\n-- 10% random sample of states\r\nSELECT sample_states.geo_name,\r\n       u1.avg_unemployment_rate as avg_unemployment_rate_2008_2010,\r\n       u2.avg_unemployment_rate as avg_unemployment_rate_2020_2022\r\nFROM   sample_states\r\n    LEFT JOIN unemployment_rate_2008_2010 u1\r\n        ON sample_states.geo_name = u1.geo_name\r\n    LEFT JOIN unemployment_rate_2020_2022 u2\r\n        ON sample_states.geo_name = u2.geo_name\r\nORDER BY sample_states.geo_name;"}, {"question": "Compare the federal funds effective rate, as a percentage to the annualized monthly inflation rate expressed as a percentage based on the consumer price index for urban consumers since 1970", "answer": "with cpi_data as (SELECT date,\r\n                         value,\r\n                         lag(value) OVER (ORDER BY date) as prev_value\r\n                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\r\n                  WHERE  variable_name ilike '%Consumer Price Index%Urban Consumers%'\r\n                     and date >= '1970-01-01'), inflation_rate as (SELECT date,\r\n                                                     (value - prev_value) / prev_value * 100 as monthly_inflation_rate\r\n                                              FROM   cpi_data\r\n                                              WHERE  prev_value is not null), federal_funds_rate as (SELECT date,\r\n                                                              value as federal_funds_rate\r\n                                                       FROM   financial_data_package.cybersyn.financial_fred_timeseries\r\n                                                       WHERE  variable_name = 'Federal Funds Effective Rate'\r\n                                                          and date >= '1970-01-01')\r\nSELECT inflation_rate.date,\r\n       100*federal_funds_rate.federal_funds_rate as federal_funds_rate,\r\n       (power((1 + inflation_rate.monthly_inflation_rate / 100),\r\n              12) - 1) * 100 as annualized_inflation_rate\r\nFROM   inflation_rate join federal_funds_rate\r\n        ON inflation_rate.date = federal_funds_rate.date\r\nORDER BY inflation_rate.date;"}, {"question": "How many complaints were submitted via each method?", "answer": "SELECT submitted_via,\n       count(*) as count\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nGROUP BY submitted_via\nORDER BY count desc"}, {"question": "Among the top 10 companies in terms of the total number of complaints received between 2018 and 2022 by CFPB, what is the percentage breakdown of the various companies' responses to customers in each year?", "answer": "The corrected SQL code is:\n\n```\nWITH top_10_companies AS (\n    SELECT company,\n           COUNT(*) AS complaints_count\n    FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n    WHERE  date_received BETWEEN '2018-01-01' AND '2022-12-31'\n    GROUP BY company\n    ORDER BY complaints_count DESC LIMIT 10\n), \ncompany_responses AS (\n    SELECT company,\n           company_response_to_consumer,\n           DATE_TRUNC('YEAR', date_received) AS year,\n           COUNT(*) AS response_count\n    FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n    WHERE  date_received BETWEEN '2018-01-01' AND '2022-12-31' AND company IN (SELECT company FROM top_10_companies)\n    GROUP BY company, company_response_to_consumer, year, company, company_response_to_consumer, year\n),\nresponse_count AS (\n    SELECT company,\n           company_response_to_consumer,\n           year,\n           SUM(response_count) AS response_count\n    FROM   company_responses\n    GROUP BY company, company_response_to_consumer, year\n)\nSELECT company,\n       company_response_to_consumer,\n       year,\n       response_count * 100.0 / SUM(response_count) OVER (PARTITION BY company, year) AS percentage\nFROM   response_count\nORDER BY company, year, percentage DESC\n``` \n\nExplanation:\n\nThe issue with the initial query is that the `response_count` column was not included in the `GROUP BY` clause of the `company_responses` subquery. To correct this, we can add `response_count` to the `GROUP BY` clause of the `company_responses` subquery.\n\nHowever, this will cause an error in the final `SELECT` statement, since `response_count` is not a valid column to group by. To fix this, we can create a new subquery called `response_count` that groups by `company`, `company_response_to_consumer`, and `year`, and sums up the `response_count` column. We can then use this subquery in the final `SELECT` statement instead of `company_responses`."}, {"question": "Which are the five biggest banks by deposits", "answer": "with latest_deposits as (SELECT id_rssd,\n                                value\n                         FROM   financial_data_package.cybersyn.financial_institution_timeseries\n                         WHERE  variable = 'DEP'\n                            and date = '2022-12-31'), active_banks as (SELECT *\n                                           FROM   financial_data_package.cybersyn.financial_institution_entities\n                                           WHERE  is_active = true)\nSELECT name,\n       value\nFROM   active_banks\n    INNER JOIN latest_deposits\n        ON (latest_deposits.id_rssd = active_banks.id_rssd)\nORDER BY value desc limit 5;"}, {"question": "Which are the states with the highest number of branch closures in 2022?", "answer": "SELECT state_abbreviation,\n       count(*) as n_closures\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  end_date between '2022-01-01'\n   and '2022-12-31'\nGROUP BY state_abbreviation\nORDER BY n_closures desc"}, {"question": "Since 2020, which banks opened at least 3 new branches in states they were new to, and how many new states were there for each bank?  ", "answer": "with new_branches as (SELECT id_rssd_parent,\n                             state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY id_rssd_parent, state_abbreviation), pre2020_branches as (SELECT id_rssd_parent,\n                                                                          state_abbreviation\n                                                                   FROM   financial_data_package.cybersyn.financial_branch_entities\n                                                                   WHERE  start_date < '2020-01-01'\n                                                                      and end_date is null\n                                                                      and state_abbreviation is not null\n                                                                   GROUP BY id_rssd_parent, state_abbreviation)\nSELECT bank_names.name,\n       count(*) as new_state_count\nFROM   new_branches\n    INNER JOIN financial_data_package.cybersyn.financial_institution_entities as bank_names\n        ON (new_branches.id_rssd_parent = bank_names.id_rssd)\n    LEFT JOIN pre2020_branches\n        ON (new_branches.id_rssd_parent = pre2020_branches.id_rssd_parent and\n           new_branches.state_abbreviation = pre2020_branches.state_abbreviation)\nWHERE  pre2020_branches.id_rssd_parent is null\nGROUP BY bank_names.name having count(*) >= 3\nORDER BY new_state_count desc;"}, {"question": "Calculate the annualized monthly inflation rate as a percentage from the consumer price index for urban consumers since 1965", "answer": "WITH cpi_data AS (\r\n  SELECT date,\r\n         value,\r\n         LAG(value) OVER (ORDER BY date) AS prev_value\r\n  FROM financial_data_package.cybersyn.financial_fred_timeseries\r\n  WHERE variable_name ILIKE '%Consumer Price Index%Urban Consumers%'\r\n    AND date >= '1965-01-01'\r\n),\r\n\r\nsubquery AS (\r\n  SELECT date,\r\n         value,\r\n         LAG(value) OVER (ORDER BY date) AS prev_value,\r\n         LAG(date) OVER (ORDER BY date) AS prev_date\r\n  FROM cpi_data\r\n)\r\n\r\nSELECT date,\r\n       (POWER((value / prev_value), 1.0/12) - 1) * 100 AS annualized_inflation_rate\r\nFROM subquery\r\nWHERE prev_value IS NOT NULL\r\nORDER BY date;"}, {"question": "Create a cumulative frequency distribution of the number of complaints received by CFPB against Equifax in 2022", "answer": "with equifax_2022_complaints as (SELECT count(*) as n_complaints\n                                 FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                 WHERE  company = 'Equifax'\n                                    and date_received between '2022-01-01'\n                                    and '2022-12-31')\nSELECT n_complaints,\n       count(*) as frequency,\n       sum(count(*)) OVER (ORDER BY n_complaints asc) as cumulative_frequency\nFROM   equifax_2022_complaints\nGROUP BY n_complaints\nORDER BY n_complaints asc"}, {"question": "How many of the new branches that were opened since 2020 are from banks new to the state? ", "answer": "with new_branches as (SELECT id_rssd_parent,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY id_rssd_parent), bank_names as (SELECT id_rssd,\n                                                name\n                                         FROM   financial_data_package.cybersyn.financial_institution_entities), pre2020_branches as (SELECT id_rssd_parent,\n                                                                                                    state_abbreviation\n                                                                                             FROM   financial_data_package.cybersyn.financial_branch_entities\n                                                                                             WHERE  start_date < '2020-01-01'\n                                                                                                and end_date is null\n                                                                                                and state_abbreviation is not null\n                                                                                             GROUP BY id_rssd_parent, state_abbreviation)\nSELECT name,\n       new_branch_count\nFROM   bank_names\n    INNER JOIN new_branches\n        ON (bank_names.id_rssd = new_branches.id_rssd_parent)\n    LEFT JOIN pre2020_branches\n        ON (new_branches.id_rssd_parent = pre2020_branches.id_rssd_parent)\nWHERE  pre2020_branches.id_rssd_parent is null\nORDER BY new_branch_count desc;"}, {"question": "On average how many CFBP complaints are there by company?", "answer": "SELECT company,\n       count(*) as complaints_count\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nGROUP BY company\nORDER BY complaints_count desc"}, {"question": "What are the states with the highest mean monthly not seasonally adjusted unemployment rates since 2020?", "answer": "SELECT geo_name,\r\n       avg(value) as avg_unemployment_rate\r\nFROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\r\n        ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\r\nWHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n   and date >= to_date('2020-01-01')\r\n   and level = 'State'\r\nGROUP BY geo_name\r\nORDER BY avg_unemployment_rate desc"}, {"question": "In 2021, how many new branches have banks opened by state?", "answer": "with new_branches as (SELECT state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2021-01-01'\n                      GROUP BY state_abbreviation)\nSELECT state_abbreviation,\n       new_branch_count\nFROM   new_branches\nORDER BY new_branch_count desc;"}, {"question": "Among the top 10 most complained companies between 2017 and 2022 by CFPB, what are the percentages of each company's various responses to customers?", "answer": "with top_10_companies as (SELECT company,\n                                 count(*) as complaints_count\n                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                          WHERE  date_received between '2017-01-01'\n                             and '2022-12-31'\n                          GROUP BY company\n                          ORDER BY complaints_count desc limit 10), company_responses as (SELECT company,\n                                                                       company_response_to_consumer,\n                                                                       count(*) as response_count\n                                                                FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                                WHERE  date_received between '2017-01-01'\n                                                                   and '2022-12-31'\n                                                                   and company in (SELECT company\n                                                                                FROM   top_10_companies)\n                                                                GROUP BY company, company_response_to_consumer)\nSELECT company,\n       company_response_to_consumer,\n       response_count,\n       response_count * 100.0 / sum(response_count) OVER (PARTITION BY company) as percentage\nFROM   company_responses\nORDER BY company, percentage desc"}, {"question": "What is the 3 month running average of each states' mean monthly not seasonally adjusted unemployment rates, expressed in percentage, between 2007 to 2022?", "answer": "with monthly_unemployment_rates as (SELECT g.geo_name,\n                                           b.date,\n                                           avg(b.value) as avg_unemployment_rate\n                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries as b join financial_data_package.cybersyn.geography_index as g\n                                            ON b.geo_id = g.geo_id\n                                    WHERE  b.variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n                                       and b.date between '2007-01-01'\n                                       and '2022-12-31'\n                                       and g.level = 'State'\n                                    GROUP BY g.geo_name, b.date)\nSELECT geo_name,\n       date,\n       avg(avg_unemployment_rate) OVER (PARTITION BY geo_name\n                                        ORDER BY date rows between 1 preceding and 1 following) as three_month_running_avg,\n       100 * avg(avg_unemployment_rate) OVER (PARTITION BY geo_name\n                                              ORDER BY date rows between 1 preceding and 1 following) / avg(avg_unemployment_rate) OVER (PARTITION BY geo_name) - 100 as three_month_running_avg_pct_change\nFROM   monthly_unemployment_rates\nORDER BY geo_name, date;"}, {"question": "Compare the seasonally adjusted Consumer Price Index for all Urban Consumers to the Federal Funds Effective Rate on the last day of each month ", "answer": "with cpi as (SELECT date_trunc('month', date) as month,\n                    last_value(value) OVER (ORDER BY date) as cpi\n             FROM   financial_data_package.cybersyn.financial_fred_timeseries\n             WHERE  variable like '%Consumer Price Index%Urban Consumers%Seasonally adjusted%'\n             ORDER BY month), federal_funds_effective_rate as (SELECT date_trunc('month', date) as month,\n                                                         last_value(value) OVER (ORDER BY date) as rate\n                                                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\n                                                  WHERE  variable = 'Federal Funds Effective Rate'\n                                                  ORDER BY month)\nSELECT cpi.month,\n       cpi.cpi,\n       federal_funds_effective_rate.rate\nFROM   cpi\n    INNER JOIN federal_funds_effective_rate\n        ON (cpi.month = federal_funds_effective_rate.month);"}, {"question": "What is the count of line items purchased with each discount rate in each region?", "answer": "SELECT\n  r.r_name AS region_name,\n  l.l_discount AS discount,\n  COUNT(*) AS count\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nGROUP BY\n  region_name,\n  discount\nORDER BY\n  region_name,\n  discount"}, {"question": "Since 1990, how many bank branches are opened and closed annually?", "answer": "with branches_opened as (SELECT count(*) as n_branches_opened,\n                                date_trunc('YEAR', start_date) as start_year\n                         FROM   financial_data_package.cybersyn.financial_branch_entities\n                         WHERE  start_date >= '1990-01-01'\n                         GROUP BY start_year), branches_closed as (SELECT count(*) as n_branches_closed,\n                                                 date_trunc('YEAR', end_date) as end_year\n                                          FROM   financial_data_package.cybersyn.financial_branch_entities\n                                          WHERE  end_date >= '1990-01-01'\n                                          GROUP BY end_year)\nSELECT coalesce(branches_opened.start_year, branches_closed.end_year) as year,\n       branches_opened.n_branches_opened,\n       branches_closed.n_branches_closed\nFROM   branches_opened full\n    OUTER JOIN branches_closed\n        ON branches_opened.start_year = branches_closed.end_year\nORDER BY year asc"}, {"question": "Among the top 10 companies in terms of the total number of complaints received between 2017 and 2022 by CFPB, what are the percentages of each company's various responses to customers?", "answer": "with top_10_companies as (SELECT company,\n                                 count(*) as complaints_count\n                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                          WHERE  date_received between '2017-01-01'\n                             and '2022-12-31'\n                          GROUP BY company\n                          ORDER BY complaints_count desc limit 10), company_responses as (SELECT company,\n                                                                       company_response_to_consumer,\n                                                                       count(*) as response_count\n                                                                FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                                WHERE  date_received between '2017-01-01'\n                                                                   and '2022-12-31'\n                                                                   and company in (SELECT company\n                                                                                FROM   top_10_companies)\n                                                                GROUP BY company, company_response_to_consumer)\nSELECT company,\n       company_response_to_consumer,\n       response_count,\n       response_count * 100.0 / sum(response_count) OVER (PARTITION BY company) as percentage\nFROM   company_responses\nORDER BY company, percentage desc"}, {"question": "Compare the seasonally adjusted Consumer Price Index for Urban Consumers, Bank Prime Loan Rate, and the Federal Funds Effective Rate", "answer": "SELECT a.date,\n       a.value as bank_prime_loan_rate,\n       b.value as federal_funds_effective_rate,\n       c.value as consumer_price_index\nFROM   financial_data_package.cybersyn.financial_fred_timeseries a join financial_data_package.cybersyn.financial_fred_timeseries b\n        ON a.date = b.date join financial_data_package.cybersyn.financial_fred_timeseries c\n        ON a.date = c.date\nWHERE  a.variable_name like '%Bank Prime Loan Rate%'\n   and b.variable_name = 'Federal Funds Effective Rate'\n   and c.variable_name like '%Consumer Price Index%Urban Consumers%Seasonally adjusted%'\nORDER BY a.date;"}, {"question": "Compare the Federal Funds Effective Rate to the rates set by the Bank of England, and the central banks of Canada, Japan and Mexico", "answer": "SELECT b.date,\r\n       a.value as boe_rate,\r\n       c.value as boj_rate,\r\n       d.value as bom_rate,\r\ne.value as boc_rate,\r\n       b.value as federal_funds_rate\r\nFROM   financial_data_package.cybersyn.financial_fred_timeseries a join financial_data_package.cybersyn.financial_fred_timeseries b\r\n        ON a.date = b.date join financial_data_package.cybersyn.financial_fred_timeseries c\r\n        ON a.date = c.date join financial_data_package.cybersyn.financial_fred_timeseries d\r\n        ON a.date = d.date join financial_data_package.cybersyn.financial_fred_timeseries e\r\n        ON a.date = e.date\r\nWHERE  a.variable_name like '%Bank of England%'\r\n   and b.variable_name = 'Federal Funds Effective Rate'\r\n   and c.variable_name like '%Bank of Japan%'\r\n   and d.variable_name like '%Mexico%'\r\nand e.variable_name like '%Canada%'\r\nORDER BY b.date;"}, {"question": "Between 1990 and 2023, how many bank branches were opened and closed annually?", "answer": "with branches_opened as (SELECT count(*) as n_branches_opened,\n                                date_trunc('YEAR', start_date) as start_year\n                         FROM   financial_data_package.cybersyn.financial_branch_entities\n                         WHERE  start_date >= '1990-01-01'\n                            and start_date < '2023-01-01'\n                         GROUP BY start_year), branches_closed as (SELECT count(*) as n_branches_closed,\n                                                 date_trunc('YEAR', end_date) as end_year\n                                          FROM   financial_data_package.cybersyn.financial_branch_entities\n                                          WHERE  end_date >= '1990-01-01'\n                                             and end_date < '2023-01-01'\n                                          GROUP BY end_year)\nSELECT coalesce(branches_opened.start_year, branches_closed.end_year) as year,\n       branches_opened.n_branches_opened,\n       branches_closed.n_branches_closed\nFROM   branches_opened full\n    OUTER JOIN branches_closed\n        ON branches_opened.start_year = branches_closed.end_year\nWHERE  coalesce(branches_opened.start_year, branches_closed.end_year) >= '1990-01-01'\nORDER BY year asc;"}, {"question": "Calculate the monthly inflation rate from the consumer price index for urban consumers", "answer": "with cpi_data as (SELECT date,\n                         value,\n                         lag(value) OVER (ORDER BY date) as prev_value\n                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\n                  WHERE  variable like '%Consumer Price Index%Urban Consumers%')\nSELECT date,\n       value as cpi,\n       (value - prev_value) / prev_value as monthly_inflation_rate\nFROM   cpi_data\nWHERE  prev_value is not null\nORDER BY date;"}, {"question": "Compare the federal funds effective rate to the monthly inflation rate expressed as a percentage based on the consumer price index for urban consumers since 1970", "answer": "with cpi_data as (SELECT date,\r\n                         value,\r\n                         lag(value) OVER (ORDER BY date) as prev_value\r\n                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\r\n                  WHERE  variable_name ilike '%Consumer Price Index%Urban Consumers%'\r\n                     and date >= '1970-01-01'), \r\ninflation_rate as (SELECT date,\r\n                          (value - prev_value) / prev_value * 100 as monthly_inflation_rate\r\n                   FROM   cpi_data\r\n                   WHERE  prev_value is not null), \r\nfederal_funds_rate as (SELECT date,\r\n                              value as federal_funds_rate\r\n                              FROM   financial_data_package.cybersyn.financial_fred_timeseries\r\n                              WHERE  variable_name = 'Federal Funds Effective Rate'\r\n                                     and date >= '1970-01-01'\r\n                       )\r\nSELECT i.date,\r\n       i.monthly_inflation_rate,\r\n       f.federal_funds_rate\r\nFROM   inflation_rate i join federal_funds_rate f\r\n        ON i.date = f.date\r\nORDER BY i.date;"}, {"question": "What is the running total of CFBP complaints by product in 2022?", "answer": "SELECT product,\n       date_received,\n       count(*) OVER (PARTITION BY product\n                      ORDER BY date_received) as running_total\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nWHERE  date_received >= '2022-01-01'\n   and date_received < '2023-01-01'\nORDER BY product, date_received"}, {"question": "Among the top 10 financial institutions in terms of the mean number of complaints annually between 2020 and 2022, what is the distribution of the total number of complaints received annually per institution by the CFPB?", "answer": "with companies_complaints as (SELECT company,\r\n                                     date_trunc('YEAR', date_received) as year,\r\n                                     count(*) as complaints_count\r\n                              FROM   financial_data_package.cybersyn.financial_cfpb_complaint\r\n                              WHERE  date_received between '2020-01-01'\r\n                                 and '2022-12-31'\r\n                              GROUP BY company, year having count(*) > 0), top_10_companies as (SELECT company,\r\n                                                                         avg(complaints_count) as mean_complaints_count\r\n                                                                  FROM   companies_complaints\r\n                                                                  GROUP BY company\r\n                                                                  ORDER BY mean_complaints_count desc limit 10)\r\nSELECT company,\r\n       date_trunc('YEAR', date_received) as year,\r\n       count(*) as annual_complaints_count\r\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\r\nWHERE  company in (SELECT company\r\n                   FROM   top_10_companies) and date_received between '2020-01-01' and '2022-12-31'\r\nGROUP BY company, year"}, {"question": "How many of the new branches that were opened since 2020 are from banks new to the state, and which state are they in? ", "answer": "with new_branches as (SELECT id_rssd_parent,\n                             state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY id_rssd_parent, state_abbreviation), pre2020_branches as (SELECT id_rssd_parent,\n                                                                          state_abbreviation\n                                                                   FROM   financial_data_package.cybersyn.financial_branch_entities\n                                                                   WHERE  start_date < '2020-01-01'\n                                                                      and end_date is null\n                                                                   GROUP BY id_rssd_parent, state_abbreviation)\nSELECT new_branches.state_abbreviation,\n       new_branch_count\nFROM   new_branches\n    LEFT JOIN pre2020_branches\n        ON (new_branches.id_rssd_parent = pre2020_branches.id_rssd_parent and\n           new_branches.state_abbreviation = pre2020_branches.state_abbreviation)\nWHERE  pre2020_branches.id_rssd_parent is null\nORDER BY new_branch_count desc;"}, {"question": "Among the top 10 companies in terms of the total number of complaints received between 2018 and 2022 by CFPB, what is the percentage breakdown of the various companies' responses to customers?", "answer": "with top_10_companies as (SELECT company,\n                                 count(*) as complaints_count\n                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                          WHERE  date_received between '2018-01-01'\n                             and '2022-12-31'\n                          GROUP BY company\n                          ORDER BY complaints_count desc limit 10), company_responses as (SELECT company,\n                                                                       company_response_to_consumer,\n                                                                       count(*) as response_count\n                                                                FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                                WHERE  date_received between '2018-01-01'\n                                                                   and '2022-12-31'\n                                                                   and company in (SELECT company\n                                                                                FROM   top_10_companies)\n                                                                GROUP BY company, company_response_to_consumer)\nSELECT company,\n       company_response_to_consumer,\n       response_count,\n       response_count*100.0/sum(response_count) OVER (PARTITION BY company) as percentage\nFROM   company_responses\nORDER BY company, percentage desc"}, {"question": "Since 2020, how many new branches have banks opened in each state?", "answer": "with new_branches as (SELECT state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY state_abbreviation)\nSELECT state_abbreviation,\n       new_branch_count\nFROM   new_branches\nORDER BY new_branch_count desc;"}, {"question": "Among the top 10 companies in terms of the total number of complaints received between 2018 and 2022 by CFPB, what are the percentages of each company's various responses to customers?", "answer": "with top_10_companies as (SELECT company,\n                                 count(*) as complaints_count\n                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                          WHERE  date_received between '2018-01-01'\n                             and '2022-12-31'\n                          GROUP BY company\n                          ORDER BY complaints_count desc limit 10), company_responses as (SELECT company,\n                                                                       company_response_to_consumer,\n                                                                       count(*) as response_count\n                                                                FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                                WHERE  date_received between '2018-01-01'\n                                                                   and '2022-12-31'\n                                                                   and company in (SELECT company\n                                                                                FROM   top_10_companies)\n                                                                GROUP BY company, company_response_to_consumer)\nSELECT company,\n       company_response_to_consumer,\n       response_count,\n       response_count * 100.0 / sum(response_count) OVER (PARTITION BY company) as percentage\nFROM   company_responses\nORDER BY company, percentage desc"}, {"question": "Compare the seasonally adjusted Consumer Price Index for urban consumers to the Federal Funds Effective Rate", "answer": "SELECT a.date,\r\n       a.value as cpi,\r\n       b.value as federal_funds_rate\r\nFROM   financial_data_package.cybersyn.financial_fred_timeseries a \r\nINNER join financial_data_package.cybersyn.financial_fred_timeseries b\r\n        ON a.date = b.date\r\nWHERE  a.variable like '%Consumer Price Index%Urban Consumers%Seasonally adjusted%'\r\n   and b.variable = 'Federal Funds Effective Rate'\r\nORDER BY a.date;"}, {"question": "Among the top 10 companies in terms of the total number of complaints received between 2020 and 2022 by CFPB, what are the various companies' responses to customers in each year expressed in percentage terms?", "answer": "with top_10_companies as (SELECT company,\n                                 count(*) as complaints_count\n                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                          WHERE  date_received between '2020-01-01'\n                             and '2022-12-31'\n                          GROUP BY company\n                          ORDER BY complaints_count desc limit 10), company_responses as (SELECT company,\n                                                                       company_response_to_consumer,\n                                                                       date_trunc('YEAR', date_received) as year,\n                                                                       count(*) as response_count\n                                                                FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                                WHERE  date_received between '2020-01-01'\n                                                                   and '2022-12-31'\n                                                                   and company in (SELECT company\n                                                                                FROM   top_10_companies)\n                                                                GROUP BY company, company_response_to_consumer, year)\nSELECT company,\n       year,\n       company_response_to_consumer,\n       response_count * 100.0 / sum(response_count) OVER (PARTITION BY company,\n                                                                       year) as percentage\nFROM   company_responses\nORDER BY company, year, percentage desc"}, {"question": "What are the names of the top 5 customers in terms of total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_quantity * l.l_extendedprice) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 5"}, {"question": "What is the total sales and number of customers for each region?", "answer": "SELECT r.r_name AS region, COUNT(DISTINCT c.c_custkey) AS number_of_customers, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nGROUP BY region"}, {"question": "What are the banks with the highest number of new branches since 2020?", "answer": "The corrected SQL code is:\n\n```\nWITH new_branches AS (\n    SELECT id_rssd_parent, COUNT(*) AS new_branch_count\n    FROM financial_data_package.cybersyn.financial_branch_entities\n    WHERE start_date >= '2020-01-01'\n    GROUP BY id_rssd_parent\n), bank_names AS (\n    SELECT id_rssd, name\n    FROM financial_data_package.cybersyn.financial_institution_entities\n)\nSELECT name, new_branch_count\nFROM bank_names\nINNER JOIN new_branches ON (bank_names.id_rssd = new_branches.id_rssd_parent)\nORDER BY new_branch_count DESC;\n```\n\nThe error message was caused by the reference to an invalid column name in the `SELECT` clause of the `bank_names` subquery. The column `name_parent` does not exist in the `financial_institution_entities` table. I have corrected this by selecting the `name` column instead."}, {"question": "Calculate the total quarterly net income for the banking industry over the last four decades", "answer": "SELECT att.variable_name,\r\n       ts.date,\r\n       ts.value\r\nFROM financial_data_package.cybersyn.financial_fred_timeseries AS ts\r\nJOIN financial_data_package.cybersyn.financial_fred_attributes AS att ON (ts.variable = att.variable)\r\nWHERE att.variable_name = 'Income and Expense: Net Income (Loss) Attributable to Bank, Not seasonally adjusted, Quarterly, USD'\r\n  AND ts.date >= '1984-01-01';"}, {"question": "Among the top 10 companies in terms of the total number of complaints received between 2020 and 2022 by CFPB, what are the various companies' responses to customers expressed in percentage terms?", "answer": "with top_10_companies as (SELECT company,\n                                 count(*) as complaints_count\n                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                          WHERE  date_received between '2020-01-01'\n                             and '2022-12-31'\n                          GROUP BY company\n                          ORDER BY complaints_count desc limit 10), company_responses as (SELECT company,\n                                                                       company_response_to_consumer,\n                                                                       count(*) as response_count\n                                                                FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                                WHERE  date_received between '2020-01-01'\n                                                                   and '2022-12-31'\n                                                                   and company in (SELECT company\n                                                                                FROM   top_10_companies)\n                                                                GROUP BY company, company_response_to_consumer)\nSELECT company,\n       company_response_to_consumer,\n       response_count * 100.0 / sum(response_count) OVER (PARTITION BY company) as percentage\nFROM   company_responses\nORDER BY company, percentage desc"}, {"question": "Randomly sample 10 CFPB customer complaints from the institution with the most number of complaints", "answer": "SELECT company,\r\n       consumer_complaint_narrative\r\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\r\nWHERE  company = (SELECT company\r\n                  FROM   financial_data_package.cybersyn.financial_cfpb_complaint\r\n                  GROUP BY company\r\n                  ORDER BY count(*) desc limit 1) and consumer_complaint_narrative is not null\r\nORDER BY random() limit 10"}, {"question": "How many CFPB complaints were there by state?", "answer": "SELECT state,\n       count(*) as count\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nGROUP BY state\nORDER BY count desc"}, {"question": "What is the 30 year fixed rate mortgage average in the united states?", "answer": "SELECT avg(value) as avg_rate\nFROM   financial_data_package.cybersyn.financial_fred_timeseries\nWHERE  variable = 'MORTGAGE30US'"}, {"question": "What are the first 10 rows in the FINANCIAL_BRANCH_ENTITIES table?", "answer": "SELECT ID_RSSD, ID_RSSD_PARENT, NAME_PARENT, BRANCH_NAME, CATEGORY, IS_ACTIVE, START_DATE, END_DATE, ADDRESS, ADDRESS_LINE2, CITY, STATE_ABBREVIATION, ZIP_CODE, ID_COUNTRY, ID_STATE, ID_COUNTY, ID_ZIP\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_BRANCH_ENTITIES\r\nLIMIT 10"}, {"question": "How many CPFB complaints of each product were made in 2022", "answer": "SELECT product,\r\n       count(*) as count\r\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\r\nWHERE  date_received between '2022-01-01'\r\n   and '2022-12-31'\r\nGROUP BY product\r\nORDER BY count desc"}, {"question": "Among the top 5 companies in terms of the total number of complaints received between 2020 and 2022 by CFPB, what is the percentage breakdown of the various companies' responses to customers in each year?", "answer": "with top_5_companies as (SELECT company,\n                                count(*) as total_complaints\n                         FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                         WHERE  date_received between '2020-01-01'\n                            and '2022-12-31'\n                         GROUP BY company\n                         ORDER BY total_complaints desc limit 5), company_responses as (SELECT company,\n                                                                      company_response_to_consumer,\n                                                                      date_trunc('year', date_received) as year,\n                                                                      count(*) as response_count\n                                                               FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                               WHERE  date_received between '2020-01-01'\n                                                                  and '2022-12-31'\n                                                                  and company in (SELECT company\n                                                                               FROM   top_5_companies)\n                                                               GROUP BY company, company_response_to_consumer, year)\nSELECT company,\n       year,\n       company_response_to_consumer,\n       response_count,\n       response_count*100.0/sum(response_count) OVER (PARTITION BY company,\n                                                                   year) as percentage\nFROM   company_responses\nORDER BY company, year, percentage desc"}, {"question": "What are the first 10 rows in the GEOGRAPHY_HIERARCHY table?", "answer": "SELECT PARENT_GEO_ID, GEO_ID\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.GEOGRAPHY_HIERARCHY\r\nLIMIT 10"}, {"question": "What is the mean monthly not seasonally adjusted unemployment rates of Canada between 2007 and 2022", "answer": "SELECT date,\r\n       avg(value) as avg_unemployment_rate\r\nFROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries\r\nWHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n   and date between '2007-01-01'\r\n   and '2022-12-31'\r\n   and geo_id like '%CA%'\r\nGROUP BY date\r\nORDER BY date;"}, {"question": "What are the first 10 rows in the FINANCIAL_INSTITUTION_EVENTS table?", "answer": "SELECT ID_RSSD_SUCCESSOR, NAME_SUCCESSOR, ACTIVE_SUCCESSOR, CATEGORY_SUCCESSOR, ID_RSSD_PREDECESSOR, NAME_PREDECESSOR, ACTIVE_PREDECESSOR, CATEGORY_PREDECESSOR, TRANSACTION_DATE, TRA<PERSON>FORMATION_TYPE, MERGER_ACCOUNTING_METHOD_UTILIZED\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_INSTITUTION_EVENTS\r\nLIMIT 10"}, {"question": "What are the number of countries in each region, in descending order?", "answer": "SELECT r.r_name AS region_name, COUNT(DISTINCT n.n_name) AS num_countries\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r \nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\nGROUP BY region_name\nORDER BY num_countries DESC"}, {"question": "What are the top 3 customers by sales ranking for each of the top 2 regions by total sales?", "answer": "WITH\n  sales_by_region AS (\n    SELECT\n      r.r_name AS region_name,\n      c.c_name AS customer_name,\n      SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\n    FROM\n      SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\n    GROUP BY\n      region_name,\n      customer_name\n  ),\n  ranked_sales AS (\n    SELECT\n      region_name,\n      customer_name,\n      total_sales,\n      RANK() OVER (\n        PARTITION BY region_name\n        ORDER BY total_sales DESC\n      ) AS sales_rank\n    FROM\n      sales_by_region\n  )\nSELECT *\nFROM ranked_sales\nWHERE region_name IN (\n  SELECT\n    r.r_name\n  FROM\n    SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\n  GROUP BY\n    r.r_name\n  ORDER BY\n    SUM(l.l_extendedprice * (1 - l.l_discount)) DESC\n  LIMIT 2\n)\nAND sales_rank <= 3;"}, {"question": "What are the names of the top three customers in terms of total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_quantity * l.l_extendedprice) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 3"}, {"question": "What are the total sales for each year for the most popular part?", "answer": "SELECT \n  TO_CHAR(o.o_orderdate, 'YYYY') AS order_year, \n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM \n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p ON l.l_partkey = p.p_partkey\nWHERE \n  p.p_name = (\n    SELECT \n      p_name \n    FROM \n      SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART \n    JOIN \n      SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM ON p_partkey = l_partkey \n    GROUP BY \n      p_name \n    ORDER BY \n      SUM(l_quantity) DESC \n    LIMIT 1\n  )\nGROUP BY \n  order_year\nORDER BY \n  order_year;"}, {"question": "Among the top 10 zip codes in terms of the total number of complaints received between 2018 and 2022 by CFPB, what is the percentage breakdown of the various responses to customers? ", "answer": "with top_10_zip_codes as (SELECT id_zip,\r\n                                 count(*) as complaints_count\r\n                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\r\n                          WHERE  date_trunc('year', date_received) between '2018-01-01'\r\n                             and '2022-12-31'\r\n                          GROUP BY id_zip\r\n                          ORDER BY complaints_count desc limit 10), zip_code_company_responses as (SELECT id_zip,\r\n                                                                                company_response_to_consumer,\r\n                                                                                count(*) as response_count\r\n                                                                         FROM   financial_data_package.cybersyn.financial_cfpb_complaint\r\n                                                                         WHERE  date_trunc('year', date_received) between '2018-01-01'\r\n                                                                            and '2022-12-31'\r\n                                                                            and id_zip in (SELECT id_zip\r\n                                                                                        FROM   top_10_zip_codes)\r\n                                                                         GROUP BY id_zip, company_response_to_consumer)\r\nSELECT id_zip,\r\n       company_response_to_consumer,\r\n       response_count,\r\n       response_count*100.0/sum(response_count) OVER (PARTITION BY id_zip) as percentage\r\nFROM   zip_code_company_responses\r\nWHERE  id_zip in (SELECT id_zip\r\n                  FROM   top_10_zip_codes)\r\nORDER BY id_zip, percentage desc"}, {"question": "What are the first 10 rows in the GEOGRAPHY_OVERLAPS table?", "answer": "SELECT GEO_ID, OVERLAPS_WITH\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.GEOGRAPHY_OVERLAPS\r\nLIMIT 10"}, {"question": "Which is the zip code with the most number of bank branches", "answer": "SELECT id_zip,\n       count(*) as branch_count\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  end_date is null\nGROUP BY id_zip\nORDER BY branch_count desc limit 1;"}, {"question": "Using the BLS Price time series, calculate the monthly inflation percentage of airline fares using the consumer price index of airline fares since 1980", "answer": "with airline_cpi as (SELECT date_trunc('month', date) as month,\n                            value as cpi\n                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_price_timeseries\n                     WHERE  variable_name ilike 'cpi%airline fares%monthly%'\n                        and date >= '1980-01-01'), monthly_inflation as (SELECT month,\n                                                        (cpi - lag(cpi) OVER (ORDER BY month)) / lag(cpi) OVER (ORDER BY month) * 100 as inflation_percentage\n                                                 FROM   airline_cpi)\nSELECT month,\n       inflation_percentage\nFROM   monthly_inflation\nWHERE  inflation_percentage is not null\nORDER BY month;"}, {"question": "For each type of complaint made to CFPB between 2020 and 2022, what is the probability of receiving monetary relief as a company response to consumer?", "answer": "with complaints_product_response as (SELECT product,\n                                            company_response_to_consumer,\n                                            count(*) as count\n                                     FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                     WHERE  date_received between '2020-01-01'\n                                        and '2022-12-31'\n                                     GROUP BY product, company_response_to_consumer), complaints_product as (SELECT product,\n                                                                               count(*) as n_complaints\n                                                                        FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                                        WHERE  date_received between '2020-01-01'\n                                                                           and '2022-12-31'\n                                                                        GROUP BY product)\nSELECT r.product,\n       100*r.count/n_complaints as prob_relief\nFROM   complaints_product_response as r\n    INNER JOIN complaints_product as p\n        ON p.product = r.product\nWHERE  r.company_response_to_consumer like '% monetary relief';"}, {"question": "What are the first 10 rows in the FINANCIAL_INSTITUTION_TIMESERIES table?", "answer": "SELECT ID_RSSD, <PERSON><PERSON><PERSON><PERSON>, VARIABLE_NAME, VA<PERSON>UE, UNIT, DATE\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_INSTITUTION_TIMESERIES\r\nLIMIT 10"}, {"question": "What are the first 10 rows in the FINANCIAL_CFPB_COMPLAINT table?", "answer": "SELECT ID_ZIP, ID_STATE, PROD<PERSON><PERSON>, SUB_PRODUCT, ISSUE, SUB_ISSUE, COMPANY, CONSUMER_COMPLAINT_NARRATIVE, COMPANY_PUBLIC_RESPONSE, COMPANY_RESPONSE_TO_CONSUMER, STAT<PERSON>, ZIP_CODE, TAG<PERSON>, CONSUMER_CONSENT_PROVIDED, SUBMITTED_VIA, CONSUMER_DISPUTED, DATE_RECEIVED, DATE_SENT_TO_COMPANY, TIMELY_RESPONSE\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_CFPB_COMPLAINT\r\nLIMIT 10"}, {"question": "Compare the states mean monthly not seasonally adjusted unemployment rates in 2008-2010 versus 2020-2022 and select the 10 states with the biggest differences", "answer": "with unemployment_rate_2008_2010 as (SELECT geo_name,\n                                            avg(value) as avg_unemployment_rate\n                                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\n                                             ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\n                                     WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n                                        and date between '2008-01-01'\n                                        and '2010-12-31'\n                                        and level = 'State'\n                                     GROUP BY geo_name), unemployment_rate_2020_2022 as (SELECT geo_name,\n                                                           avg(value) as avg_unemployment_rate\n                                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\n                                                            ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\n                                                    WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n                                                       and date between '2020-01-01'\n                                                       and '2022-12-31'\n                                                       and level = 'State'\n                                                    GROUP BY geo_name), differences as (SELECT u1.geo_name,\n                                           u1.avg_unemployment_rate as avg_unemployment_rate_2008_2010,\n                                           u2.avg_unemployment_rate as avg_unemployment_rate_2020_2022,\n                                           u2.avg_unemployment_rate - u1.avg_unemployment_rate as difference\n                                    FROM   unemployment_rate_2008_2010 u1 join unemployment_rate_2020_2022 u2\n                                            ON u1.geo_name = u2.geo_name)\nSELECT geo_name,\n       avg_unemployment_rate_2008_2010,\n       avg_unemployment_rate_2020_2022,\n       difference\nFROM   differences\nORDER BY abs(difference) desc limit 10;"}, {"question": "What are the first 10 rows in the FINANCIAL_INSTITUTION_HIERARCHY table?", "answer": "SELECT ID_RSSD_PARENT, NAME_PARENT, ACTIVE_PARENT, CATEGORY_PARENT, ID_RSSD_OFFSPRING, NAME_OFFSPRING, ACTIVE_OFFSPRING, CATEGORY_OFFSPRING, RELATIONSHIP_TERMINATED, PARENT_CONTROLS_OFFSPRING, PERCENT_CONTROL_OF_OFFSPRING, RELATIONSHIP_LEVEL\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_INSTITUTION_HIERARCHY\r\nLIMIT 10"}, {"question": "Using the BLS Price time series, calculate the monthly percentage change in airline fares using the consumer price index of airline fares since 1980", "answer": "with airline_cpi as (SELECT date_trunc('month', date) as month,\r\n                            value as cpi\r\n                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_price_timeseries\r\n                     WHERE  variable_name ilike 'CPI%Airline Fares%Not Seasonally Adjusted%Monthly%'\r\n                        and date >= '1980-01-01'), monthly_percentage_change as (SELECT month,\r\n                                                                (cpi - lag(cpi) OVER (ORDER BY month)) / lag(cpi) OVER (ORDER BY month) * 100 as percentage_change\r\n                                                         FROM   airline_cpi)\r\nSELECT month,\r\n       percentage_change\r\nFROM   monthly_percentage_change\r\nWHERE  percentage_change is not null\r\nORDER BY month DESC;"}, {"question": "Compare the Bank Prime Loan Rate to the Federal Funds Effective Rate", "answer": "SELECT a.date,\r\n       a.value as bank_prime_loan_rate,\r\n       b.value as federal_funds_effective_rate\r\nFROM   financial_data_package.cybersyn.financial_fred_timeseries a join financial_data_package.cybersyn.financial_fred_timeseries b\r\n        ON a.date = b.date\r\nWHERE  a.variable_name like '%Bank Prime Loan Rate%'\r\n   and b.variable_name = 'Federal Funds Effective Rate'\r\nORDER BY a.date;"}, {"question": "What are the first 10 rows in the FINANCIAL_INSTITUTION_ENTITIES table?", "answer": "SELECT ID_RSSD, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IS_ACTIVE, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, STATE_ABBREVIATION, ZIP_CODE, URL, START_DATE, <PERSON>ND_DATE, CHARTERING_AUTHORITY, CHARTER_TYPE, FEDERAL_REGULATOR, ENTITY_TYPE, INSURE<PERSON>, <PERSON>DIC_CERT, OCC_ID, THRIFT_ID, ID_COUNTRY, ID_STATE, ID_COUNTY, ID_ZIP, LEGAL_ENTITY_IDENTIFIER, EMPLOYER_IDENTIFICATION_NUMBER, REASON_FOR_ENTITY_TERMINATION, INTERNATIONAL_BANKING_FACILITY, NAICS_CODE, MAJORITY_OWNED_BY_MINORITY_OR_WOMEN, SPECIAL<PERSON><PERSON>ATION_GROUP\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_INSTITUTION_ENTITIES\r\nLIMIT 10"}, {"question": "What are the average hourly earnings of employees in accommodation and food services in Hawaii since 2010?", "answer": "SELECT L.GEO_ID, GEO_NAME, LEVEL, <PERSON><PERSON><PERSON><PERSON>_NAME, VALUE, DATE\r\nFROM financial_data_package.cybersyn.BUREAU_OF_LABOR_STATISTICS_EMPLOYMENT_TIMESERIES AS L\r\nINNER JOIN financial_data_package.cybersyn.GEOGRAPHY_INDEX AS G\r\n    ON L.GEO_ID = G.GEO_ID\r\nWHERE VARIABLE_NAME LIKE '%Hourly Earnings%Leisure and Hospitality: Accommodation and Food Services%' \r\nAND VARIABLE_NAME LIKE '%seasonally adjusted%Monthly%'\r\nAND DATE > to_date('2010-01-01')\r\nAND GEO_NAME = 'Hawaii'"}, {"question": "What are the top 7 customers ranked by total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 7"}, {"question": "Compare the growth and decline of bank branches by state since the start of the pandemic", "answer": "WITH pre_covid AS (\r\n    SELECT state_abbreviation,\r\n           COUNT(*) AS pre_covid_count\r\n    FROM financial_data_package.cybersyn.financial_branch_entities\r\n    WHERE start_date <= '2020-03-01'\r\n      AND (end_date >= '2020-03-01' OR end_date IS NULL)\r\n    GROUP BY state_abbreviation\r\n)\r\nSELECT cur.state_abbreviation,\r\n       pre_covid_count,\r\n       COUNT(*)                            AS current_count,\r\n       current_count / pre_covid_count - 1 AS pct_change\r\nFROM financial_data_package.cybersyn.financial_branch_entities AS cur\r\nINNER JOIN pre_covid ON (cur.state_abbreviation = pre_covid.state_abbreviation)\r\nWHERE end_date IS NULL\r\nGROUP BY cur.state_abbreviation, pre_covid_count\r\nORDER BY pct_change;"}, {"question": "Sample 10 CFPB customer complaints from the institution with the most number of complaints", "answer": "SELECT company,\n       consumer_complaint_narrative\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nWHERE  company = (SELECT company\n                  FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                  GROUP BY company\n                  ORDER BY count(*) desc limit 1) and consumer_complaint_narrative is not null limit 10"}, {"question": "Since 2020, which banks opened new branches in states they were new to, how many new branches were there in each state, and what are the names of these states?  ", "answer": "with new_branches as (SELECT id_rssd_parent,\r\n                             state_abbreviation,\r\n                             count(*) as new_branch_count\r\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\r\n                      WHERE  start_date >= '2020-01-01'\r\n                      GROUP BY id_rssd_parent, state_abbreviation), pre2020_branches as (SELECT id_rssd_parent,\r\n                                                                          state_abbreviation\r\n                                                                   FROM   financial_data_package.cybersyn.financial_branch_entities\r\n                                                                   WHERE  start_date < '2020-01-01'\r\n                                                                      and end_date is null\r\n                                                                      and state_abbreviation is not null\r\n                                                                   GROUP BY id_rssd_parent, state_abbreviation)\r\nSELECT bank_names.name,\r\n       new_branches.state_abbreviation,\r\n       new_branch_count\r\nFROM   new_branches\r\n    INNER JOIN financial_data_package.cybersyn.financial_institution_entities as bank_names\r\n        ON (new_branches.id_rssd_parent = bank_names.id_rssd)\r\n    LEFT JOIN pre2020_branches\r\n        ON (new_branches.id_rssd_parent = pre2020_branches.id_rssd_parent and\r\n           new_branches.state_abbreviation = pre2020_branches.state_abbreviation)\r\nWHERE  pre2020_branches.id_rssd_parent is null\r\nORDER BY new_branch_count desc, bank_names.name;"}, {"question": "What are the annual hourly earnings of employees in the accommodation and food services sector in each state since 2000?", "answer": "SELECT l.geo_id,\n       geo_name,\n       level,\n       variable_name,\n       value,\n       date\nFROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries as l\n    INNER JOIN financial_data_package.cybersyn.geography_index as g\n        ON l.geo_id = g.geo_id\nWHERE  variable_name like '%Leisure and Hospitality: Accommodation and Food Services%'\n   and variable_name like '%Average Hourly Earnings%Annual%'\n   and date > to_date('2000-01-01')\n   and level = 'State'\nORDER BY geo_name, date desc"}, {"question": "Which are the top 10 banks by the number of branches closed in 2022?", "answer": "with branches_closed as (SELECT id_rssd_parent,\r\n                                count(*) as n_branches_closed\r\n                         FROM   financial_data_package.cybersyn.financial_branch_entities\r\n                         WHERE  end_date between '2022-01-01'\r\n                            and '2022-12-31'\r\n                         GROUP BY id_rssd_parent)\r\nSELECT name,\r\n       n_branches_closed\r\nFROM   branches_closed\r\n    INNER JOIN financial_data_package.cybersyn.financial_institution_entities as ent\r\n        ON (branches_closed.id_rssd_parent = ent.id_rssd)\r\nORDER BY n_branches_closed desc limit 10;"}, {"question": "Since 2000, how many new branches have been opened per year?", "answer": "SELECT date_trunc('YEAR', start_date) as year,\n       count(*) as n_branches_opened\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  start_date >= '2000-01-01'\nGROUP BY year\nORDER BY year asc;"}, {"question": "How many of the new branches that were opened since 2020 are from banks new to the state, and which state are they in, and what are the names of the banks? ", "answer": "with new_branches as (SELECT id_rssd_parent,\n                             state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY id_rssd_parent, state_abbreviation), pre2020_branches as (SELECT id_rssd_parent,\n                                                                          state_abbreviation\n                                                                   FROM   financial_data_package.cybersyn.financial_branch_entities\n                                                                   WHERE  start_date < '2020-01-01'\n                                                                      and end_date is null\n                                                                      and state_abbreviation is not null\n                                                                   GROUP BY id_rssd_parent, state_abbreviation)\nSELECT new_branches.state_abbreviation,\n       new_branch_count,\n       bank_names.name\nFROM   new_branches\n    INNER JOIN financial_data_package.cybersyn.financial_institution_entities as bank_names\n        ON new_branches.id_rssd_parent = bank_names.id_rssd\n    LEFT JOIN pre2020_branches\n        ON new_branches.id_rssd_parent = pre2020_branches.id_rssd_parent and\n           new_branches.state_abbreviation = pre2020_branches.state_abbreviation\nWHERE  pre2020_branches.id_rssd_parent is null\nORDER BY new_branch_count desc, new_branches.state_abbreviation, bank_names.name;"}, {"question": "Which are the top 10 cities by the number of new branches opened in 2022?", "answer": "with new_branches as (SELECT city,\n                             state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2022-01-01'\n                      GROUP BY city, state_abbreviation)\nSELECT city || ', ' || state_abbreviation as city_state,\n       new_branch_count\nFROM   new_branches\nORDER BY new_branch_count desc limit 10;"}, {"question": "Determine which banks have the highest percentage of uninsured deposits", "answer": "WITH big_banks AS (\r\n    SELECT id_rssd\r\n    FROM financial_data_package.cybersyn.financial_institution_timeseries\r\n    WHERE variable = 'ASSET'\r\n      AND date = '2022-12-31'\r\n      AND value > 1E10\r\n)\r\nSELECT name,\r\n       1 - value AS pct_uninsured,\r\n       ent.is_active\r\nFROM financial_data_package.cybersyn.financial_institution_timeseries AS ts\r\nINNER JOIN financial_data_package.cybersyn.financial_institution_attributes AS att ON (ts.variable = att.variable)\r\nINNER JOIN financial_data_package.cybersyn.financial_institution_entities AS ent ON (ts.id_rssd = ent.id_rssd)\r\nINNER JOIN big_banks ON (big_banks.id_rssd = ts.id_rssd)\r\nWHERE ts.date = '2022-12-31'\r\n  AND att.variable_name = '% Insured (Estimated)'\r\n  AND att.frequency = 'Quarterly'\r\nORDER BY pct_uninsured DESC;"}, {"question": "What is the 3 month running average of each states' mean monthly not seasonally adjusted unemployment rates, between 2007 to 2022?", "answer": "with monthly_unemployment_rates as (SELECT g.geo_name,\n                                           b.date,\n                                           avg(b.value) as avg_unemployment_rate\n                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries b join financial_data_package.cybersyn.geography_index g\n                                            ON b.geo_id = g.geo_id\n                                    WHERE  b.variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n                                       and b.date between '2007-01-01'\n                                       and '2022-12-31'\n                                       and g.level = 'State'\n                                    GROUP BY g.geo_name, b.date)\nSELECT geo_name,\n       date,\n       avg(avg_unemployment_rate) OVER (PARTITION BY geo_name\n                                        ORDER BY date rows between 1 preceding and 1 following) as three_month_running_avg\nFROM   monthly_unemployment_rates\nORDER BY geo_name, date;"}, {"question": "Since 2005, how many new branches have been opened per year?", "answer": "SELECT date_trunc('YEAR', start_date) as year,\n       count(*) as n_branches_opened\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  start_date >= '2005-01-01'\nGROUP BY year\nORDER BY year asc;"}, {"question": "In each state, what is the percentage of existing bank branches that only began operations since 2020?", "answer": "with new_branches as (SELECT state_abbreviation,\r\n                             count(*) as new_branch_count\r\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\r\n                      WHERE  start_date >= '2020-01-01'\r\n                      GROUP BY state_abbreviation), all_branches as (SELECT state_abbreviation,\r\n                                                      count(*) as all_branch_count\r\n                                               FROM   financial_data_package.cybersyn.financial_branch_entities\r\n                                               WHERE  end_date is null\r\n                                               GROUP BY state_abbreviation)\r\nSELECT all_branches.state_abbreviation,\r\n       100.0 * new_branch_count / all_branch_count as pct_new_branches\r\nFROM   all_branches\r\n    INNER JOIN new_branches\r\n        ON (all_branches.state_abbreviation = new_branches.state_abbreviation)\r\nORDER BY pct_new_branches desc"}, {"question": "How many CPFB complaints of each product were made cumulatively in 2022?", "answer": "SELECT product,\n       date_trunc('month', date_received) as month,\n       count(*) OVER (PARTITION BY product,\n                                   date_trunc('month', date_received)\n                      ORDER BY date_received) as cumulative_count\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nWHERE  date_received >= '2022-01-01'\n   and date_received < '2023-01-01'\nORDER BY product, month;"}, {"question": "How many bank failures are there in each year since 1945?", "answer": "SELECT COUNT(NAME_PREDECESSOR) AS N_BANKS, DATE_TRUNC('YEAR', TRANSACTION_DATE) AS TRANSACTION_YEAR\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_INSTITUTION_EVENTS\r\nWHERE TRANSFORMATION_TYPE = 'Failure'\r\nAND CATEGORY_PREDECESSOR = 'Bank'\r\nGROUP BY TRANSACTION_YEAR\r\nORDER BY TRANSACTION_YEAR DESC"}, {"question": "Since 2010, how many new branches have been opened per year?", "answer": "SELECT date_trunc('YEAR', start_date) as year,\n       count(*) as n_branches_opened\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  start_date >= '2010-01-01'\nGROUP BY year\nORDER BY year asc;"}, {"question": "What are the top 10 countries ranked by total sales, based on customer and order data?", "answer": "SELECT\n  n.n_name AS country_name,\n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY country_name\nORDER BY total_sales DESC\nLIMIT 10;"}, {"question": "What are the first 10 rows in the GEOGRAPHY_INDEX table?", "answer": "SELECT GEO_ID, GEO_NAME, LE<PERSON><PERSON>, ISO_NAME, ISO_ALPHA2, ISO_ALPHA3, ISO_NUMERIC_CODE, ISO_3166_2_CODE\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.GEOGRAPHY_INDEX\r\nLIMIT 10"}, {"question": "Using the BLS Price time series, calculate the annualized monthly inflation percentage of airline fares using the consumer price index of airline fares since 1980", "answer": "with airline_cpi as (SELECT date_trunc('month', date) as month,\r\n                            value as cpi\r\n                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_price_timeseries\r\n                     WHERE  variable_name ilike 'cpi%airline fares%monthly%'\r\n                        and date >= '1980-01-01'), monthly_inflation as (SELECT month,\r\n                                                        (power(1+ (cpi - lag(cpi) OVER (ORDER BY month)) / lag(cpi) OVER (ORDER BY month),12)-1) * 100 as inflation_percentage\r\n                                                 FROM   airline_cpi)\r\nSELECT month,\r\n       inflation_percentage\r\nFROM   monthly_inflation\r\nWHERE  inflation_percentage is not null\r\nORDER BY month;"}, {"question": "In each state, what percentage of bank branches are new? New is defined as a branch that began operations since 2020", "answer": "with new_branches as (SELECT state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY state_abbreviation), all_branches as (SELECT state_abbreviation,\n                                                      count(*) as all_branch_count\n                                               FROM   financial_data_package.cybersyn.financial_branch_entities\n                                               WHERE  end_date is null\n                                               GROUP BY state_abbreviation)\nSELECT all_branches.state_abbreviation,\n       100.0 * new_branch_count / all_branch_count as pct_new_branches\nFROM   all_branches\n    INNER JOIN new_branches\n        ON (all_branches.state_abbreviation = new_branches.state_abbreviation)\nORDER BY pct_new_branches desc"}, {"question": "How many of the new branches that were opened since 2020 are from banks new to the state, which state are they in, and what are the names of the banks? ", "answer": "with new_branches as (SELECT id_rssd_parent,\n                             state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY id_rssd_parent, state_abbreviation), pre2020_branches as (SELECT id_rssd_parent,\n                                                                          state_abbreviation\n                                                                   FROM   financial_data_package.cybersyn.financial_branch_entities\n                                                                   WHERE  start_date < '2020-01-01'\n                                                                      and end_date is null\n                                                                      and state_abbreviation is not null\n                                                                   GROUP BY id_rssd_parent, state_abbreviation)\nSELECT bank_names.name,\n       new_branches.state_abbreviation,\n       new_branch_count\nFROM   new_branches\n    INNER JOIN financial_data_package.cybersyn.financial_institution_entities as bank_names\n        ON new_branches.id_rssd_parent = bank_names.id_rssd\n    LEFT JOIN pre2020_branches\n        ON new_branches.id_rssd_parent = pre2020_branches.id_rssd_parent and\n           new_branches.state_abbreviation = pre2020_branches.state_abbreviation\nWHERE  pre2020_branches.id_rssd_parent is null\nORDER BY new_branch_count desc, new_branches.state_abbreviation, bank_names.name;"}, {"question": "Create a cumulative frequency distribution of the number of complaints received by CFPB against the financial institution with the most number of complaints in 2022", "answer": "with fiwmc_2022_complaints as (SELECT count(*) as n_complaints\n                               FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                               WHERE  company = (SELECT company\n                                                 FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                 WHERE  date_received between '2022-01-01'\n                                                    and '2022-12-31'\n                                                 GROUP BY company\n                                                 ORDER BY count(*) desc limit 1) and date_received between '2022-01-01' and '2022-12-31')\nSELECT n_complaints,\n       count(*) as frequency,\n       sum(count(*)) OVER (ORDER BY n_complaints asc) as cumulative_frequency\nFROM   fiwmc_2022_complaints\nGROUP BY n_complaints\nORDER BY n_complaints asc"}, {"question": "What is the average number of items per order?", "answer": "SELECT AVG(total_items) AS avg_order_size\nFROM (\n  SELECT o.o_orderkey AS order_id, SUM(l.l_quantity) AS total_items\n  FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\n  GROUP BY o.o_orderkey\n)"}, {"question": "What are the top two regions by total sales of all orders?", "answer": "SELECT\n  r.r_name AS region_name,\n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY region_name\nORDER BY total_sales DESC\nLIMIT 2;"}, {"question": "How many bank failures are there in each year since 1930?", "answer": "SELECT count(name_predecessor) as n_banks,\n       date_trunc('YEAR', transaction_date) as transaction_year\nFROM   financial_data_package.cybersyn.financial_institution_events\nWHERE  transformation_type = 'Failure'\n   and category_predecessor = 'Bank'\n   and transaction_date >= '1930-01-01'\nGROUP BY transaction_year\nORDER BY transaction_year desc"}, {"question": "Compare the states mean monthly not seasonally adjusted unemployment rates in 2008-2010 versus 2020-2022?", "answer": "with unemployment_rate_2008_2010 as (SELECT geo_name,\n                                            avg(value) as avg_unemployment_rate\n                                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\n                                             ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\n                                     WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n                                        and date between to_date('2008-01-01')\n                                        and to_date('2010-12-31')\n                                        and level = 'State'\n                                     GROUP BY geo_name), unemployment_rate_2020_2022 as (SELECT geo_name,\n                                                           avg(value) as avg_unemployment_rate\n                                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\n                                                            ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\n                                                    WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\n                                                       and date between to_date('2020-01-01')\n                                                       and to_date('2022-12-31')\n                                                       and level = 'State'\n                                                    GROUP BY geo_name)\nSELECT a.geo_name,\n       a.avg_unemployment_rate as avg_unemployment_rate_2008_2010,\n       b.avg_unemployment_rate as avg_unemployment_rate_2020_2022\nFROM   unemployment_rate_2008_2010 a join unemployment_rate_2020_2022 b\n        ON a.geo_name = b.geo_name\nORDER BY a.geo_name;"}, {"question": "What are the total sales for each region?", "answer": "SELECT r.r_name as region,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n        ON n.n_regionkey = r.r_regionkey\nGROUP BY region\nORDER BY total_sales desc;"}, {"question": "How many complaints of each product did CPFB receive in 2022?", "answer": "SELECT product,\r\n       count(*) as count\r\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\r\nWHERE  date_received between '2022-01-01'\r\n   and '2022-12-31'\r\nGROUP BY product\r\nORDER BY count desc"}, {"question": "How many CPFB complaints of each product were made cumulatively for each month in 2022?", "answer": "SELECT product,\n       date_trunc('month', date_received) as month,\n       count(*) OVER (PARTITION BY product,\n                                   date_trunc('month', date_received)\n                      ORDER BY date_received) as cumulative_count\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nWHERE  date_received >= '2022-01-01'\n   and date_received < '2023-01-01'\nORDER BY product, month;"}, {"question": "What are the first 10 rows in the FINANCIAL_FRED_TIMESERIES table?", "answer": "SELECT GEO_ID, <PERSON><PERSON><PERSON><PERSON>, VA<PERSON><PERSON>LE_NAME, DAT<PERSON>, VA<PERSON>UE, UNIT\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_FRED_TIMESERIES\r\nLIMIT 10"}, {"question": "What are the top 5 customers in terms of total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 5"}, {"question": "Between 1990 and 2022, how many bank branches were opened and closed annually?", "answer": "with branches_opened as (SELECT count(*) as n_branches_opened,\n                                date_trunc('YEAR', start_date) as start_year\n                         FROM   financial_data_package.cybersyn.financial_branch_entities\n                         WHERE  start_date >= '1990-01-01'\n                            and start_date < '2022-01-01'\n                         GROUP BY start_year), branches_closed as (SELECT count(*) as n_branches_closed,\n                                                 date_trunc('YEAR', end_date) as end_year\n                                          FROM   financial_data_package.cybersyn.financial_branch_entities\n                                          WHERE  end_date >= '1990-01-01'\n                                             and end_date < '2022-01-01'\n                                          GROUP BY end_year)\nSELECT coalesce(branches_opened.start_year, branches_closed.end_year) as year,\n       branches_opened.n_branches_opened,\n       branches_closed.n_branches_closed\nFROM   branches_opened full\n    OUTER JOIN branches_closed\n        ON branches_opened.start_year = branches_closed.end_year\nWHERE  coalesce(branches_opened.start_year, branches_closed.end_year) >= '1990-01-01'\nORDER BY year asc;"}, {"question": "Among the top 10 zip codes in terms of the total number of complaints received between 2018 and 2022 by CFPB, what is the percentage breakdown of the various companies' responses to customers? ", "answer": "with top_10_zip_codes as (SELECT id_zip,\n                                 count(*) as complaints_count\n                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                          WHERE  date_trunc('year', date_received) between '2018-01-01'\n                             and '2022-12-31'\n                          GROUP BY id_zip\n                          ORDER BY complaints_count desc limit 10), zip_code_complaints as (SELECT id_zip,\n                                                                         company,\n                                                                         count(*) as complaints_count\n                                                                  FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                                  WHERE  date_trunc('year', date_received) between '2018-01-01'\n                                                                     and '2022-12-31'\n                                                                     and id_zip in (SELECT id_zip\n                                                                                 FROM   top_10_zip_codes)\n                                                                  GROUP BY id_zip, company), zip_code_company_responses as (SELECT id_zip,\n                                                                 company,\n                                                                 company_response_to_consumer,\n                                                                 count(*) as response_count\n                                                          FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                          WHERE  date_trunc('year', date_received) between '2018-01-01'\n                                                             and '2022-12-31'\n                                                             and id_zip in (SELECT id_zip\n                                                                         FROM   top_10_zip_codes)\n                                                          GROUP BY id_zip, company, company_response_to_consumer)\nSELECT id_zip,\n       company,\n       company_response_to_consumer,\n       response_count,\n       response_count*100.0/sum(response_count) OVER (PARTITION BY id_zip,\n                                                                   company) as percentage\nFROM   zip_code_company_responses\nWHERE  id_zip in (SELECT id_zip\n                  FROM   top_10_zip_codes)\nORDER BY id_zip asc, company asc, percentage desc"}, {"question": "Which are the top 10 banks by the number of new branches opened in 2022?", "answer": "with new_branches as (SELECT id_rssd_parent,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2022-01-01'\n                      GROUP BY id_rssd_parent), bank_names as (SELECT id_rssd,\n                                                name\n                                         FROM   financial_data_package.cybersyn.financial_institution_entities)\nSELECT bank_names.name,\n       new_branch_count\nFROM   new_branches\n    INNER JOIN bank_names\n        ON new_branches.id_rssd_parent = bank_names.id_rssd\nORDER BY new_branch_count desc limit 10;"}, {"question": "How many seasonally adjusted employees are there in the accommodation and food services sector in each state since 2015?", "answer": "SELECT l.geo_id,\n       geo_name,\n       level,\n       variable_name,\n       value,\n       date\nFROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries as l\n    INNER JOIN financial_data_package.cybersyn.geography_index as g\n        ON l.geo_id = g.geo_id\nWHERE  variable_name like '%Leisure and Hospitality: Accommodation and Food Services%'\n   and variable_name like '%Total employment, including self-employed, Not seasonally adjusted, Monthly, Persons, NSA%'\n   and date > to_date('2015-01-01')\n   and level = 'State'\nORDER BY geo_name, date desc"}, {"question": "Sample a CFPB customer complaint", "answer": "SELECT consumer_complaint_narrative\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nWHERE  consumer_complaint_narrative is not null limit 1"}, {"question": "What is the CPI for tuition in the USA?", "answer": "SELECT value as cpi_tuition\nFROM   financial_data_package.cybersyn.financial_fred_timeseries\nWHERE  variable = 'CPIAUCSL'\n   and lower(variable_name) like '%tuition%'\nORDER BY date desc limit 1"}, {"question": "Since 2018, how many new branches have been opened per year?", "answer": "SELECT date_trunc('YEAR', start_date) as year,\n       count(*) as n_branches_opened\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  start_date >= '2018-01-01'\nGROUP BY year\nORDER BY year asc;"}, {"question": "What are the top 10 customers in terms of total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 10"}, {"question": "Compare the Bank Prime Loan Rate to the Federal Funds Effective Rate and the spread between the two", "answer": "SELECT a.date,\r\n       a.value as bank_prime_loan_rate,\r\n       b.value as federal_funds_effective_rate,\r\n       a.value - b.value as spread\r\nFROM   financial_data_package.cybersyn.financial_fred_timeseries a join financial_data_package.cybersyn.financial_fred_timeseries b\r\n        ON a.date = b.date\r\nWHERE  a.variable_name like '%Bank Prime Loan Rate%'\r\n   and b.variable_name = 'Federal Funds Effective Rate'\r\nORDER BY a.date;"}, {"question": "Which is the zip code with the most number of bank branches, excluding none values?", "answer": "SELECT id_zip,\n       count(*) as branch_count\nFROM   financial_data_package.cybersyn.financial_branch_entities\nWHERE  end_date is null\n   and id_zip is not null\nGROUP BY id_zip\nORDER BY branch_count desc limit 1;"}, {"question": "Sample 10 states and compare the states mean monthly not seasonally adjusted unemployment rates in 2008-2010 versus 2020-2022", "answer": "with unemployment_rate_2008_2010 as (SELECT geo_name,\r\n                                            avg(value) as avg_unemployment_rate\r\n                                     FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\r\n                                             ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\r\n                                     WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n                                        and date between '2008-01-01'\r\n                                        and '2010-12-31'\r\n                                        and level = 'State'\r\n                                     GROUP BY geo_name), unemployment_rate_2020_2022 as (SELECT geo_name,\r\n                                                           avg(value) as avg_unemployment_rate\r\n                                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries join financial_data_package.cybersyn.geography_index\r\n                                                            ON bureau_of_labor_statistics_employment_timeseries.geo_id = geography_index.geo_id\r\n                                                    WHERE  variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n                                                       and date between '2020-01-01'\r\n                                                       and '2022-12-31'\r\n                                                       and level = 'State'\r\n                                                    GROUP BY geo_name), sample_states as (SELECT geo_name\r\n                                      FROM   financial_data_package.cybersyn.geography_index\r\n                                      SAMPLE (10)\r\n                                      WHERE  level = 'State')\r\n-- 10% random sample of states\r\nSELECT sample_states.geo_name,\r\n       u1.avg_unemployment_rate as avg_unemployment_rate_2008_2010,\r\n       u2.avg_unemployment_rate as avg_unemployment_rate_2020_2022\r\nFROM   sample_states\r\n    LEFT JOIN unemployment_rate_2008_2010 u1\r\n        ON sample_states.geo_name = u1.geo_name\r\n    LEFT JOIN unemployment_rate_2020_2022 u2\r\n        ON sample_states.geo_name = u2.geo_name\r\nORDER BY sample_states.geo_name;"}, {"question": "Compare the Federal Funds Effective Rate to the rates set by the Bank of England, and the central banks of Japan and Mexico", "answer": "SELECT b.date,\r\n       a.value as boe_rate,\r\n       c.value as boj_rate,\r\n       d.value as banxico_rate,\r\n       b.value as federal_funds_rate\r\nFROM   financial_data_package.cybersyn.financial_fred_timeseries a join financial_data_package.cybersyn.financial_fred_timeseries b\r\n        ON a.date = b.date join financial_data_package.cybersyn.financial_fred_timeseries c\r\n        ON a.date = c.date join financial_data_package.cybersyn.financial_fred_timeseries d\r\n        ON a.date = d.date\r\nWHERE  a.variable_name like '%Bank of England%'\r\n   and b.variable_name = 'Federal Funds Effective Rate'\r\n   and c.variable_name like '%Bank of Japan%'\r\n   and d.variable_name like '%Mexico%'\r\nORDER BY b.date;"}, {"question": "What are the first 10 rows in the FINANCIAL_FRED_ATTRIBUTES table?", "answer": "SELECT VARIABLE, VA<PERSON><PERSON>LE_NAME, VARIABLE_GROUP, SEASONALLY_ADJUSTED, FREQUENCY, UNIT, MEASUREMENT_METHOD\r\nFROM FINANCIAL_DATA_PACKAGE.CYBERSYN.FINANCIAL_FRED_ATTRIBUTES\r\nLIMIT 10"}, {"question": "Which are the top 10 banks by the number of branches that have been open for more than 40 years?", "answer": "with branch_lifetimes as (SELECT id_rssd_parent,\n                                 count(*) as branch_lifetime\n                          FROM   financial_data_package.cybersyn.financial_branch_entities\n                          WHERE  start_date < '2022-01-01'\n                             and (end_date >= '2022-01-01'\n                              or end_date is null)\n                          GROUP BY id_rssd_parent)\nSELECT name,\n       branch_lifetime\nFROM   branch_lifetimes join financial_data_package.cybersyn.financial_institution_entities\n        ON financial_institution_entities.id_rssd = branch_lifetimes.id_rssd_parent\nWHERE  branch_lifetime >= 40\nORDER BY branch_lifetime desc limit 10;"}, {"question": "Since 2020, how many new branches have banks opened by state and by bank type?", "answer": "with new_branches as (SELECT state_abbreviation,\n                             category,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY state_abbreviation, category)\nSELECT state_abbreviation,\n       category,\n       new_branch_count\nFROM   new_branches\nORDER BY state_abbreviation, category, new_branch_count desc;"}, {"question": "What is the 3 month running average of each states' mean monthly not seasonally adjusted unemployment rates between 2007 to 2022?", "answer": "with monthly_unemployment_rates as (SELECT g.geo_name,\r\n                                           b.date,\r\n                                           avg(b.value) as avg_unemployment_rate\r\n                                    FROM   financial_data_package.cybersyn.bureau_of_labor_statistics_employment_timeseries as b join financial_data_package.cybersyn.geography_index as g\r\n                                            ON b.geo_id = g.geo_id\r\n                                    WHERE  b.variable_name like '%Unemployment Rate%Not seasonally adjusted%'\r\n                                       and b.date between '2007-01-01'\r\n                                       and '2022-12-31'\r\n                                       and g.level = 'State'\r\n                                    GROUP BY g.geo_name, b.date)\r\nSELECT geo_name AS STATE,\r\n       date,\r\n       100*avg(avg_unemployment_rate) OVER (PARTITION BY geo_name\r\n                                        ORDER BY date rows between 1 preceding and 1 following) as UNEMPLOYMENT_RATE\r\nFROM   monthly_unemployment_rates\r\nORDER BY STATE, date;"}, {"question": "Since 2020, how many new branches have banks opened by state?", "answer": "with new_branches as (SELECT state_abbreviation,\n                             count(*) as new_branch_count\n                      FROM   financial_data_package.cybersyn.financial_branch_entities\n                      WHERE  start_date >= '2020-01-01'\n                      GROUP BY state_abbreviation)\nSELECT state_abbreviation,\n       new_branch_count\nFROM   new_branches\nORDER BY new_branch_count desc;"}, {"question": "Using the BLS Price time series, calculate the monthly inflation percentage of airline fares using the consumer price index of airline fares ", "answer": "with airline_cpi as (SELECT date_trunc('month', date) as month,\r\n                            value as cpi\r\n                     FROM   financial_data_package.CYBERSYN.BUREAU_OF_LABOR_STATISTICS_PRICE_TIMESERIES\r\n                     WHERE  variable_name ilike 'cpi%airline fares%'), \r\nmonthly_inflation as (\r\nSELECT month,\r\n       (cpi - lag(cpi) OVER (ORDER BY month)) / lag(cpi) OVER (ORDER BY month) * 100 as inflation_percentage\r\nFROM   airline_cpi)\r\nSELECT month,\r\n       inflation_percentage\r\nFROM   monthly_inflation\r\nWHERE  inflation_percentage is not null\r\nORDER BY month;"}, {"question": "Compare the seasonally adjusted Consumer Price Index for all Urban Consumers to the Federal Funds Effective Rate  ", "answer": "SELECT b.date,\n       a.value as cpi,\n       b.value as federal_funds_rate\nFROM   financial_data_package.cybersyn.financial_fred_timeseries a join financial_data_package.cybersyn.financial_fred_timeseries b\n        ON a.date = b.date\nWHERE  a.variable like '%Consumer Price Index%Urban Consumers%Seasonally adjusted%'\n   and b.variable = 'Federal Funds Effective Rate'\nORDER BY b.date;"}, {"question": "Create a cumulative frequency distribution of the number of complaints received by CFPB against the financial institution with the most number of complaints in 2022 on a weekly basis", "answer": "with fiwmc_2022_complaints as (SELECT count(*) as n_complaints\n                               FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                               WHERE  company = (SELECT company\n                                                 FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                                                 WHERE  date_received between '2022-01-01'\n                                                    and '2022-12-31'\n                                                 GROUP BY company\n                                                 ORDER BY count(*) desc limit 1) and date_received between '2022-01-01' and '2022-12-31')\nSELECT date_trunc('week', date_received) as week,\n       count(*) as frequency,\n       sum(count(*)) OVER (ORDER BY week asc) as cumulative_frequency\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nWHERE  company = (SELECT company\n                  FROM   financial_data_package.cybersyn.financial_cfpb_complaint\n                  WHERE  date_received between '2022-01-01'\n                     and '2022-12-31'\n                  GROUP BY company\n                  ORDER BY count(*) desc limit 1) and date_received between '2022-01-01' and '2022-12-31'\nGROUP BY week\nORDER BY week asc"}, {"question": "What are the names of the top 3 customers with the highest total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 3"}, {"question": "What are the total sales by country for the top 10 countries in descending order?", "answer": "SELECT\n  n.n_name AS country_name,\n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY\n  country_name\nORDER BY\n  total_sales DESC\nLIMIT 10;"}, {"question": "Calculate the monthly inflation rate as a percentage from the consumer price index for urban consumers", "answer": "with cpi_data as (SELECT date,\r\n                         value,\r\n                         lag(value) OVER (ORDER BY date) as prev_value\r\n                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\r\n                  WHERE  variable_name ilike '%Consumer Price Index%Urban Consumers%')\r\nSELECT date,\r\n       value as cpi,\r\n       (value - prev_value) / prev_value * 100 as monthly_inflation_rate_pct\r\nFROM   cpi_data\r\nWHERE  prev_value is not null\r\nORDER BY date;"}, {"question": "What is the average discount for each region?", "answer": "SELECT\n  r.r_name AS region_name,\n  AVG(l.l_discount) AS avg_discount\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nGROUP BY\n  region_name;"}, {"question": "Calculate the annualized monthly inflation rate as a percentage since 1980 based on the consumer price index for urban consumers", "answer": "with cpi_data as (SELECT date,\n                         value,\n                         lag(value) OVER (ORDER BY date) as prev_value\n                  FROM   financial_data_package.cybersyn.financial_fred_timeseries\n                  WHERE  variable_name ilike '%Consumer Price Index%Urban Consumers%'\n                     and date >= '1980-01-01'), subquery as (SELECT date,\n                                               value,\n                                               lag(value) OVER (ORDER BY date) as prev_value,\n                                               lag(date) OVER (ORDER BY date) as prev_date\n                                        FROM   cpi_data)\nSELECT date,\n       (power((value / prev_value), 1.0/12) - 1) * 100 as annualized_inflation_rate\nFROM   subquery\nWHERE  prev_value is not null\nORDER BY date;"}, {"question": "What is the running total of CPFB complaints of each product in 2022?", "answer": "SELECT product,\n       date_received,\n       count(*) OVER (PARTITION BY product\n                      ORDER BY date_received) as running_total\nFROM   financial_data_package.cybersyn.financial_cfpb_complaint\nWHERE  date_received >= '2022-01-01'\n   and date_received < '2023-01-01'\nORDER BY product, date_received"}, {"question": "Since 2021, which cities have seen an increase in branches and by which banks?", "answer": "WITH pre_2021_branches AS (\r\n    SELECT id_rssd_parent,\r\n           city,\r\n           COUNT(*) AS pre_2021_count\r\n    FROM financial_data_package.cybersyn.financial_branch_entities\r\n    WHERE start_date < '2021-01-01'\r\n      AND (end_date >= '2021-01-01' OR end_date IS NULL)\r\n    GROUP BY id_rssd_parent, city\r\n), post_2021_branches AS (\r\n    SELECT id_rssd_parent,\r\n           city,\r\n           COUNT(*) AS post_2021_count\r\n    FROM financial_data_package.cybersyn.financial_branch_entities\r\n    WHERE start_date >= '2021-01-01'\r\n    GROUP BY id_rssd_parent, city\r\n)\r\nSELECT post_2021_branches.city,\r\n       bank_names.name,\r\n       post_2021_count - pre_2021_count AS branch_change\r\nFROM post_2021_branches\r\nINNER JOIN financial_data_package.cybersyn.financial_institution_entities AS bank_names ON (post_2021_branches.id_rssd_parent = bank_names.id_rssd)\r\nLEFT JOIN pre_2021_branches ON (post_2021_branches.id_rssd_parent = pre_2021_branches.id_rssd_parent AND post_2021_branches.city = pre_2021_branches.city)\r\nWHERE post_2021_count > pre_2021_count\r\nORDER BY branch_change DESC;"}]