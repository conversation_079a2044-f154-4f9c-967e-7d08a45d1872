name: Basic Integration Tests

on:
  push:
    branches:
      - main

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"
    - name: Install pip
      run: |
        python -m pip install --upgrade pip
        pip install tox
    - name: Run tests
      env:
        PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION: python
        VANNA_API_KEY: ${{ secrets.VANNA_API_KEY }}
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
        SNOWFLAKE_ACCOUNT: ${{ secrets.SNOWFLAKE_ACCOUNT }}
        SNOWFLAKE_USERNAME: ${{ secrets.SN<PERSON>FLAKE_USERNAME }}
        SN<PERSON>FLAKE_PASSWORD: ${{ secrets.SNOWFLAKE_PASSWORD }}
      run: tox -e py310
