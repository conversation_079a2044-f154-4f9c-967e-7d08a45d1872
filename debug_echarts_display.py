#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
����ECharts��ʾ����
�𲽼��ÿ������
"""

import json
import pandas as pd
import logging

# ������־
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_echarts_config():
    """���Ի���ECharts��������"""
    print("=" * 60)
    print("����1: ����ECharts��������")
    print("=" * 60)
    
    # ��������
    test_data = [
        {"��������": "��������", "���(��Ԫ)": 7731.07},
        {"��������": "�Ϻ�����", "���(��Ԫ)": 9892.58},
        {"��������": "�Ͼ�����", "���(��Ԫ)": 5368.80},
        {"��������": "�Ϸʷ���", "���(��Ԫ)": 8017.85}
    ]
    
    df = pd.DataFrame(test_data)
    print(f"������״: {df.shape}")
    print(f"����: {list(df.columns)}")
    print(f"��������: {df.dtypes.to_dict()}")
    
    # �ֶ���������
    numeric_cols = df.select_dtypes(include=["number"]).columns.tolist()
    categorical_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()
    
    print(f"��ֵ��: {numeric_cols}")
    print(f"������: {categorical_cols}")
    
    if len(numeric_cols) >= 1 and len(categorical_cols) >= 1:
        categories = df[categorical_cols[0]].astype(str).tolist()
        values = df[numeric_cols[0]].tolist()
        
        print(f"��������: {categories}")
        print(f"��ֵ����: {values}")
        
        config = {
            "title": {
                "text": f"{categorical_cols[0]} vs {numeric_cols[0]}",
                "left": "center"
            },
            "tooltip": {
                "trigger": "axis"
            },
            "xAxis": {
                "type": "category",
                "data": categories
            },
            "yAxis": {
                "type": "value",
                "name": numeric_cols[0]
            },
            "series": [{
                "name": numeric_cols[0],
                "type": "bar",
                "data": values,
                "itemStyle": {
                    "color": "#5470c6"
                }
            }]
        }
        
        config_json = json.dumps(config, ensure_ascii=False, indent=2)
        print("���ɵ�ECharts����:")
        print(config_json)
        
        # ��֤JSON��ʽ
        try:
            json.loads(config_json)
            print("? JSON��ʽ��֤ͨ��")
            return config_json
        except json.JSONDecodeError as e:
            print(f"? JSON��ʽ����: {e}")
            return None
    else:
        print("? ���ݸ�ʽ���ʺ�������״ͼ")
        return None

def test_processor_integration():
    """���Դ���������"""
    print("\n" + "=" * 60)
    print("����2: ���������ɲ���")
    print("=" * 60)
    
    try:
        from scene_processor.impl.common_processor import CommonProcessor
        
        # ����������
        scene_config = {"name": "test_scene", "parameters": []}
        processor = CommonProcessor(scene_config)
        
        # ������������
        config = processor.test_echarts_generation()
        
        if config:
            print("? �������������ɳɹ�")
            print("����Ԥ��:")
            print(config[:300] + "..." if len(config) > 300 else config)
            return config
        else:
            print("? ��������������ʧ��")
            return None
            
    except ImportError as e:
        print(f"? ���봦����ʧ��: {e}")
        return None
    except Exception as e:
        print(f"? ����������ʧ��: {e}")
        return None

def test_complete_flow():
    """������������"""
    print("\n" + "=" * 60)
    print("����3: �������̲���")
    print("=" * 60)
    
    try:
        from scene_processor.impl.common_processor import CommonProcessor
        
        # ģ��API���
        api_result = {
            "status": "success",
            "data": [
                {"��������": "��������", "���(��Ԫ)": 7731.07},
                {"��������": "�Ϻ�����", "���(��Ԫ)": 9892.58},
                {"��������": "�Ͼ�����", "���(��Ԫ)": 5368.80},
                {"��������": "�Ϸʷ���", "���(��Ԫ)": 8017.85}
            ]
        }
        
        # ����������
        scene_config = {"name": "test_scene", "parameters": []}
        processor = CommonProcessor(scene_config)
        
        print("����1: ���ɿ��ӻ�����...")
        processor.generate_visualization(api_result, question="���������Ա�")
        
        if processor.visualization:
            print("? ���ӻ��������ɳɹ�")
            print(f"���ó���: {len(processor.visualization)}")
            
            print("\n����2: ִ�п��ӻ�...")
            chart_result = processor.execute_visualization_code(
                processor.visualization, 
                api_result["data"]
            )
            
            if chart_result:
                print("? ���ӻ�ִ�гɹ�")
                print(f"�������: {len(chart_result)}")
                
                # ��֤�����ʽ
                try:
                    result_obj = json.loads(chart_result)
                    print("? ���JSON��ʽ��ȷ")
                    
                    # ���浽�ļ�����ǰ�˲���
                    with open("test_chart_config.json", "w", encoding="utf-8") as f:
                        f.write(chart_result)
                    print("? �����ѱ��浽 test_chart_config.json")
                    
                    return chart_result
                    
                except json.JSONDecodeError as e:
                    print(f"? ���JSON��ʽ����: {e}")
                    return None
            else:
                print("? ���ӻ�ִ��ʧ��")
                return None
        else:
            print("? ���ӻ���������ʧ��")
            return None
            
    except Exception as e:
        print(f"? �������̲���ʧ��: {e}")
        return None

def generate_test_html(chart_config):
    """���ɲ���HTML�ļ�"""
    print("\n" + "=" * 60)
    print("����4: ���ɲ���HTML")
    print("=" * 60)
    
    if not chart_config:
        print("? û����Ч��ͼ������")
        return
    
    try:
        # ��������
        config_obj = json.loads(chart_config)
        
        html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts���Բ���</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .chart-container {{
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            min-height: 400px;
        }}
        #chart {{
            width: 100%;
            height: 400px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>ECharts���Բ���</h1>
        <p>���ǴӺ�����ɵ�ͼ�����ò���</p>
        
        <div class="chart-container">
            <div id="chart"></div>
        </div>
        
        <h3>������Ϣ</h3>
        <pre id="configInfo"></pre>
    </div>

    <script>
        console.log('��ʼ��ʼ��ECharts...');
        
        try {{
            const chartDiv = document.getElementById('chart');
            const chart = echarts.init(chartDiv);
            
            const config = {json.dumps(config_obj, ensure_ascii=False, indent=2)};
            console.log('ͼ������:', config);
            
            // ��ʾ������Ϣ
            document.getElementById('configInfo').textContent = JSON.stringify(config, null, 2);
            
            // ����ͼ��ѡ��
            chart.setOption(config);
            
            console.log('? EChartsͼ���ʼ���ɹ�');
            
            // ��Ӧʽ����
            window.addEventListener('resize', () => {{
                chart.resize();
            }});
            
        }} catch (error) {{
            console.error('? ECharts��ʼ��ʧ��:', error);
            document.getElementById('chart').innerHTML = 
                '<div style="color: red; padding: 20px;">ͼ���ʼ��ʧ��: ' + error.message + '</div>';
        }}
    </script>
</body>
</html>'''
        
        with open("debug_chart_test.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        
        print("? ����HTML������: debug_chart_test.html")
        print("����������д򿪴��ļ��鿴ͼ��Ч��")
        
    except Exception as e:
        print(f"? ���ɲ���HTMLʧ��: {e}")

def main():
    """������"""
    print("? ��ʼECharts��ʾ�������...")
    
    # ����1: ������������
    basic_config = test_basic_echarts_config()
    
    # ����2: ����������
    processor_config = test_processor_integration()
    
    # ����3: ��������
    complete_config = test_complete_flow()
    
    # ����4: ���ɲ���HTML
    final_config = complete_config or processor_config or basic_config
    generate_test_html(final_config)
    
    print("\n" + "=" * 60)
    print("? ���Բ�����ɣ�")
    print("=" * 60)
    
    if final_config:
        print("? ������һ�����Գɹ�����������")
        print("�������ɵ�HTML�ļ�:")
        print("- debug_chart_test.html")
        print("- test_echarts_display.html")
    else:
        print("? ���в��Զ�ʧ���ˣ��������")

if __name__ == "__main__":
    main()
