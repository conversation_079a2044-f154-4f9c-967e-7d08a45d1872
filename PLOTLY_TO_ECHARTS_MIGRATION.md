# Plotly到ECharts迁移指南

## 迁移概述

本次迁移将可视化库从Plotly.js替换为ECharts，主要原因：
- ECharts在中文环境下表现更好
- 更轻量级，加载速度更快
- 更好的移动端支持
- 丰富的图表类型和交互效果

## 主要变更

### 1. 前端变更 (demo/user_input.html)

#### JavaScript库替换
```html
<!-- 原来 -->
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>

<!-- 现在 -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
```

#### 图表渲染逻辑
```javascript
// 原来：处理Plotly HTML
if (htmlContent.includes('Plotly')) {
    // 使用iframe渲染Plotly HTML
}

// 现在：处理ECharts配置
if (content.startsWith('{') && content.includes('option')) {
    const chartConfig = JSON.parse(content);
    const chart = echarts.init(chartDiv);
    chart.setOption(chartConfig.option);
}
```

### 2. 后端变更 (scene_processor/impl/common_processor.py)

#### 导入变更
```python
# 移除Plotly导入
# import plotly
# import plotly.express as px
# import plotly.graph_objects as go
```

#### 方法重构

| 原方法 | 新方法 | 功能 |
|--------|--------|------|
| `generate_visualization()` | `generate_visualization()` | 生成ECharts配置而非Plotly代码 |
| `_extract_python_code()` | `_extract_echarts_config()` | 提取JSON配置而非Python代码 |
| `_sanitize_plotly_code()` | `_generate_fallback_echarts_config()` | 生成回退配置 |
| `get_plotly_figure()` | `get_echarts_config()` | 获取ECharts配置 |
| `execute_visualization_code()` | `execute_visualization_code()` | 返回JSON配置而非HTML |

## 技术实现细节

### 1. LLM提示词优化

```python
# 原来：要求生成Plotly代码
user_msg = ("Can you generate the Python plotly code to chart the results...")

# 现在：要求生成ECharts配置
user_msg = ("Can you generate an ECharts configuration (JSON format) to chart...")
```

### 2. 配置格式

#### Plotly输出格式
```python
# 返回HTML字符串
chart_html = fig.to_html(include_plotlyjs='cdn', ...)
return chart_html
```

#### ECharts输出格式
```python
# 返回JSON配置
wrapped_config = {"option": json.loads(final_config)}
return json.dumps(wrapped_config, ensure_ascii=False)
```

### 3. 回退机制

#### Plotly回退
```python
# 使用px.bar(), px.pie()等创建图表对象
fig = px.bar(df, x=categorical_cols[0], y=numeric_cols[0])
```

#### ECharts回退
```python
# 生成JSON配置对象
config = {
    "title": {"text": "图表标题"},
    "xAxis": {"type": "category", "data": categories},
    "yAxis": {"type": "value"},
    "series": [{"type": "bar", "data": values}]
}
```

## 图表类型对应关系

| 数据特征 | Plotly | ECharts |
|----------|--------|---------|
| 分类对比 | `px.bar()` | `{"type": "bar"}` |
| 时间趋势 | `px.line()` | `{"type": "line"}` |
| 比例分布 | `px.pie()` | `{"type": "pie"}` |
| 关系分析 | `px.scatter()` | `{"type": "scatter"}` |
| 单一数值 | `go.Indicator()` | `{"type": "gauge"}` |

## 配置示例

### 柱状图配置
```json
{
  "title": {
    "text": "各分行余额对比",
    "left": "center"
  },
  "tooltip": {
    "trigger": "axis"
  },
  "xAxis": {
    "type": "category",
    "data": ["北京分行", "上海分行", "南京分行", "合肥分行"],
    "name": "机构名称"
  },
  "yAxis": {
    "type": "value",
    "name": "余额(万元)"
  },
  "series": [{
    "name": "余额(万元)",
    "type": "bar",
    "data": [7731.07, 9892.58, 5368.80, 8017.85],
    "itemStyle": {
      "color": "#5470c6"
    }
  }],
  "color": ["#5470c6", "#91cc75", "#fac858", "#ee6666"]
}
```

### 饼图配置
```json
{
  "title": {
    "text": "机构名称分布",
    "left": "center"
  },
  "tooltip": {
    "trigger": "item"
  },
  "series": [{
    "name": "机构名称",
    "type": "pie",
    "radius": "50%",
    "data": [
      {"name": "北京分行", "value": 7731.07},
      {"name": "上海分行", "value": 9892.58}
    ]
  }]
}
```

## 优势对比

### ECharts优势
- ? 更好的中文支持
- ? 更小的文件大小
- ? 更快的渲染速度
- ? 更丰富的图表类型
- ? 更好的移动端体验
- ? 更灵活的配置选项

### 注意事项
- ?? 需要重新训练LLM生成ECharts配置
- ?? 配置格式从Python代码变为JSON
- ?? 前端渲染逻辑需要调整

## 测试验证

运行测试脚本验证迁移效果：
```bash
python test_echarts_integration.py
```

测试内容包括：
1. 回退配置生成测试
2. 配置提取功能测试  
3. 完整可视化流程测试
4. 前端渲染示例生成

## 兼容性说明

- ? 保持与现有API接口兼容
- ? 保持数据处理逻辑不变
- ? 保持错误处理机制
- ? 支持智能回退功能

## 后续优化建议

1. **主题支持**：添加暗色/浅色主题切换
2. **交互增强**：利用ECharts的丰富交互功能
3. **动画效果**：添加图表加载和切换动画
4. **响应式设计**：优化移动端显示效果
5. **性能优化**：按需加载图表组件

## 迁移完成检查清单

- [x] 前端JavaScript库替换
- [x] 前端图表渲染逻辑更新
- [x] 后端Plotly导入移除
- [x] 后端可视化生成逻辑重构
- [x] LLM提示词优化
- [x] 回退机制实现
- [x] 测试脚本编写
- [x] 文档更新

? **迁移完成！** 系统现在使用ECharts进行数据可视化。
