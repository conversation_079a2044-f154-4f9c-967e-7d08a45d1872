#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
�����޸�����
����ʵ�����ݵ�ECharts��������
"""

import json
import pandas as pd

def test_actual_data_config():
    """����ʵ�����ݵ���������"""
    print("=" * 60)
    print("�����޸����� - ʵ��������������")
    print("=" * 60)
    
    # ����ʵ������
    actual_data = [
        {"��������": "711100", "��������": "��������", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 7731.07},
        {"��������": "731109", "��������": "�Ϻ�����", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 9892.58},
        {"��������": "703220", "��������": "�Ͼ�����", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 5368.80},
        {"��������": "703260", "��������": "�Ϸʷ���", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 2693.49},
        {"��������": "703400", "��������": "���ݷ���", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 1214.51}
    ]
    
    df = pd.DataFrame(actual_data)
    print(f"������״: {df.shape}")
    print(f"����: {list(df.columns)}")
    print(f"����Ԥ��:\n{df}")
    
    # ��ȡʵ�ʵķ������ֵ����
    institution_col = "��������"
    amount_col = "���(��Ԫ)"
    
    categories = df[institution_col].tolist()
    values = df[amount_col].tolist()
    
    print(f"\n��ȡ�ķ�������: {categories}")
    print(f"��ȡ����ֵ����: {values}")
    
    # ������ȷ��ECharts����
    config = {
        "title": {
            "text": "�����жԹ�������Ա�",
            "left": "center",
            "textStyle": {
                "fontSize": 16,
                "color": "#333"
            }
        },
        "tooltip": {
            "trigger": "axis",
            "axisPointer": {
                "type": "shadow"
            },
            "formatter": "{b}: {c}��Ԫ"
        },
        "grid": {
            "left": "10%",
            "right": "10%",
            "bottom": "15%",
            "top": "20%",
            "containLabel": True
        },
        "xAxis": {
            "type": "category",
            "data": categories,  # ʹ��ʵ�ʵĻ�������
            "axisLabel": {
                "rotate": 0,
                "interval": 0,
                "fontSize": 12
            },
            "axisTick": {
                "alignWithLabel": True
            }
        },
        "yAxis": {
            "type": "value",
            "name": "���(��Ԫ)",
            "nameTextStyle": {
                "fontSize": 12
            },
            "axisLabel": {
                "fontSize": 12,
                "formatter": "{value}"
            }
        },
        "series": [{
            "name": "���(��Ԫ)",
            "type": "bar",
            "data": values,  # ʹ��ʵ�ʵ���ֵ
            "itemStyle": {
                "color": "#5470c6",
                "borderRadius": [4, 4, 0, 0]
            },
            "label": {
                "show": True,
                "position": "top",
                "fontSize": 11,
                "formatter": "{c}��Ԫ"
            },
            "barWidth": "60%"
        }],
        "color": ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de"]
    }
    
    # ���л�ΪJSON
    json_str = json.dumps(config, ensure_ascii=False, indent=2)
    
    print("\n" + "=" * 60)
    print("���ɵ���ȷECharts����:")
    print("=" * 60)
    print(json_str)
    
    # ��֤JSON��ʽ
    try:
        parsed = json.loads(json_str)
        print("\n? JSON��ʽ��֤ͨ��")
        
        # ��������Ƿ���ȷ
        x_data = parsed["xAxis"]["data"]
        y_data = parsed["series"][0]["data"]
        
        print(f"? X������: {x_data}")
        print(f"? Y������: {y_data}")
        
        # ��֤��������
        if all(isinstance(x, str) for x in x_data):
            print("? X������������ȷ���ַ�����")
        else:
            print("? X���������ʹ���")
            
        if all(isinstance(y, (int, float)) for y in y_data):
            print("? Y������������ȷ����ֵ��")
        else:
            print("? Y���������ʹ���")
        
        # ���������ļ�
        with open("final_chart_config.json", "w", encoding="utf-8") as f:
            f.write(json_str)
        print("? �����ѱ��浽 final_chart_config.json")
        
        # ���ɲ���HTML
        generate_final_test_html(json_str)
        
        return json_str
        
    except json.JSONDecodeError as e:
        print(f"? JSON��ʽ����: {e}")
        return None

def generate_final_test_html(config_json):
    """�������ղ���HTML"""
    
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>�����޸�����</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .chart-container {{
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }}
        #chart {{
            width: 100%;
            height: 500px;
        }}
        .status {{
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }}
        .status.success {{
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }}
        .status.error {{
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }}
        .config-info {{
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }}
        h1 {{
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }}
        h3 {{
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>? �����޸�����</h1>
        <p style="text-align: center; color: #666; font-size: 16px;">
            ����ʹ��ʵ���������ɵ�EChartsͼ�����
        </p>
        
        <div class="chart-container">
            <div id="chart"></div>
        </div>
        
        <div id="status" class="status">��ʼ����...</div>
        
        <h3>? ������Ϣ</h3>
        <div class="config-info">{config_json}</div>
        
        <h3>? ������֤</h3>
        <div id="dataValidation" class="config-info">�ȴ���֤...</div>
    </div>

    <script>
        function setStatus(message, type = 'success') {{
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${{type}}`;
            statusDiv.innerHTML = message;
            console.log(`[${{type.toUpperCase()}}] ${{message}}`);
        }}
        
        function validateData(config) {{
            const validation = document.getElementById('dataValidation');
            let report = '';
            
            try {{
                const xData = config.xAxis.data;
                const yData = config.series[0].data;
                
                report += `X������ (��������): ${{JSON.stringify(xData)}}\\n`;
                report += `Y������ (���): ${{JSON.stringify(yData)}}\\n\\n`;
                
                // ��֤��������
                const xValid = Array.isArray(xData) && xData.every(x => typeof x === 'string');
                const yValid = Array.isArray(yData) && yData.every(y => typeof y === 'number');
                
                report += `X���������ͼ��: ${{xValid ? '? ͨ��' : '? ʧ��'}}\\n`;
                report += `Y���������ͼ��: ${{yValid ? '? ͨ��' : '? ʧ��'}}\\n`;
                report += `���ݳ���ƥ��: ${{xData.length === yData.length ? '? ͨ��' : '? ʧ��'}}\\n\\n`;
                
                // ��ʾʵ�ʵ����ݶ�Ӧ��ϵ
                report += '���ݶ�Ӧ��ϵ:\\n';
                for (let i = 0; i < Math.min(xData.length, yData.length); i++) {{
                    report += `  ${{xData[i]}}: ${{yData[i]}}��Ԫ\\n`;
                }}
                
                validation.textContent = report;
                
            }} catch (error) {{
                validation.textContent = `��֤ʧ��: ${{error.message}}`;
            }}
        }}
        
        try {{
            setStatus('? ��ʼ��ʼ��ECharts...', 'success');
            
            // ���ECharts�Ƿ����
            if (typeof echarts === 'undefined') {{
                throw new Error('ECharts��δ����');
            }}
            
            const chartDiv = document.getElementById('chart');
            const chart = echarts.init(chartDiv);
            
            const config = {config_json};
            
            // ��֤����
            validateData(config);
            
            setStatus('? ����ͼ��ѡ��...', 'success');
            chart.setOption(config);
            
            setStatus('? ͼ����Ⱦ�ɹ���������ʾ����', 'success');
            
            // ��Ӧʽ����
            window.addEventListener('resize', () => {{
                chart.resize();
            }});
            
            // ��ӵ���¼�
            chart.on('click', function(params) {{
                alert(`�����: ${{params.name}}\\n��ֵ: ${{params.value}}��Ԫ`);
            }});
            
        }} catch (error) {{
            setStatus(`? ͼ����Ⱦʧ��: ${{error.message}}`, 'error');
            console.error('��ϸ����:', error);
        }}
    </script>
</body>
</html>'''
    
    with open("final_fix_test.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print("? ���ղ���HTML������: final_fix_test.html")

if __name__ == "__main__":
    print("? ��ʼ�����޸�����...")
    
    config = test_actual_data_config()
    
    print("\n" + "=" * 60)
    print("? �����޸�������ɣ�")
    print("=" * 60)
    
    if config:
        print("? �������ɳɹ���")
        print("�������ɵ��ļ�:")
        print("- final_fix_test.html (���ղ���ҳ��)")
        print("- final_chart_config.json (�����ļ�)")
        print("\n��������д� final_fix_test.html �鿴Ч��")
    else:
        print("? ��������ʧ��")
