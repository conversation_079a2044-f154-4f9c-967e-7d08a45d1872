import os
import json
import uuid
import logging
import time
import redis
from datetime import datetime
from typing import Dict, List, Optional, Any
from functools import lru_cache
from datetime import timedelta

from schemas.chat import ChatMessage, SessionResponse
from services.cache_service import AsyncCacheService  # 导入缓存服务
from services.mysql_service import AsyncMysqlService  # 导入MySQL服务

class SessionService:
    """会话服务，管理用户聊天会话"""

    _instance = None

    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = SessionService()
        return cls._instance

    def __init__(self):
        """初始化会话服务"""
        self.logger = logging.getLogger("session_service")

        # 获取项目根目录
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # 确保会话存储目录存在
        self.sessions_dir = os.path.join(self.base_dir, "data", "sessions")
        os.makedirs(self.sessions_dir, exist_ok=True)

        # 初始化日志
        logs_dir = os.path.join(self.base_dir, "logs", "sessions")
        os.makedirs(logs_dir, exist_ok=True)

        file_handler = logging.FileHandler(os.path.join(logs_dir, "sessions.log"))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.INFO)

        # 会话超时时间设置
        self.session_timeout = 24 * 60 * 60  # 会话缓存超时时间（秒）

        # 初始化Redis连接
        try:
            self.redis_client = redis.Redis(host='localhost', port=6379, db=0,
            decode_responses=True, password='123456')
            self.use_redis = True
            self.logger.info("Redis连接成功，将使用Redis缓存会话数据")
        except Exception as e:
            self.use_redis = False
            self.logger.warning(f"Redis连接失败: {e}，将仅使用文件系统存储会话")

        # 会话缓存
        self.session_cache = {}  # 内存缓存
        self.sessions = {}  # 保留作为内存缓存
        self.session_expiry = {}  # 保留用于过期控制
        self.user_sessions = {}  # 用户会话映射

        # 初始化异步缓存服务
        self.cache_service = AsyncCacheService(
            redis_url="redis://:123456@localhost:6379/0",
            default_ttl=self.session_timeout
        )

        # 初始化MySQL服务
        self.mysql_service = AsyncMysqlService.get_instance()

        self.logger.info("初始化异步缓存服务和MySQL服务完成")

    async def create_session(self, user_id: str, belgOrgId: str = None) -> Dict[str, Any]:
        """创建新的会话

        Args:
            user_id: 用户ID
            belgOrgId: 机构代码

        Returns:
            会话信息
        """
        session_id = str(uuid.uuid4())
        created_at = datetime.now()

        session_data = {
            "session_id": session_id,
            "user_id": user_id,
            "belgOrgId": belgOrgId,
            "created_at": created_at.isoformat(),
            "updated_at": created_at.isoformat(),
            "last_active": created_at.isoformat(),
            "messages": []
        }

        # 保存会话到缓存
        self._cache_session(session_data)

        # 异步保存会话到文件
        await self._save_session_async(session_data)

        # 如果使用Redis，异步保存会话到Redis
        if self.use_redis:
            await self._save_to_redis_async(session_data)

            # 更新用户会话映射
            user_sessions_key = f"user_sessions:{user_id}"
            cached_session_ids = await self.cache_service.get(user_sessions_key) or []
            if session_id not in cached_session_ids:
                cached_session_ids.append(session_id)
                await self.cache_service.set(user_sessions_key, cached_session_ids, ttl=30 * 24 * 60 * 60)  # 30天过期

        # 存储会话到内存
        self.sessions[session_id] = session_data

        # 更新用户会话映射
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = []
        self.user_sessions[user_id].append(session_id)

        # 设置会话过期时间
        self.session_expiry[session_id] = time.time() + self.session_timeout

        # 创建MySQL会话记录
        try:
            await self.mysql_service.create_user_chat_session(user_id, session_id)
            self.logger.info(f"会话 {session_id} 已异步保存到MySQL数据库")
        except Exception as e:
            self.logger.error(f"异步保存会话 {session_id} 到MySQL失败: {e}")

        self.logger.info(f"Created new session {session_id} for user {user_id} async")

        return session_data

    def create_session_sync(self, user_id: str, belgOrgId: str = None) -> Dict[str, Any]:
        """同步创建会话（兼容旧代码）

        Args:
            user_id: 用户ID
            belgOrgId: 机构代码

        Returns:
            会话信息
        """
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(self.create_session(user_id, belgOrgId))

    # 保留create_session_async作为create_session的别名，以保持向后兼容性
    create_session_async = create_session

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息

        Args:
            session_id: 会话ID

        Returns:
            会话信息，如果不存在则返回None
        """
        # 首先尝试从内存缓存获取
        if session_id in self.session_cache:
            self.logger.debug(f"Session {session_id} found in memory cache")
            session = self.session_cache[session_id]
            # 更新会话过期时间
            self.session_expiry[session_id] = time.time() + self.session_timeout
            # 更新最后活动时间
            session["last_active"] = datetime.now().isoformat()
            return session

        # 然后尝试从内存字典获取
        if session_id in self.sessions:
            session = self.sessions[session_id]
            # 更新会话过期时间
            self.session_expiry[session_id] = time.time() + self.session_timeout
            # 更新最后活动时间
            session["last_active"] = datetime.now().isoformat()
            return session

        # 然后尝试从Redis获取
        if self.use_redis:
            session_data = self._get_from_redis(session_id)
            if session_data:
                # 更新内存缓存
                self._cache_session(session_data)
                # 更新内存字典
                self.sessions[session_id] = session_data
                # 更新过期时间
                self.session_expiry[session_id] = time.time() + self.session_timeout
                return session_data

        # 最后尝试从文件获取
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")

        if not os.path.exists(session_file):
            self.logger.warning(f"Session {session_id} not found")
            return None

        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
                # 更新缓存
                self._cache_session(session_data)
                # 更新内存字典
                self.sessions[session_id] = session_data
                # 更新过期时间
                self.session_expiry[session_id] = time.time() + self.session_timeout
                # 如果使用Redis，也更新Redis
                if self.use_redis:
                    self._save_to_redis(session_data)
                return session_data
        except Exception as e:
            self.logger.error(f"Failed to load session {session_id}: {e}")
            return None

    # 添加同步获取会话的方法作为异步方法的包装器
    def get_session_sync(self, session_id: str) -> Optional[Dict[str, Any]]:
        """同步获取会话（兼容旧代码）

        Args:
            session_id: 会话ID

        Returns:
            会话信息，如果不存在则返回None
        """
        return self.get_session(session_id)

    async def get_session_async(self, session_id: str) -> Optional[Dict[str, Any]]:
        """异步获取会话信息

        Args:
            session_id: 会话ID

        Returns:
            会话信息，如果不存在则返回None
        """
        # 首先尝试从内存缓存获取
        if session_id in self.session_cache:
            self.logger.debug(f"Session {session_id} found in memory cache")
            session = self.session_cache[session_id]
            # 更新会话过期时间
            self.session_expiry[session_id] = time.time() + self.session_timeout
            # 更新最后活动时间
            session["last_active"] = datetime.now().isoformat()
            return session

        # 然后尝试从内存字典获取
        if session_id in self.sessions:
            session = self.sessions[session_id]
            # 更新会话过期时间
            self.session_expiry[session_id] = time.time() + self.session_timeout
            # 更新最后活动时间
            session["last_active"] = datetime.now().isoformat()
            return session

        # 然后尝试从Redis获取（使用异步方法）
        if self.use_redis:
            session_data = await self._get_from_redis_async(session_id)
            if session_data:
                # 更新内存缓存
                self._cache_session(session_data)
                # 更新内存字典
                self.sessions[session_id] = session_data
                # 更新过期时间
                self.session_expiry[session_id] = time.time() + self.session_timeout
                return session_data

        # 最后尝试从文件获取
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")

        if not os.path.exists(session_file):
            self.logger.warning(f"Session {session_id} not found")
            return None

        try:
            # 使用asyncio.to_thread异步读取文件
            import asyncio

            def read_file():
                with open(session_file, 'r', encoding='utf-8') as f:
                    return json.load(f)

            session_data = await asyncio.to_thread(read_file)

            # 更新缓存
            self._cache_session(session_data)
            # 更新内存字典
            self.sessions[session_id] = session_data
            # 更新过期时间
            self.session_expiry[session_id] = time.time() + self.session_timeout
            # 如果使用Redis，也更新Redis（异步）
            if self.use_redis:
                await self._save_to_redis_async(session_data)
            return session_data
        except Exception as e:
            self.logger.error(f"Failed to load session {session_id}: {e}")
            return None

    async def add_message_async(self, session_id: str, message: ChatMessage) -> bool:
        """异步添加消息到会话

        Args:
            session_id: 会话ID
            message: 聊天消息

        Returns:
            是否成功添加
        """
        # 获取会话数据
        session_data = await self.get_session_async(session_id)

        if not session_data:
            self.logger.warning(f"Cannot add message to non-existent session {session_id}")
            return False

        # 更新最后活动时间
        session_data["last_active"] = datetime.now().isoformat()
        session_data["updated_at"] = datetime.now().isoformat()

        # 创建消息对象
        new_message = {
            "role": message.role,
            "content": message.content,
            "timestamp": datetime.now().isoformat()  # 添加消息时间戳
        }

        # 添加消息到内存中的会话数据
        if "messages" not in session_data:
            session_data["messages"] = []

        session_data["messages"].append(new_message)

        # 使用{user_id}_{session_id}作为键更新Redis缓存
        user_id = session_data.get("user_id", "unknown")
        cache_key = f"{user_id}_{session_id}"

        # 更新整个会话数据，而不是追加单个消息
        await self.cache_service.set(cache_key, session_data, ttl=self.session_timeout)

        # 异步更新内存缓存和持久化存储
        self._cache_session(session_data)  # 内存缓存更新是快速的，可以保持同步
        self.sessions[session_id] = session_data  # 内存字典更新也是快速的

        # 异步保存到文件
        await self._save_session_async(session_data)

        # 如果使用Redis，异步更新Redis
        if self.use_redis:
            await self._save_to_redis_async(session_data)
        try:
            await self.mysql_service.create_user_chat_message(
                session_id, message.role, message.content
            )
            self.logger.info(f"消息已异步保存到MySQL数据库，会话ID: {session_id}")
        except Exception as e:
            self.logger.error(f"异步保存消息到MySQL失败: {e}")

        return True


    async def get_user_sessions_async(self, user_id: str) -> List[Dict[str, Any]]:
        """异步获取用户的所有会话

        Args:
            user_id: 用户ID

        Returns:
            会话列表
        """
        # 尝试从异步缓存获取用户会话列表
        cache_key = f"user_sessions:{user_id}"
        cached_session_ids = await self.cache_service.get(cache_key)

        if cached_session_ids:
            # 从缓存获取各个会话详情
            sessions = []
            for session_id in cached_session_ids:
                session_data = await self.get_session_async(session_id)
                if session_data:
                    # 确保会话数据包含所有必需字段
                    session_data = self._normalize_session_data(session_data)
                    sessions.append(session_data)

            if sessions:
                # 按最后活动时间排序
                sessions.sort(key=lambda x: x.get("last_active", ""), reverse=True)
                return sessions

        # 如果缓存未命中，使用同步方法获取
        sessions = self.get_user_sessions(user_id)

        if sessions:
            # 缓存用户的会话ID列表
            session_ids = [session["session_id"] for session in sessions]
            await self.cache_service.set(cache_key, session_ids)

            # 缓存各个会话详情
            for session in sessions:
                session_id = session["session_id"]
                user_id = session.get("user_id", "unknown")
                await self.cache_service.set(f"{user_id}_{session_id}", session)

        return sessions


    def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户的所有会话

        Args:
            user_id: 用户ID

        Returns:
            会话列表
        """
        sessions = []

        # 首先尝试从Redis获取用户会话列表
        if self.use_redis:
            try:
                user_sessions_key = f"user_sessions:{user_id}"
                session_ids = self.redis_client.smembers(user_sessions_key)

                if session_ids:
                    self.logger.info(f"从Redis获取到用户 {user_id} 的 {len(session_ids)} 个会话ID")

                    # 获取每个会话的详细信息
                    for session_id in session_ids:
                        # 使用{user_id}_{session_id}格式的键获取会话数据
                        cache_key = f"{user_id}_{session_id}"
                        session_json = self.redis_client.get(cache_key)

                        if session_json:
                            try:
                                session_data = json.loads(session_json)
                                sessions.append(session_data)
                            except Exception as e:
                                self.logger.error(f"解析会话JSON失败: {e}")
                        else:
                            # 如果Redis中没有详情，尝试从文件获取
                            session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
                            if os.path.exists(session_file):
                                try:
                                    with open(session_file, 'r', encoding='utf-8') as f:
                                        file_session_data = json.load(f)
                                        sessions.append(file_session_data)
                                except Exception as e:
                                    self.logger.error(f"加载会话文件 {session_file} 失败: {e}")
            except Exception as e:
                self.logger.error(f"从Redis获取用户会话失败: {e}")

        # 如果从Redis没有获取到会话，或者不使用Redis，则从文件系统获取
        if not sessions:
            # 遍历会话目录
            for filename in os.listdir(self.sessions_dir):
                if not filename.endswith('.json'):
                    continue

                try:
                    with open(os.path.join(self.sessions_dir, filename), 'r', encoding='utf-8') as f:
                        session_data = json.load(f)

                        if session_data.get("user_id") == user_id:
                            # 确保会话数据包含所有必需字段
                            session_data = self._normalize_session_data(session_data)
                            sessions.append(session_data)
                except Exception as e:
                    self.logger.error(f"Failed to load session file {filename}: {e}")

        # 按最后活动时间排序
        sessions.sort(key=lambda x: x.get("last_active", ""), reverse=True)

        self.logger.info(f"获取到用户 {user_id} 的 {len(sessions)} 个会话")
        return sessions

    def _normalize_session_data(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化会话数据，确保包含所有必需字段

        Args:
            session_data: 原始会话数据

        Returns:
            标准化后的会话数据
        """
        current_time = datetime.now().isoformat()

        # 确保包含所有必需字段
        normalized = {
            "session_id": session_data.get("session_id", ""),
            "user_id": session_data.get("user_id", ""),
            "belgOrgId": session_data.get("belgOrgId"),
            "created_at": session_data.get("created_at", current_time),
            "updated_at": session_data.get("updated_at", session_data.get("created_at", current_time)),
            "last_active": session_data.get("last_active", session_data.get("updated_at", session_data.get("created_at", current_time))),
            "messages": session_data.get("messages", [])
        }

        return normalized
    async def delete_session_async(self, session_id: str) -> bool:
        """异步删除会话

        Args:
            session_id: 会话ID

        Returns:
            是否成功删除
        """
        # 获取会话信息
        session_data = await self.get_session_async(session_id)

        if not session_data:
            return False

        # 从异步缓存中删除
        user_id = session_data.get("user_id", "unknown")
        await self.cache_service.delete(f"{user_id}_{session_id}")

        # 从用户会话列表缓存中删除
        user_id = session_data.get("user_id")
        if user_id:
            user_sessions_key = f"user_sessions:{user_id}"
            cached_session_ids = await self.cache_service.get(user_sessions_key)
            if cached_session_ids and session_id in cached_session_ids:
                cached_session_ids.remove(session_id)
                await self.cache_service.set(user_sessions_key, cached_session_ids)

        # 使用同步方法删除持久化存储
        return self.delete_session(session_id)

    # 添加同步删除会话方法
    def delete_session(self, session_id: str) -> bool:
        """删除会话

        Args:
            session_id: 会话ID

        Returns:
            是否成功删除
        """
        # 获取会话信息
        session_data = self.get_session(session_id)

        if not session_data:
            return False

        # 从内存缓存中删除
        if session_id in self.session_cache:
            del self.session_cache[session_id]

        # 从内存字典中删除
        if session_id in self.sessions:
            del self.sessions[session_id]

        # 从过期时间字典中删除
        if session_id in self.session_expiry:
            del self.session_expiry[session_id]

        # 从用户会话映射中删除
        user_id = session_data.get("user_id")
        if user_id in self.user_sessions and session_id in self.user_sessions[user_id]:
            self.user_sessions[user_id].remove(session_id)

        # 从Redis中删除
        if self.use_redis:
            try:
                # 查找并删除会话数据
                pattern = f"*_{session_id}"
                matching_keys = self.redis_client.keys(pattern)

                for session_key in matching_keys:
                    self.redis_client.delete(session_key)

                # 从用户会话集合中删除
                if user_id:
                    user_sessions_key = f"user_sessions:{user_id}"
                    self.redis_client.srem(user_sessions_key, session_id)
            except Exception as e:
                self.logger.error(f"Failed to delete session from Redis: {e}")

        # 删除会话文件
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
        if os.path.exists(session_file):
            try:
                os.remove(session_file)
            except Exception as e:
                self.logger.error(f"Failed to delete session file: {e}")
                return False

        return True

    def _cache_session(self, session_data: Dict[str, Any]) -> None:
        """将会话数据缓存到内存

        Args:
            session_data: 会话数据
        """
        session_id = session_data["session_id"]
        self.session_cache[session_id] = session_data

    async def _save_to_redis_async(self, session_data: Dict[str, Any]) -> None:
        """异步保存会话数据到Redis

        Args:
            session_data: 会话数据
        """
        if not self.use_redis:
            return

        try:
            session_id = session_data["session_id"]
            user_id = session_data.get("user_id", "unknown")

            # 使用{user_id}_{session_id}作为键，存储整个会话数据
            cache_key = f"{user_id}_{session_id}"

            # 使用异步缓存服务存储会话数据
            await self.cache_service.set(cache_key, session_data, ttl=30 * 24 * 60 * 60)  # 30天过期

            # 更新用户会话映射
            user_sessions_key = f"user_sessions:{user_id}"
            cached_session_ids = await self.cache_service.get(user_sessions_key) or []
            if session_id not in cached_session_ids:
                cached_session_ids.append(session_id)
                await self.cache_service.set(user_sessions_key, cached_session_ids, ttl=30 * 24 * 60 * 60)  # 30天过期

            self.logger.debug(f"异步保存会话 {session_id} 到Redis成功")
        except Exception as e:
            self.logger.error(f"异步保存会话到Redis失败: {e}")


    def _save_to_redis(self, session_data: Dict[str, Any]) -> None:
        """同步保存会话数据到Redis（兼容旧代码）

        Args:
            session_data: 会话数据
        """
        if not self.use_redis:
            return

        try:
            session_id = session_data["session_id"]
            user_id = session_data.get("user_id", "unknown")

            # 使用{user_id}_{session_id}作为键，存储整个会话数据
            cache_key = f"{user_id}_{session_id}"

            # 将整个会话数据序列化为JSON字符串
            self.redis_client.set(cache_key, json.dumps(session_data, ensure_ascii=False))
            self.redis_client.expire(cache_key, self.session_timeout)  # 使用会话超时时间

            self.logger.debug(f"保存会话 {session_id} 到Redis成功")
        except Exception as e:
            self.logger.error(f"保存会话到Redis失败: {e}")


    async def _get_from_redis_async(self, session_id: str) -> Optional[Dict[str, Any]]:
        """异步从Redis获取会话数据

        Args:
            session_id: 会话ID

        Returns:
            会话数据，如果不存在则返回None
        """
        if not self.use_redis:
            return None

        try:
            # 首先尝试从用户会话列表查找用户ID
            user_sessions_keys = await self.cache_service.redis.keys("user_sessions:*")
            for user_sessions_key in user_sessions_keys:
                user_id = user_sessions_key.decode().split(":")[1]
                cache_key = f"{user_id}_{session_id}"

                # 检查这个键是否存在
                exists = await self.cache_service.redis.exists(cache_key)
                if exists:
                    # 使用AsyncCacheService的get方法获取会话数据
                    session_data = await self.cache_service.get(cache_key)
                    if session_data:
                        return session_data

            # 如果没有找到，尝试直接使用模式查找
            pattern = f"*_{session_id}"
            matching_keys = await self.cache_service.redis.keys(pattern)
            if matching_keys:
                cache_key = matching_keys[0].decode()
                session_data = await self.cache_service.get(cache_key)
                if session_data:
                    return session_data

            # 最后尝试从session:前缀的键中获取
            session_key = f"session:{session_id}"
            exists = await self.cache_service.redis.exists(session_key)
            if exists:
                # 直接获取字符串类型的会话数据
                session_json = await self.cache_service.redis.get(session_key)
                if session_json:
                    try:
                        session_data = json.loads(session_json)
                        return session_data
                    except json.JSONDecodeError as e:
                        self.logger.error(f"解析会话JSON失败: {e}")
                        return None

            return None
        except Exception as e:
            self.logger.error(f"异步从Redis获取会话失败: {e}")
            return None


    def _get_from_redis(self, session_id: str) -> Optional[Dict[str, Any]]:
        """同步从Redis获取会话数据（兼容旧代码）

        Args:
            session_id: 会话ID

        Returns:
            会话数据，如果不存在则返回None
        """
        if not self.use_redis:
            return None

        try:
            # 首先尝试查找所有以session_id结尾的键
            pattern = f"*_{session_id}"
            matching_keys = self.redis_client.keys(pattern)

            if matching_keys:
                # 使用找到的第一个键（应该只有一个）
                session_key = matching_keys[0]

                # 检查键的类型
                key_type = self.redis_client.type(session_key)

                if key_type == "string":
                    # 如果是字符串类型，直接获取并解析JSON
                    session_json = self.redis_client.get(session_key)
                    if session_json:
                        return json.loads(session_json)
                elif key_type == "list":
                    # 如果是列表类型，使用旧的解析逻辑
                    all_items = self.redis_client.lrange(session_key, 0, -1)
                    if not all_items:
                        return None

                    # 解析第一个元素（基本会话信息）
                    session_data = json.loads(all_items[0])

                    # 合并所有消息
                    all_messages = []
                    for i in range(1, len(all_items)):
                        try:
                            item_data = json.loads(all_items[i])
                            if "messages" in item_data and isinstance(item_data["messages"], list):
                                all_messages.extend(item_data["messages"])
                        except Exception as e:
                            self.logger.error(f"解析消息项失败: {e}")

                    # 替换会话数据中的消息列表
                    session_data["messages"] = all_messages
                    return session_data

            return None
        except Exception as e:
            self.logger.error(f"从Redis获取会话失败: {e}")
            return None


    async def _save_session_async(self, session_data: Dict[str, Any]) -> None:
        """异步保存会话到文件

        Args:
            session_data: 会话数据
        """
        session_id = session_data["session_id"]
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")

        try:
            # 使用asyncio.to_thread包装文件I/O操作
            import asyncio
            await asyncio.to_thread(self._save_session_sync, session_data)
        except Exception as e:
            self.logger.error(f"Failed to save session async {session_id}: {e}")

    def _save_session_sync(self, session_data: Dict[str, Any]) -> None:
        """同步保存会话到文件（内部使用）

        Args:
            session_data: 会话数据
        """
        session_id = session_data["session_id"]
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")

        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)

    def _save_session(self, session_data: Dict[str, Any]) -> None:
        """保存会话到文件（兼容旧代码）

        Args:
            session_data: 会话数据
        """
        session_id = session_data["session_id"]
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")

        try:
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)

            # 异步更新缓存
            import asyncio
            asyncio.create_task(self._save_to_redis_async(session_data))
        except Exception as e:
            self.logger.error(f"Failed to save session {session_id}: {e}")

    def cleanup_old_sessions(self, days_threshold: int = 60) -> int:
        """清理超过指定天数的旧会话

        Args:
            days_threshold: 天数阈值，默认60天

        Returns:
            清理的会话数量
        """
        threshold_date = datetime.now() - timedelta(days=days_threshold)
        cleaned_count = 0

        # 清理内存缓存
        for session_id in list(self.session_cache.keys()):
            session = self.session_cache.get(session_id)
            if session:
                last_active = datetime.fromisoformat(session.get("last_active", ""))
                if last_active < threshold_date:
                    del self.session_cache[session_id]
                    # 同时清理内存字典
                    if session_id in self.sessions:
                        del self.sessions[session_id]
                    # 清理过期时间
                    if session_id in self.session_expiry:
                        del self.session_expiry[session_id]
                    cleaned_count += 1

        # 清理内存字典中的过期会话
        current_time = time.time()
        expired_sessions = []

        for session_id, expiry_time in self.session_expiry.items():
            if current_time > expiry_time:
                expired_sessions.append(session_id)

        for session_id in expired_sessions:
            # 获取用户ID
            session = self.sessions.get(session_id)
            if session:
                user_id = session.get("user_id")
                if user_id and user_id in self.user_sessions:
                    # 从用户会话列表中移除
                    if session_id in self.user_sessions[user_id]:
                        self.user_sessions[user_id].remove(session_id)

            # 删除会话
            if session_id in self.sessions:
                del self.sessions[session_id]
            if session_id in self.session_expiry:
                del self.session_expiry[session_id]
            if session_id in self.session_cache:
                del self.session_cache[session_id]
            cleaned_count += 1

        # 如果使用Redis，清理Redis中的过期会话
        if self.use_redis:
            try:
                # 获取所有会话
                session_keys = self.redis_client.keys("*_*")

                for session_key in session_keys:
                    # 从列表中获取第一个元素（基本会话信息）
                    all_items = self.redis_client.lrange(session_key, 0, 0)
                    if not all_items:
                        continue

                    try:
                        # 解析会话数据
                        session_data = json.loads(all_items[0])
                        if "last_active" in session_data:
                            last_active = datetime.fromisoformat(session_data["last_active"])

                            if last_active < threshold_date:
                                # 获取会话ID
                                session_id = session_key.split("_")[-1]

                            # 获取用户ID
                            user_id = session_data.get("user_id")

                            # 删除会话
                            self.redis_client.delete(session_key)

                            # 从用户会话映射中删除
                            if user_id:
                                user_sessions_key = f"user:{user_id}:sessions"
                                self.redis_client.srem(user_sessions_key, session_id)

                            cleaned_count += 1
                    except Exception as e:
                        self.logger.error(f"发生未知错误: {e}")
            except Exception as e:
                self.logger.error(f"Failed to cleanup Redis sessions: {e}")

        # 遍历会话目录
        for filename in os.listdir(self.sessions_dir):
            if not filename.endswith('.json'):
                continue

            try:
                file_path = os.path.join(self.sessions_dir, filename)
                with open(file_path, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)

                last_active = datetime.fromisoformat(session_data.get("last_active", ""))

                # 如果会话超过阈值天数未活动，则删除
                if last_active < threshold_date:
                    os.remove(file_path)
                    cleaned_count += 1
                    self.logger.info(f"Cleaned up old session: {filename}")
            except Exception as e:
                self.logger.error(f"Failed to process session file {filename} during cleanup: {e}")

        return cleaned_count

    @lru_cache(maxsize=100)
    def get_cached_session_info(self, session_id):
        """获取缓存的会话基本信息（不包含消息）"""
        session = self.sessions.get(session_id)
        if not session:
            return None

        # 返回会话基本信息（不包含消息，减少内存占用）
        return {
            "session_id": session["session_id"],
            "user_id": session["user_id"],
            "belgOrgId": session.get("belgOrgId"),
            "created_at": session["created_at"],
            "updated_at": session.get("updated_at", ""),
            "last_active": session["last_active"]
        }

    def invalidate_session_cache(self, session_id):
        """使会话缓存失效"""
        if hasattr(self.get_cached_session_info, "cache_clear"):
            self.get_cached_session_info.cache_clear()

    def get_session_messages(self, session_id, limit=None, offset=None):
        """获取会话消息，支持分页"""
        session = self.sessions.get(session_id)
        if not session or "messages" not in session:
            return []

        messages = session["messages"]

        # 应用分页
        if offset is not None and limit is not None:
            messages = messages[offset:offset+limit]
        elif limit is not None:
            messages = messages[-limit:]

        return messages

    def get_session_message_count(self, session_id):
        """获取会话消息数量"""
        session = self.sessions.get(session_id)
        if not session or "messages" not in session:
            return 0

        return len(session["messages"])

    async def delete_session_async(self, session_id: str) -> bool:
        """异步删除会话

        Args:
            session_id: 会话 ID

        Returns:
            是否成功删除
        """
        try:
            # 从内存中删除会话
            if session_id in self.sessions:
                session_data = self.sessions[session_id]
                user_id = session_data.get("user_id")

                # 从会话字典中删除
                del self.sessions[session_id]

                # 从用户会话映射中删除
                if user_id and user_id in self.user_sessions:
                    if session_id in self.user_sessions[user_id]:
                        self.user_sessions[user_id].remove(session_id)

                        # 如果用户没有其他会话，删除用户映射
                        if not self.user_sessions[user_id]:
                            del self.user_sessions[user_id]

                # 从内存缓存中删除
                if session_id in self.session_cache:
                    del self.session_cache[session_id]

                # 从过期时间记录中删除
                if session_id in self.session_expiry:
                    del self.session_expiry[session_id]

                # 如果使用Redis，从 Redis 中删除
                if self.use_redis and self.cache_service:
                    try:
                        # 删除会话数据
                        await self.cache_service.delete(f"{user_id}_{session_id}")

                        # 从用户会话列表中删除
                        if user_id:
                            user_sessions_key = f"user_sessions:{user_id}"
                            cached_session_ids = await self.cache_service.get(user_sessions_key) or []
                            if session_id in cached_session_ids:
                                cached_session_ids.remove(session_id)
                                await self.cache_service.set(user_sessions_key, cached_session_ids, ttl=30 * 24 * 60 * 60)
                    except Exception as e:
                        self.logger.error(f"从 Redis 删除会话失败: {e}")

                # 删除会话文件
                session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
                if os.path.exists(session_file):
                    try:
                        os.remove(session_file)
                        self.logger.info(f"已删除会话文件: {session_file}")
                    except Exception as e:
                        self.logger.error(f"删除会话文件失败: {e}")

                self.logger.info(f"会话 {session_id} 已成功删除")
                return True
            else:
                self.logger.warning(f"会话 {session_id} 不存在")
                return False

        except Exception as e:
            self.logger.error(f"删除会话 {session_id} 时出错: {e}")
            return False

    def save_sessions_to_file(self, file_path="sessions.json"):
        """保存会话到文件"""
        try:
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)

            # 保存会话数据
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump({
                    "sessions": self.sessions,
                    "user_sessions": self.user_sessions
                }, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            self.logger.error(f"保存会话失败: {e}")
            return False

    def load_sessions_from_file(self, file_path="sessions.json"):
        """从文件加载会话"""
        try:
            if not os.path.exists(file_path):
                return False

            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            self.sessions = data.get("sessions", {})
            self.user_sessions = data.get("user_sessions", {})

            # 重新设置会话过期时间
            current_time = time.time()
            for session_id in self.sessions:
                self.session_expiry[session_id] = current_time + self.session_timeout

            return True
        except Exception as e:
            self.logger.error(f"加载会话失败: {e}")
            return False

    def start_auto_save(self, interval=300, file_path="sessions.json"):
        """启动自动保存会话"""
        import threading

        def auto_save():
            while True:
                time.sleep(interval)
                self.save_sessions_to_file(file_path)
                self.logger.info(f"已自动保存会话数据，当前会话数: {len(self.sessions)}")

        # 启动自动保存线程
        save_thread = threading.Thread(target=auto_save, daemon=True)
        save_thread.start()