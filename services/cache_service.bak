# encoding=utf-8
import json
import hashlib
import asyncio
import logging
from typing import Any, Dict, Optional, Union
import redis.asyncio as redis_asyncio  # 替换 aioredis 为 redis.asyncio

class AsyncCacheService:
    """异步缓存服务，用于提高频繁查询的响应速度"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0", default_ttl: int = 3600):
        """
        初始化缓存服务
        
        Args:
            redis_url: Redis连接URL
            default_ttl: 默认缓存过期时间(秒)
        """
        self.redis = None
        self.redis_url = redis_url
        self.default_ttl = default_ttl
        self.logger = logging.getLogger("cache_service")
    
    async def connect(self):
        """建立Redis连接"""
        if self.redis is None:
            try:
                # 使用 redis.asyncio 替代 aioredis
                self.redis = await redis_asyncio.from_url(self.redis_url)
                self.logger.info("成功连接到Redis缓存服务")
            except Exception as e:
                self.logger.error(f"连接Redis失败: {str(e)}")
                # 降级为内存缓存
                self.redis = {}
                self.logger.warning("降级为内存缓存模式")
    
    async def get(self, key: str) -> Optional[Any]:
        """
        从缓存获取数据
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的数据或None(如果不存在)
        """
        await self.connect()
        
        cache_key = self._generate_key(key)
        
        try:
            if isinstance(self.redis, dict):  # 内存缓存模式
                return self.redis.get(cache_key)
            
            data = await self.redis.get(cache_key)
            if data:
                return json.loads(data)
        except Exception as e:
            self.logger.error(f"获取缓存失败: {str(e)}")
        
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        异步设置缓存
        
        Args:
            key: 缓存键
            value: 要缓存的数据
            ttl: 过期时间(秒)，None表示使用默认值
            
        Returns:
            是否成功设置缓存
        """
        await self.connect()
        
        cache_key = self._generate_key(key)
        ttl = ttl if ttl is not None else self.default_ttl
        
        try:
            if isinstance(self.redis, dict):  # 内存缓存模式
                self.redis[cache_key] = value
                # 创建异步任务在ttl秒后删除缓存
                asyncio.create_task(self._delayed_delete(cache_key, ttl))
                return True
            
            serialized = json.dumps(value)
            await self.redis.set(cache_key, serialized, ex=ttl)
            return True
        except Exception as e:
            self.logger.error(f"设置缓存失败: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        await self.connect()
        
        cache_key = self._generate_key(key)
        
        try:
            if isinstance(self.redis, dict):  # 内存缓存模式
                if cache_key in self.redis:
                    del self.redis[cache_key]
                return True
            
            await self.redis.delete(cache_key)
            return True
        except Exception as e:
            self.logger.error(f"删除缓存失败: {str(e)}")
            return False
    
    async def set_if_not_exists(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        仅当键不存在时设置缓存(用于分布式锁)
        
        Args:
            key: 缓存键
            value: 要缓存的数据
            ttl: 过期时间(秒)
            
        Returns:
            是否成功设置
        """
        await self.connect()
        
        cache_key = self._generate_key(key)
        ttl = ttl if ttl is not None else self.default_ttl
        
        try:
            if isinstance(self.redis, dict):  # 内存缓存模式
                if cache_key in self.redis:
                    return False
                self.redis[cache_key] = value
                asyncio.create_task(self._delayed_delete(cache_key, ttl))
                return True
            
            serialized = json.dumps(value)
            # redis.asyncio 使用 nx=True 参数
            return await self.redis.set(cache_key, serialized, ex=ttl, nx=True)
        except Exception as e:
            self.logger.error(f"条件设置缓存失败: {str(e)}")
            return False
    
    async def _delayed_delete(self, key: str, delay: int):
        """内存缓存模式下的延迟删除"""
        await asyncio.sleep(delay)
        if isinstance(self.redis, dict) and key in self.redis:
            del self.redis[key]
    
    def _generate_key(self, key: str) -> str:
        """生成标准化的缓存键"""
        if isinstance(key, dict):
            # 对字典类型的键进行排序，确保相同内容的字典生成相同的键
            key = json.dumps(key, sort_keys=True)
        
        # 使用MD5生成固定长度的键
        return f"intelliq:cache:{hashlib.md5(key.encode()).hexdigest()}"
    
    async def close(self):
        """关闭Redis连接"""
        if self.redis and not isinstance(self.redis, dict):
            await self.redis.close()
            self.redis = None

# if __name__ == '__main__':

#         redisService = AsyncCacheService.get_instance()