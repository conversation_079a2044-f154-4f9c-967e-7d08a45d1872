#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化修复
验证图表在不同主题下的显示效果
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

def test_chart_colors():
    """测试图表颜色配置"""
    
    # 创建测试数据
    test_data = [
        {"belgOrgNm": "北京分行", "amount": 8000, "date": "20250616"},
        {"belgOrgNm": "上海分行", "amount": 7500, "date": "20250616"},
        {"belgOrgNm": "南京分行", "amount": 6800, "date": "20250616"},
        {"belgOrgNm": "合肥分行", "amount": 7200, "date": "20250616"}
    ]
    
    df = pd.DataFrame(test_data)
    
    # 浅色主题颜色
    light_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    # 暗色主题颜色（更亮的颜色）
    dark_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
    
    print("创建浅色主题图表...")
    fig_light = px.bar(df, x='belgOrgNm', y='amount', 
                       color='belgOrgNm',
                       color_discrete_sequence=light_colors,
                       title="各分行昨天对公存款余额趋势 - 浅色主题")
    
    fig_light.update_layout(
        template="plotly_white",
        font=dict(color="black")
    )
    
    print("创建暗色主题图表...")
    fig_dark = px.bar(df, x='belgOrgNm', y='amount', 
                      color='belgOrgNm',
                      color_discrete_sequence=dark_colors,
                      title="各分行昨天对公存款余额趋势 - 暗色主题")
    
    fig_dark.update_layout(
        template="plotly_dark",
        font=dict(color="white"),
        plot_bgcolor="rgba(0,0,0,0.1)",
        paper_bgcolor="rgba(0,0,0,0.1)"
    )
    fig_dark.update_xaxes(gridcolor="rgba(255,255,255,0.2)", tickfont=dict(color="white"))
    fig_dark.update_yaxes(gridcolor="rgba(255,255,255,0.2)", tickfont=dict(color="white"))
    
    # 保存HTML文件用于测试
    fig_light.write_html("test_chart_light.html")
    fig_dark.write_html("test_chart_dark.html")
    
    print("测试图表已生成:")
    print("- test_chart_light.html (浅色主题)")
    print("- test_chart_dark.html (暗色主题)")
    
    return fig_light, fig_dark

def test_fallback_charts():
    """测试回退图表生成"""
    
    # 测试不同类型的数据
    test_cases = [
        {
            "name": "数值+分类数据",
            "data": [
                {"category": "A", "value": 100},
                {"category": "B", "value": 150},
                {"category": "C", "value": 120}
            ]
        },
        {
            "name": "多数值列数据", 
            "data": [
                {"x": 1, "y": 10, "z": 5},
                {"x": 2, "y": 15, "z": 8},
                {"x": 3, "y": 12, "z": 6}
            ]
        },
        {
            "name": "纯分类数据",
            "data": [
                {"type": "类型A", "count": 5},
                {"type": "类型B", "count": 3},
                {"type": "类型C", "count": 7}
            ]
        }
    ]
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    for i, case in enumerate(test_cases):
        df = pd.DataFrame(case["data"])
        print(f"\n测试案例: {case['name']}")
        print(f"数据形状: {df.shape}")
        print(f"列类型: {df.dtypes.to_dict()}")
        
        # 分析数据类型
        numeric_cols = df.select_dtypes(include=["number"]).columns.tolist()
        categorical_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()
        
        print(f"数值列: {numeric_cols}")
        print(f"分类列: {categorical_cols}")
        
        # 生成对应的图表
        if len(numeric_cols) >= 2:
            fig = px.scatter(df, x=numeric_cols[0], y=numeric_cols[1],
                           color_discrete_sequence=colors)
            chart_type = "散点图"
        elif len(numeric_cols) == 1 and len(categorical_cols) >= 1:
            fig = px.bar(df, x=categorical_cols[0], y=numeric_cols[0],
                       color=categorical_cols[0],
                       color_discrete_sequence=colors)
            chart_type = "柱状图"
        elif len(categorical_cols) >= 1 and df[categorical_cols[0]].nunique() < 10:
            fig = px.pie(df, names=categorical_cols[0],
                       color_discrete_sequence=colors)
            chart_type = "饼图"
        else:
            fig = px.line(df, color_discrete_sequence=colors)
            chart_type = "折线图"
            
        print(f"生成图表类型: {chart_type}")
        fig.write_html(f"test_fallback_{i+1}_{chart_type}.html")

if __name__ == "__main__":
    print("开始测试可视化修复...")
    
    # 测试颜色配置
    test_chart_colors()
    
    # 测试回退图表
    test_fallback_charts()
    
    print("\n测试完成！请检查生成的HTML文件以验证图表显示效果。")
