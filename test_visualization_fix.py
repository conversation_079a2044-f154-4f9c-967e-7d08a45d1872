#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
���Կ��ӻ��޸�
��֤ͼ���ڲ�ͬ�����µ���ʾЧ��
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

def test_chart_colors():
    """����ͼ����ɫ����"""
    
    # ������������
    test_data = [
        {"belgOrgNm": "��������", "amount": 8000, "date": "20250616"},
        {"belgOrgNm": "�Ϻ�����", "amount": 7500, "date": "20250616"},
        {"belgOrgNm": "�Ͼ�����", "amount": 6800, "date": "20250616"},
        {"belgOrgNm": "�Ϸʷ���", "amount": 7200, "date": "20250616"}
    ]
    
    df = pd.DataFrame(test_data)
    
    # ǳɫ������ɫ
    light_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    # ��ɫ������ɫ����������ɫ��
    dark_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
    
    print("����ǳɫ����ͼ��...")
    fig_light = px.bar(df, x='belgOrgNm', y='amount', 
                       color='belgOrgNm',
                       color_discrete_sequence=light_colors,
                       title="����������Թ����������� - ǳɫ����")
    
    fig_light.update_layout(
        template="plotly_white",
        font=dict(color="black")
    )
    
    print("������ɫ����ͼ��...")
    fig_dark = px.bar(df, x='belgOrgNm', y='amount', 
                      color='belgOrgNm',
                      color_discrete_sequence=dark_colors,
                      title="����������Թ����������� - ��ɫ����")
    
    fig_dark.update_layout(
        template="plotly_dark",
        font=dict(color="white"),
        plot_bgcolor="rgba(0,0,0,0.1)",
        paper_bgcolor="rgba(0,0,0,0.1)"
    )
    fig_dark.update_xaxes(gridcolor="rgba(255,255,255,0.2)", tickfont=dict(color="white"))
    fig_dark.update_yaxes(gridcolor="rgba(255,255,255,0.2)", tickfont=dict(color="white"))
    
    # ����HTML�ļ����ڲ���
    fig_light.write_html("test_chart_light.html")
    fig_dark.write_html("test_chart_dark.html")
    
    print("����ͼ��������:")
    print("- test_chart_light.html (ǳɫ����)")
    print("- test_chart_dark.html (��ɫ����)")
    
    return fig_light, fig_dark

def test_fallback_charts():
    """���Ի���ͼ������"""
    
    # ���Բ�ͬ���͵�����
    test_cases = [
        {
            "name": "��ֵ+��������",
            "data": [
                {"category": "A", "value": 100},
                {"category": "B", "value": 150},
                {"category": "C", "value": 120}
            ]
        },
        {
            "name": "����ֵ������", 
            "data": [
                {"x": 1, "y": 10, "z": 5},
                {"x": 2, "y": 15, "z": 8},
                {"x": 3, "y": 12, "z": 6}
            ]
        },
        {
            "name": "����������",
            "data": [
                {"type": "����A", "count": 5},
                {"type": "����B", "count": 3},
                {"type": "����C", "count": 7}
            ]
        }
    ]
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    for i, case in enumerate(test_cases):
        df = pd.DataFrame(case["data"])
        print(f"\n���԰���: {case['name']}")
        print(f"������״: {df.shape}")
        print(f"������: {df.dtypes.to_dict()}")
        
        # ������������
        numeric_cols = df.select_dtypes(include=["number"]).columns.tolist()
        categorical_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()
        
        print(f"��ֵ��: {numeric_cols}")
        print(f"������: {categorical_cols}")
        
        # ���ɶ�Ӧ��ͼ��
        if len(numeric_cols) >= 2:
            fig = px.scatter(df, x=numeric_cols[0], y=numeric_cols[1],
                           color_discrete_sequence=colors)
            chart_type = "ɢ��ͼ"
        elif len(numeric_cols) == 1 and len(categorical_cols) >= 1:
            fig = px.bar(df, x=categorical_cols[0], y=numeric_cols[0],
                       color=categorical_cols[0],
                       color_discrete_sequence=colors)
            chart_type = "��״ͼ"
        elif len(categorical_cols) >= 1 and df[categorical_cols[0]].nunique() < 10:
            fig = px.pie(df, names=categorical_cols[0],
                       color_discrete_sequence=colors)
            chart_type = "��ͼ"
        else:
            fig = px.line(df, color_discrete_sequence=colors)
            chart_type = "����ͼ"
            
        print(f"����ͼ������: {chart_type}")
        fig.write_html(f"test_fallback_{i+1}_{chart_type}.html")

if __name__ == "__main__":
    print("��ʼ���Կ��ӻ��޸�...")
    
    # ������ɫ����
    test_chart_colors()
    
    # ���Ի���ͼ��
    test_fallback_charts()
    
    print("\n������ɣ��������ɵ�HTML�ļ�����֤ͼ����ʾЧ����")
