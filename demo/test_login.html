<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 700px;
            margin: 80px auto;
            padding: 30px;
            background: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .chat-section {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .chat-controls {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 14px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-small:hover {
            background: #218838;
        }

        .btn-small.secondary {
            background: #6c757d;
        }

        .btn-small.secondary:hover {
            background: #545b62;
        }

        .history-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .history-panel.open {
            right: 0;
        }

        .history-header {
            padding: 20px;
            background: #007bff;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-content {
            padding: 20px;
        }

        .history-item {
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .history-item:hover {
            background: #f8f9fa;
        }

        .history-item-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }

        .history-item-time {
            font-size: 12px;
            color: #666;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }

        .chat-messages {
            height: 300px;
            border: 1px solid #ddd;
            padding: 10px;
            overflow-y: auto;
            background: white;
            margin-bottom: 10px;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }

        .user-message {
            background: #e3f2fd;
            text-align: right;
        }

        .bot-message {
            background: #f5f5f5;
        }

        .chart-container {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }

        .chart-container iframe {
            width: 100%;
            min-height: 400px;
            border: none;
            border-radius: 4px;
        }

        .chat-input {
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
        }

        .chat-input button {
            width: auto;
            padding: 10px 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能问答系统 - 登录测试</h1>

        <div id="loginForm">
            <div class="form-group">
                <label for="userId">用户ID:</label>
                <input type="text" id="userId" value="123456" placeholder="请输入用户ID">
            </div>

            <div class="form-group">
                <label for="orgId">机构代码:</label>
                <input type="text" id="orgId" value="701111" placeholder="请输入机构代码">
            </div>

            <!-- 添加分行ID输入字段 -->
            <div class="form-group">
                <label for="fstLvlBrchOrgId">分行代码:</label>
                <input type="text" id="fstLvlBrchOrgId" value="703220" placeholder="请输入分行代码（可选，默认为总行）">
            </div>

            <button onclick="testLogin()">登录测试</button>

            <div id="result" class="result"></div>
        </div>

        <div id="chatSection" class="chat-section">
            <div class="chat-header">
                <div>
                    <h3>聊天界面</h3>
                    <div id="userInfo"></div>
                </div>
                <div class="chat-controls">
                    <button class="btn-small" onclick="newSession()">新建会话</button>
                    <button class="btn-small secondary" onclick="showHistory()">历史对话</button>
                </div>
            </div>
            <div id="chatMessages" class="chat-messages">
                <!-- 在chat-messages div后添加反馈按钮 -->
                <div class="message bot-message">系统: 登录成功！可以开始对话了。</div>
                </div>
                <div class="chat-input">
                    <input type="text" id="chatInput" placeholder="输入您的问题..." onkeypress="handleEnter(event)">
                    <button onclick="sendMessage()">发送</button>
                </div>
                <!-- 添加反馈按钮组 -->
                <div class="feedback-container" style="margin-top: 10px; display: none;" id="feedbackContainer">
                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                        <button class="btn-small" onclick="submitFeedback('like')" style="background: #28a745;">👍 有帮助</button>
                        <button class="btn-small" onclick="submitFeedback('dislike')" style="background: #dc3545;">👎 没帮助</button>
                    </div>
                    <div>
                        <textarea id="feedbackText" placeholder="请输入您的反馈意见..." style="width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ddd; margin-bottom: 5px;"></textarea>
                        <button class="btn-small" onclick="submitFeedback('feedback')" style="background: #17a2b8;">提交反馈</button>
                    </div>
                </div>
                <button onclick="logout()" style="margin-top: 10px; background: #6c757d;">登出</button>
        </div>

        <!-- 历史对话面板 -->
        <div id="historyPanel" class="history-panel">
            <div class="history-header">
                <h3>历史对话</h3>
                <button class="close-btn" onclick="hideHistory()">&times;</button>
            </div>
            <div class="history-content">
                <div id="historyList">
                    <div style="text-align: center; color: #666; padding: 20px;">正在加载...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSession = null;

        async function testLogin() {
            const userId = document.getElementById('userId').value.trim();
            const orgId = document.getElementById('orgId').value.trim();
            const fstLvlBrchOrgId = document.getElementById('fstLvlBrchOrgId').value.trim();
            const resultDiv = document.getElementById('result');

            if (!userId || !orgId) {
                showResult('请输入用户ID和机构代码', 'error');
                return;
            }

            try {
                console.log('发送登录请求...');
                const loginData = {
                    user_id: userId,
                    belgOrgId: orgId
                };
                
                // 如果提供了分行代码，则添加到请求中
                if (fstLvlBrchOrgId) {
                    loginData.fstLvlBrchOrgId = fstLvlBrchOrgId;
                }
                
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                console.log('响应状态:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('登录成功:', data);

                    currentSession = {
                        userId: data.user_id,
                        orgId: data.belgOrgId,
                        sessionId: data.session_id,
                        fstLvlBrchOrgId: data.fstLvlBrchOrgId,
                        fstLvlBrchOrgNm: data.fstLvlBrchOrgNm
                    };

                    showResult(`登录成功！会话ID: ${data.session_id}`, 'success');
                    showChatInterface();
                } else {
                    const errorData = await response.json();
                    console.log('登录失败:', errorData);
                    showResult(`登录失败: ${errorData.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                console.error('请求错误:', error);
                showResult(`网络错误: ${error.message}`, 'error');
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        function showChatInterface() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('chatSection').style.display = 'block';
            
            const userInfoText = `当前用户: ${currentSession.userId} | 机构: ${currentSession.orgId}`;
            const branchInfo = currentSession.fstLvlBrchOrgNm ? ` | 分行: ${currentSession.fstLvlBrchOrgNm}` : '';
            
            document.getElementById('userInfo').textContent = userInfoText + branchInfo;
        }

        function logout() {
            currentSession = null;
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('chatSection').style.display = 'none';
            document.getElementById('result').style.display = 'none';
            document.getElementById('chatMessages').innerHTML =
                '<div class="message bot-message">系统: 登录成功！可以开始对话了。</div>';
        }

        function handleEnter(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message || !currentSession) return;

            input.value = '';
            addMessage(message, 'user-message');
            addMessage('正在查询中，请等待...', 'bot-message');

            try {
                const response = await fetch('/multi_question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question: message,
                        format: "html",
                        session_id: currentSession.sessionId,
                        user_id: currentSession.userId,
                        belgOrgId: currentSession.orgId
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    // 移除"正在处理..."消息
                    const messages = document.getElementById('chatMessages');
                    messages.removeChild(messages.lastChild);
                    addMessage(data.answer, 'bot-message');
                } else {
                    const messages = document.getElementById('chatMessages');
                    messages.lastChild.textContent = '处理失败，请重试';
                }
            } catch (error) {
                const messages = document.getElementById('chatMessages');
                messages.lastChild.textContent = '网络错误: ' + error.message;
            }
        }

        // 在addMessage函数中添加显示反馈按钮的逻辑
        function addMessage(content, className) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${className}`;
        
            // 处理包含图表的内容
            if (content.includes('<div class=\'chart-container\'>')) {
                messageDiv.innerHTML = content;
        
                // 处理图表容器中的HTML内容
                const chartContainers = messageDiv.querySelectorAll('.chart-container');
                chartContainers.forEach(container => {
                    // 如果包含HTML内容，创建一个iframe来显示
                    const htmlContent = container.innerHTML;
                    if (htmlContent.includes('<script') || htmlContent.includes('Plotly')) {
                        const iframe = document.createElement('iframe');
                        iframe.style.width = '100%';
                        iframe.style.height = '400px';
                        iframe.style.border = 'none';
        
                        // 将HTML内容写入iframe
                        container.innerHTML = ''; // 清空容器
                        container.appendChild(iframe);
        
                        // 使用 onload 事件确保 iframe 加载完毕
                        iframe.onload = function() {
                            try {
                                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                if (iframeDoc) {
                                    iframeDoc.open();
                                    iframeDoc.write(htmlContent);
                                    iframeDoc.close();
                                } else {
                                    console.error('Failed to get iframe document.');
                                    // 可以选择将原始内容直接放入容器作为后备
                                    // container.innerHTML = htmlContent;
                                }
                            } catch (e) {
                                console.error('Error writing to iframe:', e);
                                // 发生错误时，也可以考虑回退方案
                                // container.innerHTML = htmlContent;
                            }
                        };
                        
                        // 对于某些浏览器，设置 src 为 'about:blank' 可以帮助触发 onload
                        // 如果 htmlContent 自身是一个完整的HTML文档，可以直接设置 iframe.srcdoc = htmlContent;
                        // 但这里 htmlContent 是片段，所以写入 document 的方式更合适。
                        // 为确保 onload 触发，尤其是在 iframe 已添加到 DOM 后动态设置内容时：
                        if (!iframe.contentWindow || !iframe.contentWindow.document) {
                            // 如果直接写入失败，尝试设置一个空的 src 来初始化文档，然后 onload 中再写入
                            // 但通常 appendChild 之后，contentDocument 应该很快可用
                            // 此处主要依赖 appendChild 后 iframe 的正常加载流程
                        }
                    }
                });
            } else {
                messageDiv.innerHTML = content;
            }
        
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            // 如果是机器人回复，显示反馈按钮
            if (className === 'bot-message') {
                // 确保 feedbackContainer 存在
                const feedbackContainer = document.getElementById('feedbackContainer');
                if (feedbackContainer) {
                    feedbackContainer.style.display = 'block';
                } else {
                    console.warn('feedbackContainer not found');
                }
            }
        }
        
        // 添加提交反馈的函数
        async function submitFeedback(type) {
            if (!currentSession) return;
            
            const feedbackText = document.getElementById('feedbackText').value.trim();
            let content = feedbackText;
            
            // 如果是点赞/踩，但没有填写内容，使用默认内容
            if (type === 'like' && !content) {
                content = '回答有帮助';
            } else if (type === 'dislike' && !content) {
                content = '回答没有帮助';
            }
            
            // 如果是文字反馈，必须填写内容
            if (type === 'feedback' && !content) {
                alert('请输入反馈内容');
                return;
            }
            
            try {
                const response = await fetch('/feedback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: currentSession.userId,
                        user_name: '', // 可以从登录信息中获取
                        org_id: currentSession.orgId,
                        content: content,
                        mark: type,
                        session_id: currentSession.sessionId
                    })
                });
                
                if (response.ok) {
                    // 清空反馈文本
                    document.getElementById('feedbackText').value = '';
                    // 隐藏反馈区域
                    document.getElementById('feedbackContainer').style.display = 'none';
                    // 显示成功消息
                    alert('感谢您的反馈！');
                } else {
                    const errorData = await response.json();
                    alert(`提交反馈失败: ${errorData.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('提交反馈错误:', error);
                alert('网络错误: ' + error.message);
            }
        }

        // 新建会话
        async function newSession() {
            if (!currentSession) return;

            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: currentSession.userId,
                        belgOrgId: currentSession.orgId
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    currentSession.sessionId = data.session_id;

                    // 清空聊天记录
                    document.getElementById('chatMessages').innerHTML =
                        '<div class="message bot-message">系统: 新会话已创建！可以开始对话了。</div>';

                    alert(`新会话已创建！会话 ID: ${data.session_id}`);
                } else {
                    alert('创建新会话失败');
                }
            } catch (error) {
                console.error('创建新会话错误:', error);
                alert('网络错误: ' + error.message);
            }
        }

        // 显示历史对话
        async function showHistory() {
            if (!currentSession) return;

            document.getElementById('historyPanel').classList.add('open');
            await loadHistorySessions();
        }

        // 隐藏历史对话
        function hideHistory() {
            document.getElementById('historyPanel').classList.remove('open');
        }

        // 加载历史会话列表
        async function loadHistorySessions() {
            try {
                const response = await fetch(`/sessions?user_id=${currentSession.userId}&belgOrgId=${currentSession.orgId}`);

                if (response.ok) {
                    const sessions = await response.json();
                    displayHistorySessions(sessions);
                } else {
                    document.getElementById('historyList').innerHTML =
                        '<div style="text-align: center; color: #666; padding: 20px;">加载失败</div>';
                }
            } catch (error) {
                console.error('加载历史会话错误:', error);
                document.getElementById('historyList').innerHTML =
                    '<div style="text-align: center; color: #666; padding: 20px;">网络错误</div>';
            }
        }

        // 显示历史会话列表
        function displayHistorySessions(sessions) {
            const historyList = document.getElementById('historyList');

            if (sessions.length === 0) {
                historyList.innerHTML =
                    '<div style="text-align: center; color: #666; padding: 20px;">暂无历史会话</div>';
                return;
            }

            historyList.innerHTML = sessions.map(session => {
                // 获取会话的第一个用户消息作为标题
                let title = '新对话';
                if (session.messages && session.messages.length > 0) {
                    const firstUserMessage = session.messages.find(msg => msg.role === 'user');
                    if (firstUserMessage) {
                        title = firstUserMessage.content.length > 30
                            ? firstUserMessage.content.substring(0, 30) + '...'
                            : firstUserMessage.content;
                    }
                }

                const createdTime = session.created_at
                    ? new Date(session.created_at).toLocaleString()
                    : '未知时间';

                return `
                    <div class="history-item" onclick="loadSession('${session.session_id}')">
                        <div class="history-item-title">${title}</div>
                        <div class="history-item-time">创建时间: ${createdTime}</div>
                        <div style="font-size: 12px; color: #999; margin-top: 3px;">会话 ID: ${session.session_id}</div>
                    </div>
                `;
            }).join('');
        }

        // 加载指定会话
        async function loadSession(sessionId) {
            try {
                const response = await fetch(`/sessions/${sessionId}/messages`);

                if (response.ok) {
                    const messages = await response.json();
                    currentSession.sessionId = sessionId;

                    // 清空当前聊天记录
                    const chatMessages = document.getElementById('chatMessages');
                    chatMessages.innerHTML = '';

                    // 显示历史消息
                    messages.forEach(msg => {
                        if (msg.role === 'user') {
                            addMessage(msg.content, 'user-message');
                        } else {
                            addMessage(msg.content, 'bot-message');
                        }
                    });

                    hideHistory();
                    alert(`已加载会话: ${sessionId}`);
                } else {
                    alert('加载会话失败');
                }
            } catch (error) {
                console.error('加载会话错误:', error);
                alert('网络错误: ' + error.message);
            }
        }
    </script>
</body>
</html>
