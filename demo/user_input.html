<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问答系统</title>
    <!-- 引入Plotly.js用于数据可视化 -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
        }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 600px;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
            background: #fafafa;
        }
        
        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
        }
        
        .user-message {
            background: #e3f2fd;
            margin-left: 20%;
            text-align: right;
        }
        
        .bot-message {
            background: #f8f9fa;
            margin-right: 20%;
            border-left: 4px solid #007bff;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
        }
        
        .input-container input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .input-container button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .input-container button:hover {
            background: #0056b3;
        }
        
        .chart-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .table-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            overflow-x: auto;
        }
        
        .table-container table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .table-container th, .table-container td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
            vertical-align: middle;
        }
        
        .table-container th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }
        
        .table-container tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .table-container tr:hover {
            background-color: #e8f4f8;
        }
        
        .table-container td {
            border-right: 1px solid #dee2e6;
        }
        
        .table-container td:last-child {
            border-right: 1px solid #ddd;
        }
        
        .table-container tr:hover {
            background-color: #f0f0f0;
        }
        
        .chart-container iframe {
            width: 100%;
            min-height: 400px;
            border: none;
            border-radius: 4px;
        }
        
        .loading {
            color: #666;
            font-style: italic;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>智能问答系统</h1>
            <p>请输入您的问题，系统将为您提供详细的回答和数据可视化</p>
        </div>
        
        <div class="chat-container">
            <div id="messages" class="messages">
                <div class="message bot-message">
                    <strong>系统:</strong> 欢迎使用智能问答系统！请输入您的问题。
                </div>
            </div>
            
            <!-- 在input-container后添加反馈容器 -->
            <div class="input-container">
                <input type="text" id="questionInput" placeholder="请输入您的问题..." onkeypress="handleEnter(event)">
                <button onclick="sendQuestion()">发送</button>
            </div>
            
            <!-- 添加反馈容器 -->
            <div class="feedback-container" style="margin-top: 15px; display: none;" id="feedbackContainer">
                <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                    <button class="feedback-btn like" onclick="submitFeedback('like')">👍 有帮助</button>
                    <button class="feedback-btn dislike" onclick="submitFeedback('dislike')">👎 没帮助</button>
                </div>
                <div>
                    <textarea id="feedbackText" placeholder="请输入您的反馈意见..." class="feedback-textarea"></textarea>
                    <button class="feedback-btn submit" onclick="submitFeedback('feedback')">提交反馈</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function handleEnter(event) {
            if (event.key === 'Enter') {
                sendQuestion();
            }
        }
        
        // 在addMessage函数中添加显示反馈按钮的逻辑
        function addMessage(content, isUser = false) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
            
            const sender = isUser ? '您' : '系统';
            
            // 处理包含图表或表格的内容
            if (content.includes('<div class=\'chart-container\'>') || content.includes('<div class=\'table-container\'>')) {
                messageDiv.innerHTML = `<strong>${sender}:</strong> ${content}`;
                
                // 处理图表容器中的HTML内容
                const chartContainers = messageDiv.querySelectorAll('.chart-container');
                chartContainers.forEach(container => {
                    const htmlContent = container.innerHTML;
                    if (htmlContent.includes('<script') || htmlContent.includes('Plotly')) {
                        const iframe = document.createElement('iframe');
                        iframe.style.width = '100%';
                        iframe.style.height = '400px';
                        iframe.style.border = 'none';
                        
                        // 将HTML内容写入iframe
                        container.innerHTML = '';
                        container.appendChild(iframe);
                        
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        iframeDoc.open();
                        iframeDoc.write(htmlContent);
                        iframeDoc.close();
                    }
                });
                
                // 处理表格容器，确保表格正确显示
                const tableContainers = messageDiv.querySelectorAll('.table-container');
                tableContainers.forEach(container => {
                    // 直接显示HTML表格内容，不需要转换
                    const tableContent = container.innerHTML;
                    
                    // 如果已经包含HTML表格，直接显示
                    if (tableContent.includes('<table')) {
                        // 表格已经是HTML格式，无需处理
                        return;
                    }
                    
                    // 如果是markdown格式，转换为HTML表格
                    if (tableContent.includes('|') && !tableContent.includes('<table>')) {
                        const lines = tableContent.trim().split('\n');
                        let htmlTable = '<table class="dataframe table table-striped table-bordered" id="data-table">';
                        
                        // 处理表头
                        if (lines.length > 0) {
                            const headers = lines[0].split('|').filter(cell => cell.trim() !== '');
                            htmlTable += '<thead><tr style="text-align: right;">';
                            headers.forEach(header => {
                                htmlTable += `<th>${header.trim()}</th>`;
                            });
                            htmlTable += '</tr></thead>';
                        }
                        
                        // 跳过分隔行
                        let startRow = 2;
                        if (lines.length > 1 && lines[1].includes('---')) {
                            startRow = 2;
                        } else {
                            startRow = 1;
                        }
                        
                        // 处理数据行
                        htmlTable += '<tbody>';
                        for (let i = startRow; i < lines.length; i++) {
                            const cells = lines[i].split('|').filter(cell => cell.trim() !== '');
                            htmlTable += '<tr>';
                            cells.forEach(cell => {
                                htmlTable += `<td>${cell.trim()}</td>`;
                            });
                            htmlTable += '</tr>';
                        }
                        htmlTable += '</tbody></table>';
                        
                        container.innerHTML = htmlTable;
                    }
                });
            } else {
                messageDiv.innerHTML = `<strong>${sender}:</strong> ${content}`;
            }
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            // 如果是机器人回复，显示反馈按钮
            if (!isUser) {
                document.getElementById('feedbackContainer').style.display = 'block';
            }
        }
        
        // 添加提交反馈的函数
        async function submitFeedback(type) {
            const feedbackText = document.getElementById('feedbackText').value.trim();
            let content = feedbackText;
            
            // 如果是点赞/踩，但没有填写内容，使用默认内容
            if (type === 'like' && !content) {
                content = '回答有帮助';
            } else if (type === 'dislike' && !content) {
                content = '回答没有帮助';
            }
            
            // 如果是文字反馈，必须填写内容
            if (type === 'feedback' && !content) {
                addMessage('<span class="error">请输入反馈内容</span>');
                return;
            }
            
            try {
                const response = await fetch('/feedback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: 'default',
                        user_name: '',
                        org_id: 'default',
                        content: content,
                        mark: type,
                        session_id: 'default'
                    })
                });
                
                if (response.ok) {
                    // 清空反馈文本
                    document.getElementById('feedbackText').value = '';
                    // 隐藏反馈区域
                    document.getElementById('feedbackContainer').style.display = 'none';
                    // 显示成功消息
                    addMessage('<span style="color: #28a745;">感谢您的反馈！</span>');
                } else {
                    const errorData = await response.json();
                    addMessage(`<span class="error">提交反馈失败: ${errorData.error || '未知错误'}</span>`);
                }
            } catch (error) {
                console.error('提交反馈错误:', error);
                addMessage(`<span class="error">网络错误: ${error.message}</span>`);
            }
        }
        
        async function sendQuestion() {
            const input = document.getElementById('questionInput');
            const question = input.value.trim();
            
            if (!question) {
                return;
            }
            
            // 显示用户问题
            addMessage(question, true);
            
            // 清空输入框
            input.value = '';
            
            // 显示加载状态
            addMessage('<span class="loading">正在思考中...</span>');
            
            try {
                const response = await fetch('/multi_question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question: question,
                        format: 'html',
                        user_id: 'default',
                        session_id: 'default'
                    })
                });
                
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                
                const data = await response.json();
                
                // 移除加载消息
                const messages = document.getElementById('messages');
                messages.removeChild(messages.lastChild);
                
                // 显示回答
                const answer = data.formatted_answer || data.answer || '抱歉，没有获取到回答。';
                addMessage(answer);
                
            } catch (error) {
                console.error('发送请求失败:', error);
                
                // 移除加载消息
                const messages = document.getElementById('messages');
                messages.removeChild(messages.lastChild);
                
                // 显示错误消息
                addMessage('<span class="error">抱歉，发生了错误，请稍后重试。</span>');
            }
        }
        
        // 页面加载完成后聚焦到输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('questionInput').focus();
        });
    </script>
</body>
</html>
