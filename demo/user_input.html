<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问答系统</title>
    <!-- 引入ECharts用于数据可视化 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
        }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 600px;
        }
        
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
            background: #fafafa;
        }
        
        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
        }
        
        .user-message {
            background: #e3f2fd;
            margin-left: 20%;
            text-align: right;
        }
        
        .bot-message {
            background: #f8f9fa;
            margin-right: 20%;
            border-left: 4px solid #007bff;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
        }
        
        .input-container input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .input-container button {
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .input-container button:hover {
            background: #0056b3;
        }
        
        .chart-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            min-height: 400px;
        }

        .echarts-chart {
            width: 100%;
            height: 400px;
        }
        
        .table-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            overflow-x: auto;
        }
        
        .table-container table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .table-container th, .table-container td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
            vertical-align: middle;
        }
        
        .table-container th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }
        
        .table-container tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .table-container tr:hover {
            background-color: #e8f4f8;
        }
        
        .table-container td {
            border-right: 1px solid #dee2e6;
        }
        
        .table-container td:last-child {
            border-right: 1px solid #ddd;
        }
        
        .table-container tr:hover {
            background-color: #f0f0f0;
        }
        
        .chart-container iframe {
            width: 100%;
            min-height: 400px;
            border: none;
            border-radius: 4px;
        }
        
        .loading {
            color: #666;
            font-style: italic;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>智能问答系统</h1>
            <p>请输入您的问题，系统将为您提供详细的回答和数据可视化</p>
        </div>
        
        <div class="chat-container">
            <div id="messages" class="messages">
                <div class="message bot-message">
                    <strong>系统:</strong> 欢迎使用智能问答系统！请输入您的问题。
                </div>
            </div>
            
            <!-- 在input-container后添加反馈容器 -->
            <div class="input-container">
                <input type="text" id="questionInput" placeholder="请输入您的问题..." onkeypress="handleEnter(event)">
                <button onclick="sendQuestion()">发送</button>
            </div>
            
            <!-- 添加反馈容器 -->
            <div class="feedback-container" style="margin-top: 15px; display: none;" id="feedbackContainer">
                <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                    <button class="feedback-btn like" onclick="submitFeedback('like')">👍 有帮助</button>
                    <button class="feedback-btn dislike" onclick="submitFeedback('dislike')">👎 没帮助</button>
                </div>
                <div>
                    <textarea id="feedbackText" placeholder="请输入您的反馈意见..." class="feedback-textarea"></textarea>
                    <button class="feedback-btn submit" onclick="submitFeedback('feedback')">提交反馈</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function handleEnter(event) {
            if (event.key === 'Enter') {
                sendQuestion();
            }
        }
        
        // 在addMessage函数中添加显示反馈按钮的逻辑
        function addMessage(content, isUser = false) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
            
            const sender = isUser ? '您' : '系统';
            
            // 处理包含图表或表格的内容
            if (content.includes('<div class=\'chart-container\'>') || content.includes('<div class=\'table-container\'>')) {
                messageDiv.innerHTML = `<strong>${sender}:</strong> ${content}`;
                
                // 处理图表容器中的ECharts配置
                const chartContainers = messageDiv.querySelectorAll('.chart-container');
                console.log(`找到 ${chartContainers.length} 个图表容器`);

                chartContainers.forEach((container, index) => {
                    let content = container.innerHTML.trim();
                    console.log(`容器 ${index} 原始内容:`, content);

                    // 清理HTML标签和多余的空白，只保留JSON内容
                    content = content.replace(/<[^>]*>/g, '').trim();
                    content = content.replace(/\s+/g, ' '); // 压缩空白
                    console.log(`容器 ${index} 清理后内容:`, content);

                    // 更宽松的ECharts配置检查
                    const isEChartsConfig = content.startsWith('{') &&
                                          (content.includes('"title"') ||
                                           content.includes('"series"') ||
                                           content.includes('"xAxis"') ||
                                           content.includes('"data"'));

                    console.log(`容器 ${index} 是否为ECharts配置:`, isEChartsConfig);

                    if (isEChartsConfig) {
                        try {
                            console.log(`开始处理容器 ${index} 的JSON配置...`);

                            // 尝试修复可能的JSON格式问题
                            let jsonContent = content;

                            // 修复常见的JSON格式问题
                            jsonContent = jsonContent.replace(/'/g, '"'); // 单引号替换为双引号
                            jsonContent = jsonContent.replace(/,\s*}/g, '}'); // 移除尾随逗号
                            jsonContent = jsonContent.replace(/,\s*]/g, ']'); // 移除数组尾随逗号

                            // 确保JSON格式正确
                            if (!jsonContent.startsWith('{')) {
                                throw new Error('不是有效的JSON对象');
                            }

                            console.log(`容器 ${index} 修复后的JSON:`, jsonContent.substring(0, 200) + '...');

                            // 解析ECharts配置
                            const chartConfig = JSON.parse(jsonContent);
                            console.log(`容器 ${index} 解析成功:`, chartConfig);

                            // 创建图表容器
                            const chartDiv = document.createElement('div');
                            chartDiv.className = 'echarts-chart';
                            chartDiv.id = `chart_${Date.now()}_${index}`;
                            chartDiv.style.width = '100%';
                            chartDiv.style.height = '400px';
                            chartDiv.style.border = '1px solid #ddd';
                            chartDiv.style.borderRadius = '4px';

                            // 清空容器并添加图表div
                            container.innerHTML = '';
                            container.appendChild(chartDiv);

                            // 延迟初始化确保DOM已渲染
                            setTimeout(() => {
                                try {
                                    console.log(`开始初始化容器 ${index} 的ECharts...`);

                                    // 检查ECharts是否可用
                                    if (typeof echarts === 'undefined') {
                                        throw new Error('ECharts库未加载');
                                    }

                                    // 检查容器是否存在且有尺寸
                                    if (!chartDiv.offsetWidth || !chartDiv.offsetHeight) {
                                        console.warn(`容器 ${index} 尺寸异常，强制设置尺寸`);
                                        chartDiv.style.width = '100%';
                                        chartDiv.style.height = '400px';
                                    }

                                    // 初始化ECharts实例
                                    const chart = echarts.init(chartDiv);
                                    console.log(`容器 ${index} ECharts实例创建成功`);

                                    // 获取配置选项
                                    const option = chartConfig.option || chartConfig;
                                    console.log(`容器 ${index} 使用的ECharts选项:`, option);

                                    // 验证配置的基本结构
                                    if (!option.series || !Array.isArray(option.series) || option.series.length === 0) {
                                        throw new Error('配置中缺少有效的series数据');
                                    }

                                    // 设置图表选项
                                    chart.setOption(option, true); // 第二个参数true表示不合并配置
                                    console.log(`容器 ${index} 图表选项设置成功`);

                                    // 响应式调整
                                    const resizeHandler = () => {
                                        chart.resize();
                                    };
                                    window.addEventListener('resize', resizeHandler);

                                    // 存储chart实例以便后续清理
                                    chartDiv._chartInstance = chart;
                                    chartDiv._resizeHandler = resizeHandler;

                                    console.log(`✅ 容器 ${index} ECharts图表初始化成功`);

                                    // 添加成功标识
                                    const successDiv = document.createElement('div');
                                    successDiv.style.cssText = 'position: absolute; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; z-index: 1000;';
                                    successDiv.textContent = '✅ 图表加载成功';
                                    container.style.position = 'relative';
                                    container.appendChild(successDiv);

                                    // 3秒后移除成功标识
                                    setTimeout(() => {
                                        if (successDiv.parentNode) {
                                            successDiv.parentNode.removeChild(successDiv);
                                        }
                                    }, 3000);

                                } catch (chartError) {
                                    console.error(`❌ 容器 ${index} ECharts初始化失败:`, chartError);
                                    container.innerHTML = `<div style="color: red; padding: 20px; border: 1px solid red; border-radius: 4px; background: #fff5f5;">
                                        <strong>📊 图表渲染失败</strong><br>
                                        <strong>错误信息:</strong> ${chartError.message}<br>
                                        <strong>容器ID:</strong> ${index}<br>
                                        <small>请检查浏览器控制台获取详细信息</small><br>
                                        <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;">重新加载页面</button>
                                    </div>`;
                                }
                            }, 300);

                        } catch (error) {
                            console.error('❌ 解析ECharts配置失败:', error);
                            console.error('原始内容:', content);

                            // 显示更友好的错误信息
                            container.innerHTML = `<div style="color: orange; padding: 20px; border: 1px solid orange; border-radius: 4px;">
                                <strong>图表配置解析失败</strong><br>
                                错误信息: ${error.message}<br>
                                <details style="margin-top: 10px;">
                                    <summary>查看原始配置</summary>
                                    <pre style="background: #f5f5f5; padding: 10px; margin-top: 5px; font-size: 12px; overflow: auto;">${content}</pre>
                                </details>
                            </div>`;
                        }
                    } else {
                        console.log('不是有效的ECharts配置，内容:', content.substring(0, 100));
                    }
                });
                
                // 处理表格容器，确保表格正确显示
                const tableContainers = messageDiv.querySelectorAll('.table-container');
                tableContainers.forEach(container => {
                    // 直接显示HTML表格内容，不需要转换
                    const tableContent = container.innerHTML;
                    
                    // 如果已经包含HTML表格，直接显示
                    if (tableContent.includes('<table')) {
                        // 表格已经是HTML格式，无需处理
                        return;
                    }
                    
                    // 如果是markdown格式，转换为HTML表格
                    if (tableContent.includes('|') && !tableContent.includes('<table>')) {
                        const lines = tableContent.trim().split('\n');
                        let htmlTable = '<table class="dataframe table table-striped table-bordered" id="data-table">';
                        
                        // 处理表头
                        if (lines.length > 0) {
                            const headers = lines[0].split('|').filter(cell => cell.trim() !== '');
                            htmlTable += '<thead><tr style="text-align: right;">';
                            headers.forEach(header => {
                                htmlTable += `<th>${header.trim()}</th>`;
                            });
                            htmlTable += '</tr></thead>';
                        }
                        
                        // 跳过分隔行
                        let startRow = 2;
                        if (lines.length > 1 && lines[1].includes('---')) {
                            startRow = 2;
                        } else {
                            startRow = 1;
                        }
                        
                        // 处理数据行
                        htmlTable += '<tbody>';
                        for (let i = startRow; i < lines.length; i++) {
                            const cells = lines[i].split('|').filter(cell => cell.trim() !== '');
                            htmlTable += '<tr>';
                            cells.forEach(cell => {
                                htmlTable += `<td>${cell.trim()}</td>`;
                            });
                            htmlTable += '</tr>';
                        }
                        htmlTable += '</tbody></table>';
                        
                        container.innerHTML = htmlTable;
                    }
                });
            } else {
                messageDiv.innerHTML = `<strong>${sender}:</strong> ${content}`;
            }
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            // 如果是机器人回复，显示反馈按钮
            if (!isUser) {
                document.getElementById('feedbackContainer').style.display = 'block';
            }
        }
        
        // 添加提交反馈的函数
        async function submitFeedback(type) {
            const feedbackText = document.getElementById('feedbackText').value.trim();
            let content = feedbackText;
            
            // 如果是点赞/踩，但没有填写内容，使用默认内容
            if (type === 'like' && !content) {
                content = '回答有帮助';
            } else if (type === 'dislike' && !content) {
                content = '回答没有帮助';
            }
            
            // 如果是文字反馈，必须填写内容
            if (type === 'feedback' && !content) {
                addMessage('<span class="error">请输入反馈内容</span>');
                return;
            }
            
            try {
                const response = await fetch('/feedback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: 'default',
                        user_name: '',
                        org_id: 'default',
                        content: content,
                        mark: type,
                        session_id: 'default'
                    })
                });
                
                if (response.ok) {
                    // 清空反馈文本
                    document.getElementById('feedbackText').value = '';
                    // 隐藏反馈区域
                    document.getElementById('feedbackContainer').style.display = 'none';
                    // 显示成功消息
                    addMessage('<span style="color: #28a745;">感谢您的反馈！</span>');
                } else {
                    const errorData = await response.json();
                    addMessage(`<span class="error">提交反馈失败: ${errorData.error || '未知错误'}</span>`);
                }
            } catch (error) {
                console.error('提交反馈错误:', error);
                addMessage(`<span class="error">网络错误: ${error.message}</span>`);
            }
        }
        
        async function sendQuestion() {
            const input = document.getElementById('questionInput');
            const question = input.value.trim();
            
            if (!question) {
                return;
            }
            
            // 显示用户问题
            addMessage(question, true);
            
            // 清空输入框
            input.value = '';
            
            // 显示加载状态
            addMessage('<span class="loading">正在思考中...</span>');
            
            try {
                const response = await fetch('/multi_question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question: question,
                        format: 'html',
                        user_id: 'default',
                        session_id: 'default'
                    })
                });
                
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                
                const data = await response.json();
                
                // 移除加载消息
                const messages = document.getElementById('messages');
                messages.removeChild(messages.lastChild);
                
                // 显示回答
                const answer = data.formatted_answer || data.answer || '抱歉，没有获取到回答。';
                addMessage(answer);
                
            } catch (error) {
                console.error('发送请求失败:', error);
                
                // 移除加载消息
                const messages = document.getElementById('messages');
                messages.removeChild(messages.lastChild);
                
                // 显示错误消息
                addMessage('<span class="error">抱歉，发生了错误，请稍后重试。</span>');
            }
        }
        
        // 全局调试函数
        window.debugECharts = function() {
            console.log('=== ECharts调试信息 ===');
            console.log('ECharts版本:', typeof echarts !== 'undefined' ? echarts.version : '未加载');

            const containers = document.querySelectorAll('.chart-container');
            console.log('图表容器数量:', containers.length);

            containers.forEach((container, index) => {
                console.log(`容器 ${index}:`, {
                    innerHTML: container.innerHTML.substring(0, 200) + '...',
                    hasChart: !!container.querySelector('.echarts-chart'),
                    chartInstance: container.querySelector('.echarts-chart')?._chartInstance
                });
            });
        };

        // 页面加载完成后聚焦到输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('questionInput').focus();

            // 检查ECharts加载状态
            console.log('页面加载完成，ECharts状态:', typeof echarts !== 'undefined' ? '已加载' : '未加载');
            if (typeof echarts !== 'undefined') {
                console.log('ECharts版本:', echarts.version);
            } else {
                console.warn('⚠️ ECharts未加载，图表功能将不可用');
            }

            // 添加调试按钮（开发时使用）
            if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                const debugBtn = document.createElement('button');
                debugBtn.textContent = '🔍 调试ECharts';
                debugBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer;';
                debugBtn.onclick = window.debugECharts;
                document.body.appendChild(debugBtn);
            }
        });
    </script>
</body>
</html>
