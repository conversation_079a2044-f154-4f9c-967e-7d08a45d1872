<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问答系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        #chat-container {
            width: 60%;
            max-width: 900px;
            border-radius: 12px;
            box-shadow: 0 0 20px rgba(0,0,0,0.15);
            background: #fff;
            padding: 20px;
            position: relative;
            transition: all 0.3s ease;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .header h2 {
            margin: 0;
            color: #333;
        }
        .control-buttons {
            display: flex;
            gap: 10px;
        }
        #fullscreen-btn, #history-btn {
            cursor: pointer;
            background-color: #007bff;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        #fullscreen-btn:hover, #history-btn:hover {
            background-color: #0056b3;
        }
        #chat-output {
            height: 350px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
            word-wrap: break-word;
        }
        .user-message {
            background-color: #e3f2fd;
            margin-left: auto;
            text-align: right;
            border-bottom-right-radius: 0;
        }
        .bot-message {
            background-color: #f1f1f1;
            margin-right: auto;
            text-align: left;
            border-bottom-left-radius: 0;
        }
        #input-container {
            display: flex;
            margin-top: 10px;
        }
        #chat-input {
            flex-grow: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 0 10px 0 0;
            font-size: 16px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #0056b3;
        }
        .fullscreen {
            width: 100vw;
            height: 100vh;
            max-width: 100vw;
            max-height: 100vh;
            border-radius: 0;
            position: fixed;
            top: 0;
            left: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .fullscreen #chat-output {
            height: calc(100% - 120px);
        }
        #mic-btn {
            background-color: #28a745;
            margin-left: 10px;
        }
        #mic-btn:hover {
            background-color: #218838;
        }
        #history-panel {
            position: fixed;
            top: 0;
            right: -350px;
            width: 350px;
            height: 100%;
            background-color: white;
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            padding: 20px;
            box-sizing: border-box;
            overflow-y: auto;
        }
        #history-panel.active {
            right: 0;
        }
        .history-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        .history-item:hover {
            background-color: #f5f5f5;
        }
        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .close-history {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #333;
        }
        .timestamp {
            font-size: 0.8em;
            color: #888;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div id="chat-container">
        <div class="header">
            <h2>智能问答系统</h2>
            <div class="control-buttons">
                <button id="history-btn">历史记录</button>
                <button id="fullscreen-btn">全屏</button>
            </div>
        </div>
        <div id="chat-output"></div>
        <div id="input-container">
            <input type="text" id="chat-input" placeholder="输入问题..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">发送</button>
            <button id="mic-btn" onclick="toggleMic()">🎤</button>
        </div>
    </div>

    <div id="history-panel">
        <div class="history-header">
            <h3>对话历史记录</h3>
            <button class="close-history" onclick="toggleHistoryPanel()">×</button>
        </div>
        <div id="history-list"></div>
    </div>

    <script>
        var socket;
        var mediaRecorder;
        var recognizing = false;
        var chatHistory = [];
        var currentSessionId = new Date().getTime();

        // 初始化时从本地存储加载历史记录
        function loadHistory() {
            const savedHistory = localStorage.getItem('chatHistory');
            if (savedHistory) {
                chatHistory = JSON.parse(savedHistory);
                updateHistoryPanel();
            }
        }

        // 保存历史记录到本地存储
        function saveHistory() {
            localStorage.setItem('chatHistory', JSON.stringify(chatHistory));
        }

        // 更新历史面板
        function updateHistoryPanel() {
            const historyList = document.getElementById('history-list');
            historyList.innerHTML = '';
            
            // 按会话分组显示历史记录
            const sessions = {};
            chatHistory.forEach(item => {
                if (!sessions[item.sessionId]) {
                    sessions[item.sessionId] = [];
                }
                sessions[item.sessionId].push(item);
            });
            
            // 为每个会话创建一个条目
            Object.keys(sessions).sort((a, b) => b - a).forEach(sessionId => {
                const sessionItems = sessions[sessionId];
                const sessionDate = new Date(parseInt(sessionId));
                
                const sessionDiv = document.createElement('div');
                sessionDiv.className = 'history-item';
                sessionDiv.innerHTML = `
                    <strong>会话: ${sessionDate.toLocaleDateString()} ${sessionDate.toLocaleTimeString()}</strong>
                    <p>${sessionItems[0].question.substring(0, 30)}${sessionItems[0].question.length > 30 ? '...' : ''}</p>
                `;
                sessionDiv.onclick = function() {
                    loadSessionChat(sessionId);
                };
                historyList.appendChild(sessionDiv);
            });
        }

        // 加载特定会话的聊天记录
        function loadSessionChat(sessionId) {
            const sessionItems = chatHistory.filter(item => item.sessionId == sessionId);
            const chatOutput = document.getElementById('chat-output');
            chatOutput.innerHTML = '';
            
            sessionItems.forEach(item => {
                appendMessage('你', item.question, 'user-message');
                appendMessage('回答', item.answer, 'bot-message');
            });
            
            toggleHistoryPanel();
        }

        // 切换历史面板显示
        function toggleHistoryPanel() {
            const historyPanel = document.getElementById('history-panel');
            historyPanel.classList.toggle('active');
        }

        function setupWebSocket() {
            socket = new WebSocket('ws://10.21.8.6:43007');

            socket.onopen = function(event) {
                console.log('WebSocket connection established');
            };

            socket.onmessage = function(event) {
                appendMessage('回答', event.data, 'bot-message');
                
                // 更新历史记录中的答案
                const lastItem = chatHistory[chatHistory.length - 1];
                if (lastItem) {
                    lastItem.answer = event.data;
                    saveHistory();
                    updateHistoryPanel();
                }
            };

            socket.onclose = function(event) {
                console.log('WebSocket connection closed');
            };

            socket.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }

        function toggleMic() {
            if (recognizing) {
                stopRecording();
            } else {
                startRecording();
            }
        }

        function startRecording() {
            setupWebSocket();
            
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(function(stream) {
                    mediaRecorder = new MediaRecorder(stream);
                    
                    mediaRecorder.ondataavailable = function(event) {
                        if (event.data.size > 0 && socket.readyState === WebSocket.OPEN) {
                            socket.send(event.data);
                        }
                    };

                    mediaRecorder.start(100); // Send audio data in chunks of 100ms

                    recognizing = true;
                    document.getElementById('mic-btn').style.backgroundColor = '#dc3545';
                })
                .catch(function(err) {
                    console.error('Error accessing microphone:', err);
                });
        }

        function stopRecording() {
            if (mediaRecorder) {
                mediaRecorder.stop();
            }
            if (socket) {
                socket.close();
            }
            recognizing = false;
            document.getElementById('mic-btn').style.backgroundColor = '#28a745';
        }

        function appendMessage(sender, text, className) {
            const chatOutput = document.getElementById('chat-output');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${className}`;
            
            // 添加时间戳
            const timestamp = new Date().toLocaleTimeString();
            
            messageDiv.innerHTML = `
                <div>${text}</div>
                <div class="timestamp">${timestamp}</div>
            `;
            
            chatOutput.appendChild(messageDiv);
            chatOutput.scrollTop = chatOutput.scrollHeight; // 滚动到底部
        }

        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            
            if (!message) return;
            
            input.value = '';
            
            // 显示用户输入的问题
            appendMessage('你', message, 'user-message');
        
            // 添加到历史记录
            chatHistory.push({
                sessionId: currentSessionId,
                timestamp: new Date().getTime(),
                question: message,
                answer: ''
            });
            
            // 创建一个消息容器用于流式更新
            const chatOutput = document.getElementById('chat-output');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot-message';
            const contentDiv = document.createElement('div');
            contentDiv.id = 'current-response';
            const timestampDiv = document.createElement('div');
            timestampDiv.className = 'timestamp';
            timestampDiv.textContent = new Date().toLocaleTimeString();
            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timestampDiv);
            chatOutput.appendChild(messageDiv);
            chatOutput.scrollTop = chatOutput.scrollHeight;
            
            // 使用流式API
            const useStreaming = true; // 可以添加一个开关来控制是否使用流式API
            
            // 替换现有的流式处理代码
            if (useStreaming) {
            // 使用 fetch 进行流式处理
            fetch('http://127.0.0.1:5000/multi_question/stream', {
            method: 'POST',
            headers: {
            'Content-Type': 'application/json'
            },
            body: JSON.stringify({
            question: message,
            format: 'html',
            history: getRecentHistory(5)
            })
            })
            .then(response => {
            if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
            }
            
            const reader = response.body.getReader();
            let decoder = new TextDecoder();
            let fullResponse = '';
            
            function processStream() {
            return reader.read().then(({ done, value }) => {
            if (done) {
            console.log('Stream complete');
            
            // 更新历史记录中的答案
            const lastItem = chatHistory[chatHistory.length - 1];
            if (lastItem) {
            lastItem.answer = fullResponse;
            saveHistory();
            updateHistoryPanel();
            }
            
            return;
            }
            
            // 解码接收到的数据
            const chunk = decoder.decode(value, { stream: true });
            
            // 处理SSE格式的数据
            const lines = chunk.split('\n\n');
            lines.forEach(line => {
            if (line.startsWith('data: ')) {
            try {
            const jsonData = JSON.parse(line.substring(6));
            
            if (jsonData.content) {
            // 更新当前响应
            fullResponse += jsonData.content;
            contentDiv.innerHTML = fullResponse;
            chatOutput.scrollTop = chatOutput.scrollHeight;
            } else if (jsonData.formatted) {
            // 处理格式化的最终响应
            contentDiv.innerHTML = jsonData.formatted;
            chatOutput.scrollTop = chatOutput.scrollHeight;
            } else if (jsonData.error) {
            // 处理错误
            contentDiv.innerHTML = `<span style="color: red;">错误: ${jsonData.error}</span>`;
            chatOutput.scrollTop = chatOutput.scrollHeight;
            }
            } catch (e) {
            console.error('解析事件数据出错:', e, line);
            }
            }
            });
            
            // 继续处理流
            return processStream();
            });
            }
            
            return processStream();
            })
            .catch(error => {
            console.error('流式处理错误:', error);
            contentDiv.textContent = '流式处理失败，正在尝试传统请求...';
            fetchTraditionalAPI(message, contentDiv);
            });
            } else {
            // 使用传统API
            fetchTraditionalAPI(message, contentDiv);
            }
        }
        
        // 提取传统API调用为单独函数
        function fetchTraditionalAPI(message, contentDiv) {
            fetch('http://127.0.0.1:5000/multi_question', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    question: message,
                    format: "html",
                    history: getRecentHistory(5)
                })
            })
            .then(response => response.json())
            .then(data => {
                const answer = data.answer || (data.formatted_answer ? data.formatted_answer : "无回答");
                
                // 更新响应内容
                contentDiv.innerHTML = answer;
                
                // 更新历史记录中的答案
                const lastItem = chatHistory[chatHistory.length - 1];
                if (lastItem) {
                    lastItem.answer = answer;
                    saveHistory();
                    updateHistoryPanel();
                }
                
                // 滚动到底部
                const chatOutput = document.getElementById('chat-output');
                chatOutput.scrollTop = chatOutput.scrollHeight;
            })
            .catch(error => {
                console.error('Error:', error);
                contentDiv.innerHTML = '发生错误，请稍后再试';
            });
        }

        // 获取最近的历史记录
        function getRecentHistory(count) {
            return chatHistory
                .slice(-count)
                .map(item => ({
                    question: item.question,
                    answer: item.answer
                }));
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 全屏切换
        document.getElementById('fullscreen-btn').addEventListener('click', function() {
            const chatContainer = document.getElementById('chat-container');
            if (chatContainer.classList.contains('fullscreen')) {
                chatContainer.classList.remove('fullscreen');
                this.textContent = '全屏';
            } else {
                chatContainer.classList.add('fullscreen');
                this.textContent = '还原';
            }
        });

        // 历史记录按钮
        document.getElementById('history-btn').addEventListener('click', toggleHistoryPanel);

        // 页面加载时初始化
        window.onload = function() {
            loadHistory();
        };
    </script>
</body>
</html>
