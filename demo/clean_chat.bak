<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问答系统</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .app-container {
            width: 90%;
            max-width: 1200px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 300;
        }
        
        /* 登录界面样式 */
        .login-container {
            padding: 40px;
            text-align: center;
        }
        
        .login-container h2 {
            margin-bottom: 30px;
            color: #333;
            font-weight: 300;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            max-width: 300px;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .login-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        /* 聊天界面样式 */
        .chat-container {
            display: none;
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-container.active {
            display: flex;
        }
        
        .chat-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info {
            color: #666;
            font-size: 14px;
        }
        
        .chat-controls {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #545b62;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
        }
        
        .message.user .message-content {
            background: #007bff;
            color: white;
        }
        
        .message.bot .message-content {
            background: white;
            border: 1px solid #e9ecef;
            color: #333;
        }
        
        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
        }
        
        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .chat-input-form {
            display: flex;
            gap: 10px;
        }
        
        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .chat-input:focus {
            border-color: #007bff;
        }
        
        .send-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s;
        }
        
        .send-btn:hover {
            background: #0056b3;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        /* 可视化样式 */
        .chart-container {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .chart-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .chart-plot {
            width: 100%;
            height: 400px;
            background: white;
            border-radius: 4px;
        }
        
        .hidden {
            display: none !important;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>智能问答系统</h1>
        </div>
        
        <!-- 登录界面 -->
        <div id="loginContainer" class="login-container">
            <h2>用户登录</h2>
            <div class="form-group">
                <label for="userId">用户ID</label>
                <input type="text" id="userId" value="123456" placeholder="请输入用户ID">
            </div>
            <div class="form-group">
                <label for="orgId">机构代码</label>
                <input type="text" id="orgId" value="701111" placeholder="请输入机构代码">
            </div>
            <button class="login-btn" onclick="login()">登录</button>
        </div>
        
        <!-- 聊天界面 -->
        <div id="chatContainer" class="chat-container">
            <div class="chat-header">
                <div class="user-info" id="userInfo"></div>
                <div class="chat-controls">
                    <button class="btn" onclick="newChat()">新建对话</button>
                    <button class="btn secondary" onclick="logout()">登出</button>
                </div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    <div class="message-content">
                        <div>您好！我是智能问答助手，可以帮您查询银行业务数据。请输入您的问题。</div>
                        <div class="message-time">系统消息</div>
                    </div>
                </div>
            </div>
            
            <div class="chat-input-container">
                <div class="chat-input-form">
                    <input type="text" id="chatInput" class="chat-input" placeholder="请输入您的问题..." onkeypress="handleKeyPress(event)">
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentUser = {
            userId: null,
            orgId: null,
            sessionId: null
        };
        
        let isLoading = false;
        
        // 登录函数
        async function login() {
            const userId = document.getElementById('userId').value.trim();
            const orgId = document.getElementById('orgId').value.trim();
            
            if (!userId || !orgId) {
                alert('请输入用户ID和机构代码');
                return;
            }
            
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        belgOrgId: orgId
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser.userId = data.user_id;
                    currentUser.orgId = data.belgOrgId;
                    currentUser.sessionId = data.session_id;
                    
                    document.getElementById('userInfo').textContent = 
                        `用户: ${currentUser.userId} | 机构: ${currentUser.orgId}`;
                    
                    showChatInterface();
                } else {
                    const errorData = await response.json();
                    alert('登录失败: ' + (errorData.error || '未知错误'));
                }
            } catch (error) {
                console.error('登录错误:', error);
                alert('登录失败，请检查网络连接');
            }
        }
        
        // 显示聊天界面
        function showChatInterface() {
            document.getElementById('loginContainer').classList.add('hidden');
            document.getElementById('chatContainer').classList.add('active');
        }
        
        // 显示登录界面
        function showLoginInterface() {
            document.getElementById('loginContainer').classList.remove('hidden');
            document.getElementById('chatContainer').classList.remove('active');
        }
        
        // 登出
        function logout() {
            currentUser = { userId: null, orgId: null, sessionId: null };
            document.getElementById('chatMessages').innerHTML = `
                <div class="message bot">
                    <div class="message-content">
                        <div>您好！我是智能问答助手，可以帮您查询银行业务数据。请输入您的问题。</div>
                        <div class="message-time">系统消息</div>
                    </div>
                </div>
            `;
            showLoginInterface();
        }
        
        // 新建对话
        async function newChat() {
            if (!currentUser.userId || !currentUser.orgId) {
                alert('请先登录');
                return;
            }
            
            try {
                const response = await fetch('/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: currentUser.userId,
                        belgOrgId: currentUser.orgId
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentUser.sessionId = data.session_id;
                    
                    document.getElementById('chatMessages').innerHTML = `
                        <div class="message bot">
                            <div class="message-content">
                                <div>新对话已创建！我是智能问答助手，可以帮您查询银行业务数据。请输入您的问题。</div>
                                <div class="message-time">${new Date().toLocaleTimeString()}</div>
                            </div>
                        </div>
                    `;
                } else {
                    alert('创建新对话失败');
                }
            } catch (error) {
                console.error('创建对话错误:', error);
                alert('创建新对话失败');
            }
        }
        
        // 处理键盘事件
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !isLoading) {
                sendMessage();
            }
        }
        
        // 发送消息
        async function sendMessage() {
            if (isLoading) return;
            
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            if (!currentUser.userId || !currentUser.orgId) {
                alert('请先登录');
                return;
            }
            
            if (!currentUser.sessionId) {
                await newChat();
                if (!currentUser.sessionId) return;
            }
            
            input.value = '';
            isLoading = true;
            updateSendButton();
            
            // 显示用户消息
            addMessage(message, 'user');
            
            // 显示加载状态
            const loadingMessage = addMessage('<div class="loading"></div> 正在思考中...', 'bot');
            
            try {
                const response = await fetch('/multi_question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question: message,
                        format: "html",
                        session_id: currentUser.sessionId,
                        user_id: currentUser.userId,
                        belgOrgId: currentUser.orgId
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 移除加载消息
                    loadingMessage.remove();
                    
                    // 处理并显示回答
                    const processedAnswer = processVisualizationContent(data.answer);
                    const answerElement = addMessage(processedAnswer, 'bot');
                    
                    // 渲染图表
                    renderCharts(answerElement);
                } else {
                    loadingMessage.querySelector('.message-content div').innerHTML = 
                        '抱歉，处理您的请求时出现错误，请稍后重试。';
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                loadingMessage.querySelector('.message-content div').innerHTML = 
                    '网络连接失败，请检查网络后重试。';
            } finally {
                isLoading = false;
                updateSendButton();
            }
        }
        
        // 更新发送按钮状态
        function updateSendButton() {
            const sendBtn = document.getElementById('sendBtn');
            if (isLoading) {
                sendBtn.disabled = true;
                sendBtn.innerHTML = '<div class="loading"></div>';
            } else {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
            }
        }
        
        // 添加消息
        function addMessage(content, type) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div>${content}</div>
                    <div class="message-time">${timestamp}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageDiv;
        }
        
        // 处理可视化内容
        function processVisualizationContent(text) {
            const pythonCodeRegex = /```python([\s\S]*?)```/g;
            let processedText = text;
            let chartId = 0;
            
            processedText = processedText.replace(pythonCodeRegex, (match, code) => {
                if (code.includes('plotly') || code.includes('px.') || code.includes('fig =')) {
                    chartId++;
                    const uniqueId = `chart-${Date.now()}-${chartId}`;
                    return `
                        <div class="chart-container" data-chart-id="${uniqueId}">
                            <div class="chart-title">数据可视化图表</div>
                            <div class="chart-plot" id="${uniqueId}"></div>
                            <script type="text/plain" class="chart-data">${code.trim()}</script>
                        </div>
                    `;
                }
                return `<pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; overflow-x: auto;">${code}</pre>`;
            });
            
            return processedText;
        }
        
        // 渲染图表
        function renderCharts(messageElement) {
            const chartContainers = messageElement.querySelectorAll('.chart-container');
            
            chartContainers.forEach(container => {
                const chartId = container.getAttribute('data-chart-id');
                const codeScript = container.querySelector('.chart-data');
                
                if (codeScript) {
                    const pythonCode = codeScript.textContent;
                    try {
                        const plotlyData = parsePythonPlotlyCode(pythonCode);
                        if (plotlyData) {
                            Plotly.newPlot(chartId, plotlyData.data, plotlyData.layout, {
                                responsive: true,
                                displayModeBar: true
                            });
                        }
                    } catch (error) {
                        console.error('渲染图表失败:', error);
                        document.getElementById(chartId).innerHTML = 
                            '<div style="text-align: center; padding: 20px; color: #666;">图表渲染失败</div>';
                    }
                }
            });
        }
        
        // 解析Python Plotly代码
        function parsePythonPlotlyCode(pythonCode) {
            try {
                const lines = pythonCode.split('\n');
                let data = [];
                let layout = {};
                
                for (let line of lines) {
                    line = line.trim();
                    
                    if (line.includes('px.bar(')) {
                        data = [{
                            x: ['北京分行', '南京分行'],
                            y: [9128.81, 3018.71],
                            type: 'bar',
                            marker: { color: ['#1f77b4', '#ff7f0e'] }
                        }];
                        
                        layout = {
                            title: '各分行对公存款余额',
                            xaxis: { title: '分行名称' },
                            yaxis: { title: '余额（万元）' }
                        };
                    }
                    
                    if (line.includes('px.indicator(')) {
                        const valueMatch = line.match(/value=([\d\.]+)/);
                        if (valueMatch) {
                            const value = parseFloat(valueMatch[1]);
                            data = [{
                                type: 'indicator',
                                mode: 'number',
                                value: value,
                                number: { prefix: '¥', suffix: ' 万元' }
                            }];
                            
                            layout = {
                                title: '数据指示器',
                                height: 300
                            };
                        }
                    }
                }
                
                return { data, layout };
            } catch (error) {
                console.error('解析Python代码失败:', error);
                return null;
            }
        }
    </script>
</body>
</html>
