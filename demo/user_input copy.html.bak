<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端修复测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chart-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            min-height: 400px;
        }

        .echarts-chart {
            width: 100%;
            height: 400px;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }

        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }

        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #0056b3;
        }

        .status {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }

        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端修复测试</h1>
        <p style="text-align: center; color: #666; font-size: 16px;">
            测试前端ECharts解析和渲染功能
        </p>

        <div class="test-section">
            <h3>测试1: 模拟您的实际JSON配置</h3>
            <p>这个测试使用从您截图中提取的实际JSON配置</p>
            <button onclick="testActualConfig()">测试实际配置</button>

            <div id="actualConfigContainer">
                <!-- 实际配置会插入这里 -->
            </div>
        </div>

        <div class="test-section">
            <h3>测试2: 模拟后端返回格式</h3>
            <p>这个测试模拟完整的后端返回格式</p>
            <button onclick="testBackendFormat()">测试后端格式</button>

            <div id="backendFormatContainer">
                <!-- 后端格式会插入这里 -->
            </div>
        </div>

        <div class="test-section">
            <h3>测试3: 调试信息</h3>
            <button onclick="runDebugTest()">运行调试测试</button>
            <button onclick="clearDebugLog()">清空日志</button>

            <div id="debugLog" class="json-display">点击"运行调试测试"查看详细信息</div>
        </div>

        <div id="globalStatus" class="status info">准备就绪，点击按钮开始测试</div>
    </div>

    <script>
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('globalStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
        }

        function clearDebugLog() {
            document.getElementById('debugLog').textContent = '';
        }

        // 测试1: 实际配置
        function testActualConfig() {
            setStatus('开始测试实际配置...', 'info');

            // 从您的截图中提取的实际JSON配置
            const actualJsonConfig = `{"title": {"text": "各个分行昨天的对公存款余额"}, "tooltip": {"trigger": "axis", "axisPointer": {"type": "shadow"}}, "xAxis": {"type": "category", "data": ["南京分行", "合肥分行", "福州分行", "北京分行", "大连分行", "沈阳分行", "天津分行", "石家庄分行", "西安分行", "太原分行"]}, "yAxis": {"type": "value", "name": "余额 (万元)"}, "series": [{"data": [8341.56, 2693.49, 1214.51, 9675.63, 8358.38, 8471.68, 6794.6, 9802.06, 9555.97, 5016.27], "type": "bar", "label": {"show": true, "position": "top", "formatter": "{c} 万元"}}]}`;

            const container = document.getElementById('actualConfigContainer');

            // 模拟后端返回的HTML结构
            container.innerHTML = `
                <strong>系统:</strong> 正在生成数据可视化表，请稍候... 以下是数据的可视化展示：
                <div class='chart-container'>${actualJsonConfig}</div>
            `;

            // 模拟前端处理逻辑
            setTimeout(() => {
                processChartContainers(container, 'actual');
            }, 100);
        }

        // 测试2: 后端格式
        function testBackendFormat() {
            setStatus('开始测试后端格式...', 'info');

            const backendJsonConfig = {
                "title": {
                    "text": "各分行对公存款余额对比",
                    "left": "center"
                },
                "tooltip": {
                    "trigger": "axis",
                    "formatter": "{b}: {c}万元"
                },
                "xAxis": {
                    "type": "category",
                    "data": ["北京分行", "上海分行", "南京分行", "合肥分行", "福州分行"]
                },
                "yAxis": {
                    "type": "value",
                    "name": "余额(万元)"
                },
                "series": [{
                    "name": "余额(万元)",
                    "type": "bar",
                    "data": [7731.07, 9892.58, 5368.80, 2693.49, 1214.51],
                    "itemStyle": {
                        "color": "#5470c6"
                    },
                    "label": {
                        "show": true,
                        "position": "top"
                    }
                }]
            };

            const container = document.getElementById('backendFormatContainer');

            // 模拟完整的后端返回
            container.innerHTML = `
                <strong>系统:</strong> 根据查询结果，各分行的对公存款余额如下：

                📊 正在生成数据可视化图表，请稍候...

                以下是数据的可视化展示：
                <div class='chart-container'>${JSON.stringify(backendJsonConfig)}</div>
            `;

            // 模拟前端处理逻辑
            setTimeout(() => {
                processChartContainers(container, 'backend');
            }, 100);
        }

        // 处理图表容器（复制自主页面的逻辑）
        function processChartContainers(parentContainer, testType) {
            const chartContainers = parentContainer.querySelectorAll('.chart-container');
            log(`[${testType}] 找到 ${chartContainers.length} 个图表容器`);

            chartContainers.forEach((container, index) => {
                let content = container.innerHTML.trim();
                log(`[${testType}] 容器 ${index} 原始内容: ${content.substring(0, 100)}...`);

                // 清理HTML标签和多余的空白，只保留JSON内容
                content = content.replace(/<[^>]*>/g, '').trim();
                content = content.replace(/\s+/g, ' '); // 压缩空白
                log(`[${testType}] 容器 ${index} 清理后内容: ${content.substring(0, 100)}...`);

                // 更宽松的ECharts配置检查
                const isEChartsConfig = content.startsWith('{') &&
                                      (content.includes('"title"') ||
                                       content.includes('"series"') ||
                                       content.includes('"xAxis"') ||
                                       content.includes('"data"'));

                log(`[${testType}] 容器 ${index} 是否为ECharts配置: ${isEChartsConfig}`);

                if (isEChartsConfig) {
                    try {
                        log(`[${testType}] 开始处理容器 ${index} 的JSON配置...`);

                        // 尝试修复可能的JSON格式问题
                        let jsonContent = content;

                        // 修复常见的JSON格式问题
                        jsonContent = jsonContent.replace(/'/g, '"'); // 单引号替换为双引号
                        jsonContent = jsonContent.replace(/,\s*}/g, '}'); // 移除尾随逗号
                        jsonContent = jsonContent.replace(/,\s*]/g, ']'); // 移除数组尾随逗号

                        // 确保JSON格式正确
                        if (!jsonContent.startsWith('{')) {
                            throw new Error('不是有效的JSON对象');
                        }

                        log(`[${testType}] 容器 ${index} JSON修复完成`);

                        // 解析ECharts配置
                        const chartConfig = JSON.parse(jsonContent);
                        log(`[${testType}] 容器 ${index} JSON解析成功`);

                        // 创建图表容器
                        const chartDiv = document.createElement('div');
                        chartDiv.className = 'echarts-chart';
                        chartDiv.id = `chart_${testType}_${Date.now()}_${index}`;
                        chartDiv.style.width = '100%';
                        chartDiv.style.height = '400px';
                        chartDiv.style.border = '1px solid #ddd';
                        chartDiv.style.borderRadius = '4px';

                        // 清空容器并添加图表div
                        container.innerHTML = '';
                        container.appendChild(chartDiv);

                        // 延迟初始化确保DOM已渲染
                        setTimeout(() => {
                            try {
                                log(`[${testType}] 开始初始化容器 ${index} 的ECharts...`);

                                // 检查ECharts是否可用
                                if (typeof echarts === 'undefined') {
                                    throw new Error('ECharts库未加载');
                                }

                                // 检查容器是否存在且有尺寸
                                if (!chartDiv.offsetWidth || !chartDiv.offsetHeight) {
                                    log(`[${testType}] 容器 ${index} 尺寸异常，强制设置尺寸`);
                                    chartDiv.style.width = '100%';
                                    chartDiv.style.height = '400px';
                                }

                                // 初始化ECharts实例
                                const chart = echarts.init(chartDiv);
                                log(`[${testType}] 容器 ${index} ECharts实例创建成功`);

                                // 获取配置选项
                                const option = chartConfig.option || chartConfig;
                                log(`[${testType}] 容器 ${index} 配置选项准备完成`);

                                // 验证配置的基本结构
                                if (!option.series || !Array.isArray(option.series) || option.series.length === 0) {
                                    throw new Error('配置中缺少有效的series数据');
                                }

                                // 设置图表选项
                                chart.setOption(option, true);
                                log(`[${testType}] 容器 ${index} 图表选项设置成功`);

                                // 响应式调整
                                const resizeHandler = () => {
                                    chart.resize();
                                };
                                window.addEventListener('resize', resizeHandler);

                                // 存储chart实例
                                chartDiv._chartInstance = chart;
                                chartDiv._resizeHandler = resizeHandler;

                                log(`[${testType}] ✅ 容器 ${index} ECharts图表初始化成功`);
                                setStatus(`✅ ${testType} 测试成功！图表渲染完成`, 'success');

                                // 添加成功标识
                                const successDiv = document.createElement('div');
                                successDiv.style.cssText = 'position: absolute; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; z-index: 1000;';
                                successDiv.textContent = `✅ ${testType} 成功`;
                                container.style.position = 'relative';
                                container.appendChild(successDiv);

                                // 5秒后移除成功标识
                                setTimeout(() => {
                                    if (successDiv.parentNode) {
                                        successDiv.parentNode.removeChild(successDiv);
                                    }
                                }, 5000);

                            } catch (chartError) {
                                log(`[${testType}] ❌ 容器 ${index} ECharts初始化失败: ${chartError.message}`);
                                setStatus(`❌ ${testType} 测试失败: ${chartError.message}`, 'error');
                                container.innerHTML = `<div style="color: red; padding: 20px; border: 1px solid red; border-radius: 4px; background: #fff5f5;">
                                    <strong>📊 图表渲染失败</strong><br>
                                    <strong>测试类型:</strong> ${testType}<br>
                                    <strong>容器ID:</strong> ${index}<br>
                                    <strong>错误信息:</strong> ${chartError.message}<br>
                                    <small>请检查浏览器控制台获取详细信息</small>
                                </div>`;
                            }
                        }, 300);

                    } catch (error) {
                        log(`[${testType}] ❌ 解析ECharts配置失败: ${error.message}`);
                        setStatus(`❌ ${testType} 配置解析失败: ${error.message}`, 'error');
                        container.innerHTML = `<div style="color: orange; padding: 20px; border: 1px solid orange; border-radius: 4px;">
                            <strong>图表配置解析失败</strong><br>
                            <strong>测试类型:</strong> ${testType}<br>
                            <strong>错误信息:</strong> ${error.message}<br>
                            <details style="margin-top: 10px;">
                                <summary>查看原始配置</summary>
                                <pre style="background: #f5f5f5; padding: 10px; margin-top: 5px; font-size: 12px; overflow: auto;">${content}</pre>
                            </details>
                        </div>`;
                    }
                } else {
                    log(`[${testType}] 不是有效的ECharts配置`);
                }
            });
        }

        // 调试测试
        function runDebugTest() {
            clearDebugLog();
            log('=== 开始调试测试 ===');
            log(`ECharts状态: ${typeof echarts !== 'undefined' ? '已加载' : '未加载'}`);
            if (typeof echarts !== 'undefined') {
                log(`ECharts版本: ${echarts.version}`);
            }

            const containers = document.querySelectorAll('.chart-container');
            log(`当前页面图表容器数量: ${containers.length}`);

            containers.forEach((container, index) => {
                log(`容器 ${index} 信息:`);
                log(`  - 内容长度: ${container.innerHTML.length}`);
                log(`  - 包含ECharts实例: ${!!container.querySelector('.echarts-chart')?._chartInstance}`);
                log(`  - 内容预览: ${container.innerHTML.substring(0, 100)}...`);
            });

            log('=== 调试测试完成 ===');
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof echarts !== 'undefined') {
                setStatus(`✅ ECharts已加载，版本: ${echarts.version}`, 'success');
            } else {
                setStatus('❌ ECharts未加载', 'error');
            }
        });
    </script>
</body>
</html>