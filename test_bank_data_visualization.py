#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
�����������ݿ��ӻ�
��֤��������+������ݵ���״ͼ����
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

def test_bank_data_chart():
    """�����������ݵ�ͼ������"""
    
    # ģ������ʵ������
    bank_data = [
        {
            "��������": "711100",
            "��������": "��������", 
            "����": "********",
            "��Ŀ����": "�Թ����",
            "���(��Ԫ)": 7731.07
        },
        {
            "��������": "731109",
            "��������": "�Ϻ�����",
            "����": "********", 
            "��Ŀ����": "�Թ����",
            "���(��Ԫ)": 9892.58
        },
        {
            "��������": "703220",
            "��������": "�Ͼ�����",
            "����": "********",
            "��Ŀ����": "�Թ����", 
            "���(��Ԫ)": 5368.80
        },
        {
            "��������": "703260",
            "��������": "�Ϸʷ���",
            "����": "********",
            "��Ŀ����": "�Թ����",
            "���(��Ԫ)": 8017.85
        }
    ]
    
    df = pd.DataFrame(bank_data)
    print("����Ԥ��:")
    print(df)
    print(f"\n������״: {df.shape}")
    print(f"������: {df.dtypes.to_dict()}")
    
    # ������������
    numeric_cols = df.select_dtypes(include=["number"]).columns.tolist()
    categorical_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()
    
    print(f"\n��ֵ��: {numeric_cols}")
    print(f"������: {categorical_cols}")
    
    # �������ҵ��������
    institution_cols = [col for col in categorical_cols if any(keyword in col.lower() 
                      for keyword in ['����', '����', 'org', 'branch', '����', 'name'])]
    amount_cols = [col for col in numeric_cols if any(keyword in col.lower() 
                  for keyword in ['���', '���', 'amount', 'balance', '��Ԫ', 'Ԫ'])]
    
    print(f"��⵽�Ļ�����: {institution_cols}")
    print(f"��⵽�Ľ����: {amount_cols}")
    
    # ��ɫ����
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    if institution_cols and amount_cols:
        print(f"\n�������л���������״ͼ...")
        
        # ������״ͼ
        fig = px.bar(df, 
                    x=institution_cols[0], 
                    y=amount_cols[0],
                    color=institution_cols[0],
                    color_discrete_sequence=colors,
                    title=f"������{amount_cols[0]}�Ա�")
        
        # �Ż���ʾ
        fig.update_layout(
            showlegend=False,  # ����ͼ��
            template="plotly_white",
            font=dict(color="black"),
            xaxis_title=institution_cols[0],
            yaxis_title=amount_cols[0]
        )
        fig.update_xaxes(tickangle=45)  # ��б��ǩ
        
        # �����ֵ��ǩ
        fig.update_traces(texttemplate='%{y:.2f}', textposition='outside')
        
        # ��������ļ�
        fig.write_html("bank_data_chart.html")
        print("��״ͼ������: bank_data_chart.html")
        
        return fig
    else:
        print("δ��⵽����ҵ��������ʹ��Ĭ��ͼ������")
        return None

def test_chart_variations():
    """���Բ�ͬ��ͼ��仯"""
    
    # ��������
    base_data = [
        {"��������": "��������", "���(��Ԫ)": 7731.07},
        {"��������": "�Ϻ�����", "���(��Ԫ)": 9892.58}, 
        {"��������": "�Ͼ�����", "���(��Ԫ)": 5368.80},
        {"��������": "�Ϸʷ���", "���(��Ԫ)": 8017.85}
    ]
    
    df = pd.DataFrame(base_data)
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    
    # 1. ������״ͼ
    fig1 = px.bar(df, x='��������', y='���(��Ԫ)', 
                  color='��������',
                  color_discrete_sequence=colors,
                  title="������״ͼ")
    fig1.update_layout(showlegend=False, template="plotly_white")
    fig1.update_traces(texttemplate='%{y:.2f}��Ԫ', textposition='outside')
    fig1.write_html("chart_basic_bar.html")
    
    # 2. ˮƽ��״ͼ
    fig2 = px.bar(df, x='���(��Ԫ)', y='��������',
                  color='��������', 
                  color_discrete_sequence=colors,
                  orientation='h',
                  title="ˮƽ��״ͼ")
    fig2.update_layout(showlegend=False, template="plotly_white")
    fig2.update_traces(texttemplate='%{x:.2f}��Ԫ', textposition='outside')
    fig2.write_html("chart_horizontal_bar.html")
    
    # 3. ��������״ͼ
    df_sorted = df.sort_values('���(��Ԫ)', ascending=False)
    fig3 = px.bar(df_sorted, x='��������', y='���(��Ԫ)',
                  color='��������',
                  color_discrete_sequence=colors,
                  title="������������״ͼ")
    fig3.update_layout(showlegend=False, template="plotly_white")
    fig3.update_traces(texttemplate='%{y:.2f}��Ԫ', textposition='outside')
    fig3.write_html("chart_sorted_bar.html")
    
    print("�����˶���ͼ��仯:")
    print("- chart_basic_bar.html (������״ͼ)")
    print("- chart_horizontal_bar.html (ˮƽ��״ͼ)")
    print("- chart_sorted_bar.html (������״ͼ)")

def generate_optimal_code():
    """������������ݵ����ſ��ӻ�����"""
    
    optimal_code = '''
import plotly.express as px

# ������״ͼ�����ʺϻ����Ա�����
fig = px.bar(df, 
            x='��������', 
            y='���(��Ԫ)',
            color='��������',
            title='�����жԹ�������Ա�')

# �Ż���ʾЧ��
fig.update_layout(
    showlegend=False,  # ����ͼ����x������ʾ������
    template="plotly_white",
    xaxis_title="��������",
    yaxis_title="���(��Ԫ)"
)

# ��бx���ǩ�����ص�
fig.update_xaxes(tickangle=45)

# ����������ʾ��ֵ
fig.update_traces(texttemplate='%{y:.2f}', textposition='outside')
'''
    
    print("����������ݵ����ſ��ӻ�����:")
    print(optimal_code)
    
    return optimal_code

if __name__ == "__main__":
    print("��ʼ�����������ݿ��ӻ�...")
    
    # ������Ҫ����
    test_bank_data_chart()
    
    # ����ͼ��仯
    test_chart_variations()
    
    # �������Ŵ���
    generate_optimal_code()
    
    print("\n������ɣ���鿴���ɵ�HTML�ļ���֤Ч����")
