<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ǰ���޸�����</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            min-height: 400px;
        }
        
        .echarts-chart {
            width: 100%;
            height: 400px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .status {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>? ǰ���޸�����</h1>
        <p style="text-align: center; color: #666; font-size: 16px;">
            ����ǰ��ECharts��������Ⱦ����
        </p>
        
        <div class="test-section">
            <h3>����1: ģ������ʵ��JSON����</h3>
            <p>�������ʹ�ô�����ͼ����ȡ��ʵ��JSON����</p>
            <button onclick="testActualConfig()">����ʵ������</button>
            
            <div id="actualConfigContainer">
                <!-- ʵ�����û�������� -->
            </div>
        </div>
        
        <div class="test-section">
            <h3>����2: ģ���˷��ظ�ʽ</h3>
            <p>�������ģ�������ĺ�˷��ظ�ʽ</p>
            <button onclick="testBackendFormat()">���Ժ�˸�ʽ</button>
            
            <div id="backendFormatContainer">
                <!-- ��˸�ʽ��������� -->
            </div>
        </div>
        
        <div class="test-section">
            <h3>����3: ������Ϣ</h3>
            <button onclick="runDebugTest()">���е��Բ���</button>
            <button onclick="clearDebugLog()">�����־</button>
            
            <div id="debugLog" class="json-display">���"���е��Բ���"�鿴��ϸ��Ϣ</div>
        </div>
        
        <div id="globalStatus" class="status info">׼�������������ť��ʼ����</div>
    </div>

    <script>
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('globalStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
        }
        
        function clearDebugLog() {
            document.getElementById('debugLog').textContent = '';
        }
        
        // ����1: ʵ������
        function testActualConfig() {
            setStatus('��ʼ����ʵ������...', 'info');
            
            // �����Ľ�ͼ����ȡ��ʵ��JSON����
            const actualJsonConfig = `{"title": {"text": "������������ĶԹ�������"}, "tooltip": {"trigger": "axis", "axisPointer": {"type": "shadow"}}, "xAxis": {"type": "category", "data": ["�Ͼ�����", "�Ϸʷ���", "���ݷ���", "��������", "��������", "��������", "������", "ʯ��ׯ����", "��������", "̫ԭ����"]}, "yAxis": {"type": "value", "name": "��� (��Ԫ)"}, "series": [{"data": [8341.56, 2693.49, 1214.51, 9675.63, 8358.38, 8471.68, 6794.6, 9802.06, 9555.97, 5016.27], "type": "bar", "label": {"show": true, "position": "top", "formatter": "{c} ��Ԫ"}}]}`;
            
            const container = document.getElementById('actualConfigContainer');
            
            // ģ���˷��ص�HTML�ṹ
            container.innerHTML = `
                <strong>ϵͳ:</strong> �����������ݿ��ӻ������Ժ�... ���������ݵĿ��ӻ�չʾ��
                <div class='chart-container'>${actualJsonConfig}</div>
            `;
            
            // ģ��ǰ�˴����߼�
            setTimeout(() => {
                processChartContainers(container, 'actual');
            }, 100);
        }
        
        // ����2: ��˸�ʽ
        function testBackendFormat() {
            setStatus('��ʼ���Ժ�˸�ʽ...', 'info');
            
            const backendJsonConfig = {
                "title": {
                    "text": "�����жԹ�������Ա�",
                    "left": "center"
                },
                "tooltip": {
                    "trigger": "axis",
                    "formatter": "{b}: {c}��Ԫ"
                },
                "xAxis": {
                    "type": "category",
                    "data": ["��������", "�Ϻ�����", "�Ͼ�����", "�Ϸʷ���", "���ݷ���"]
                },
                "yAxis": {
                    "type": "value",
                    "name": "���(��Ԫ)"
                },
                "series": [{
                    "name": "���(��Ԫ)",
                    "type": "bar",
                    "data": [7731.07, 9892.58, 5368.80, 2693.49, 1214.51],
                    "itemStyle": {
                        "color": "#5470c6"
                    },
                    "label": {
                        "show": true,
                        "position": "top"
                    }
                }]
            };
            
            const container = document.getElementById('backendFormatContainer');
            
            // ģ�������ĺ�˷���
            container.innerHTML = `
                <strong>ϵͳ:</strong> ���ݲ�ѯ����������еĶԹ����������£�
                
                ? �����������ݿ��ӻ�ͼ�����Ժ�...
                
                ���������ݵĿ��ӻ�չʾ��
                <div class='chart-container'>${JSON.stringify(backendJsonConfig)}</div>
            `;
            
            // ģ��ǰ�˴����߼�
            setTimeout(() => {
                processChartContainers(container, 'backend');
            }, 100);
        }
        
        // ����ͼ����������������ҳ����߼���
        function processChartContainers(parentContainer, testType) {
            const chartContainers = parentContainer.querySelectorAll('.chart-container');
            log(`[${testType}] �ҵ� ${chartContainers.length} ��ͼ������`);
            
            chartContainers.forEach((container, index) => {
                let content = container.innerHTML.trim();
                log(`[${testType}] ���� ${index} ԭʼ����: ${content.substring(0, 100)}...`);
                
                // ����HTML��ǩ�Ͷ���Ŀհף�ֻ����JSON����
                content = content.replace(/<[^>]*>/g, '').trim();
                content = content.replace(/\s+/g, ' '); // ѹ���հ�
                log(`[${testType}] ���� ${index} ���������: ${content.substring(0, 100)}...`);
                
                // �����ɵ�ECharts���ü��
                const isEChartsConfig = content.startsWith('{') && 
                                      (content.includes('"title"') || 
                                       content.includes('"series"') || 
                                       content.includes('"xAxis"') ||
                                       content.includes('"data"'));
                
                log(`[${testType}] ���� ${index} �Ƿ�ΪECharts����: ${isEChartsConfig}`);
                
                if (isEChartsConfig) {
                    try {
                        log(`[${testType}] ��ʼ�������� ${index} ��JSON����...`);
                        
                        // �����޸����ܵ�JSON��ʽ����
                        let jsonContent = content;
                        
                        // �޸�������JSON��ʽ����
                        jsonContent = jsonContent.replace(/'/g, '"'); // �������滻Ϊ˫����
                        jsonContent = jsonContent.replace(/,\s*}/g, '}'); // �Ƴ�β�涺��
                        jsonContent = jsonContent.replace(/,\s*]/g, ']'); // �Ƴ�����β�涺��
                        
                        // ȷ��JSON��ʽ��ȷ
                        if (!jsonContent.startsWith('{')) {
                            throw new Error('������Ч��JSON����');
                        }
                        
                        log(`[${testType}] ���� ${index} JSON�޸����`);
                        
                        // ����ECharts����
                        const chartConfig = JSON.parse(jsonContent);
                        log(`[${testType}] ���� ${index} JSON�����ɹ�`);
                        
                        // ����ͼ������
                        const chartDiv = document.createElement('div');
                        chartDiv.className = 'echarts-chart';
                        chartDiv.id = `chart_${testType}_${Date.now()}_${index}`;
                        chartDiv.style.width = '100%';
                        chartDiv.style.height = '400px';
                        chartDiv.style.border = '1px solid #ddd';
                        chartDiv.style.borderRadius = '4px';
                        
                        // ������������ͼ��div
                        container.innerHTML = '';
                        container.appendChild(chartDiv);
                        
                        // �ӳٳ�ʼ��ȷ��DOM����Ⱦ
                        setTimeout(() => {
                            try {
                                log(`[${testType}] ��ʼ��ʼ������ ${index} ��ECharts...`);
                                
                                // ���ECharts�Ƿ����
                                if (typeof echarts === 'undefined') {
                                    throw new Error('ECharts��δ����');
                                }
                                
                                // ��������Ƿ�������гߴ�
                                if (!chartDiv.offsetWidth || !chartDiv.offsetHeight) {
                                    log(`[${testType}] ���� ${index} �ߴ��쳣��ǿ�����óߴ�`);
                                    chartDiv.style.width = '100%';
                                    chartDiv.style.height = '400px';
                                }
                                
                                // ��ʼ��EChartsʵ��
                                const chart = echarts.init(chartDiv);
                                log(`[${testType}] ���� ${index} EChartsʵ�������ɹ�`);
                                
                                // ��ȡ����ѡ��
                                const option = chartConfig.option || chartConfig;
                                log(`[${testType}] ���� ${index} ����ѡ��׼�����`);
                                
                                // ��֤���õĻ����ṹ
                                if (!option.series || !Array.isArray(option.series) || option.series.length === 0) {
                                    throw new Error('������ȱ����Ч��series����');
                                }
                                
                                // ����ͼ��ѡ��
                                chart.setOption(option, true);
                                log(`[${testType}] ���� ${index} ͼ��ѡ�����óɹ�`);
                                
                                // ��Ӧʽ����
                                const resizeHandler = () => {
                                    chart.resize();
                                };
                                window.addEventListener('resize', resizeHandler);
                                
                                // �洢chartʵ��
                                chartDiv._chartInstance = chart;
                                chartDiv._resizeHandler = resizeHandler;
                                
                                log(`[${testType}] ? ���� ${index} EChartsͼ���ʼ���ɹ�`);
                                setStatus(`? ${testType} ���Գɹ���ͼ����Ⱦ���`, 'success');
                                
                                // ��ӳɹ���ʶ
                                const successDiv = document.createElement('div');
                                successDiv.style.cssText = 'position: absolute; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 3px; font-size: 12px; z-index: 1000;';
                                successDiv.textContent = `? ${testType} �ɹ�`;
                                container.style.position = 'relative';
                                container.appendChild(successDiv);
                                
                                // 5����Ƴ��ɹ���ʶ
                                setTimeout(() => {
                                    if (successDiv.parentNode) {
                                        successDiv.parentNode.removeChild(successDiv);
                                    }
                                }, 5000);
                                
                            } catch (chartError) {
                                log(`[${testType}] ? ���� ${index} ECharts��ʼ��ʧ��: ${chartError.message}`);
                                setStatus(`? ${testType} ����ʧ��: ${chartError.message}`, 'error');
                                container.innerHTML = `<div style="color: red; padding: 20px; border: 1px solid red; border-radius: 4px; background: #fff5f5;">
                                    <strong>? ͼ����Ⱦʧ��</strong><br>
                                    <strong>��������:</strong> ${testType}<br>
                                    <strong>����ID:</strong> ${index}<br>
                                    <strong>������Ϣ:</strong> ${chartError.message}<br>
                                    <small>�������������̨��ȡ��ϸ��Ϣ</small>
                                </div>`;
                            }
                        }, 300);
                        
                    } catch (error) {
                        log(`[${testType}] ? ����ECharts����ʧ��: ${error.message}`);
                        setStatus(`? ${testType} ���ý���ʧ��: ${error.message}`, 'error');
                        container.innerHTML = `<div style="color: orange; padding: 20px; border: 1px solid orange; border-radius: 4px;">
                            <strong>ͼ�����ý���ʧ��</strong><br>
                            <strong>��������:</strong> ${testType}<br>
                            <strong>������Ϣ:</strong> ${error.message}<br>
                            <details style="margin-top: 10px;">
                                <summary>�鿴ԭʼ����</summary>
                                <pre style="background: #f5f5f5; padding: 10px; margin-top: 5px; font-size: 12px; overflow: auto;">${content}</pre>
                            </details>
                        </div>`;
                    }
                } else {
                    log(`[${testType}] ������Ч��ECharts����`);
                }
            });
        }
        
        // ���Բ���
        function runDebugTest() {
            clearDebugLog();
            log('=== ��ʼ���Բ��� ===');
            log(`ECharts״̬: ${typeof echarts !== 'undefined' ? '�Ѽ���' : 'δ����'}`);
            if (typeof echarts !== 'undefined') {
                log(`ECharts�汾: ${echarts.version}`);
            }
            
            const containers = document.querySelectorAll('.chart-container');
            log(`��ǰҳ��ͼ����������: ${containers.length}`);
            
            containers.forEach((container, index) => {
                log(`���� ${index} ��Ϣ:`);
                log(`  - ���ݳ���: ${container.innerHTML.length}`);
                log(`  - ����EChartsʵ��: ${!!container.querySelector('.echarts-chart')?._chartInstance}`);
                log(`  - ����Ԥ��: ${container.innerHTML.substring(0, 100)}...`);
            });
            
            log('=== ���Բ������ ===');
        }
        
        // ҳ��������
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof echarts !== 'undefined') {
                setStatus(`? ECharts�Ѽ��أ��汾: ${echarts.version}`, 'success');
            } else {
                setStatus('? EChartsδ����', 'error');
            }
        });
    </script>
</body>
</html>
