#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
����ECharts����
��֤��PlotlyǨ�Ƶ�ECharts�Ĺ���
"""

import json
import pandas as pd
from scene_processor.impl.common_processor import CommonProcessor

def test_echarts_fallback():
    """����ECharts������������"""
    
    # ������������
    test_data = [
        {"��������": "��������", "���(��Ԫ)": 7731.07},
        {"��������": "�Ϻ�����", "���(��Ԫ)": 9892.58},
        {"��������": "�Ͼ�����", "���(��Ԫ)": 5368.80},
        {"��������": "�Ϸʷ���", "���(��Ԫ)": 8017.85}
    ]
    
    df = pd.DataFrame(test_data)
    
    # ����������ʵ��
    scene_config = {
        "name": "test_scene",
        "parameters": []
    }
    processor = CommonProcessor(scene_config)
    
    print("������״ͼ��������...")
    bar_config = processor._generate_fallback_echarts_config(df, "bar")
    print("��״ͼ����:")
    print(bar_config)
    print("\n" + "="*50 + "\n")
    
    print("���Ա�ͼ��������...")
    pie_config = processor._generate_fallback_echarts_config(df, "pie")
    print("��ͼ����:")
    print(pie_config)
    print("\n" + "="*50 + "\n")
    
    # ��֤JSON��ʽ
    try:
        bar_json = json.loads(bar_config)
        pie_json = json.loads(pie_config)
        print("? JSON��ʽ��֤ͨ��")
        
        # ����Ҫ�ֶ�
        required_fields = ["title", "xAxis", "yAxis", "series"]
        for field in required_fields:
            if field in bar_json:
                print(f"? ��״ͼ����{field}�ֶ�")
            else:
                print(f"? ��״ͼȱ��{field}�ֶ�")
                
        pie_required = ["title", "series"]
        for field in pie_required:
            if field in pie_json:
                print(f"? ��ͼ����{field}�ֶ�")
            else:
                print(f"? ��ͼȱ��{field}�ֶ�")
                
    except json.JSONDecodeError as e:
        print(f"? JSON��ʽ����: {e}")

def test_echarts_config_extraction():
    """����ECharts������ȡ"""
    
    scene_config = {
        "name": "test_scene", 
        "parameters": []
    }
    processor = CommonProcessor(scene_config)
    
    # ������ȷ��JSON����
    valid_json = '''
    {
        "title": {
            "text": "����ͼ��"
        },
        "xAxis": {
            "type": "category",
            "data": ["A", "B", "C"]
        },
        "yAxis": {
            "type": "value"
        },
        "series": [{
            "type": "bar",
            "data": [1, 2, 3]
        }]
    }
    '''
    
    print("������ЧJSON������ȡ...")
    extracted = processor._extract_echarts_config(valid_json)
    print("��ȡ���:")
    print(extracted)
    print("\n" + "="*50 + "\n")
    
    # ���Դ�����������
    json_with_blocks = '''
    ����һ��ECharts���ã�
    ```json
    {
        "title": {"text": "������������"},
        "series": [{"type": "line", "data": [1,2,3]}]
    }
    ```
    '''
    
    print("���Դ����������ȡ...")
    extracted_blocks = processor._extract_echarts_config(json_with_blocks)
    print("��ȡ���:")
    print(extracted_blocks)
    print("\n" + "="*50 + "\n")
    
    # ������Ч����
    invalid_json = "�ⲻ��һ����Ч��JSON����"
    print("������Ч���ô���...")
    extracted_invalid = processor._extract_echarts_config(invalid_json)
    print("��ȡ���:")
    print(f"'{extracted_invalid}' (Ӧ��Ϊ��)")

def test_complete_visualization_flow():
    """���������Ŀ��ӻ�����"""
    
    # ģ��API���
    api_result = {
        "status": "success",
        "data": [
            {"��������": "��������", "���(��Ԫ)": 7731.07},
            {"��������": "�Ϻ�����", "���(��Ԫ)": 9892.58},
            {"��������": "�Ͼ�����", "���(��Ԫ)": 5368.80},
            {"��������": "�Ϸʷ���", "���(��Ԫ)": 8017.85}
        ]
    }
    
    scene_config = {
        "name": "test_scene",
        "parameters": []
    }
    processor = CommonProcessor(scene_config)
    
    print("�����������ӻ�����...")
    
    # ģ��ECharts���ã�ͨ����LLM���ɣ�
    mock_echarts_config = '''
    {
        "title": {
            "text": "���������Ա�",
            "left": "center"
        },
        "tooltip": {
            "trigger": "axis"
        },
        "xAxis": {
            "type": "category",
            "data": ["��������", "�Ϻ�����", "�Ͼ�����", "�Ϸʷ���"],
            "name": "��������"
        },
        "yAxis": {
            "type": "value",
            "name": "���(��Ԫ)"
        },
        "series": [{
            "name": "���(��Ԫ)",
            "type": "bar",
            "data": [7731.07, 9892.58, 5368.80, 8017.85],
            "itemStyle": {
                "color": "#5470c6"
            }
        }]
    }
    '''
    
    # ִ�п��ӻ�
    result = processor.execute_visualization_code(mock_echarts_config, api_result["data"])
    
    if result:
        print("? ���ӻ�ִ�гɹ�")
        print("���ɵ�����:")
        print(result)
        
        # ��֤�����ʽ
        try:
            result_json = json.loads(result)
            if "option" in result_json:
                print("? ���ø�ʽ��ȷ������option�ֶ�")
            else:
                print("? ���ø�ʽ����ȱ��option�ֶ�")
        except json.JSONDecodeError:
            print("? ���ؽ��������ЧJSON")
    else:
        print("? ���ӻ�ִ��ʧ��")

def generate_sample_html():
    """����ʾ��HTML�ļ����ڲ���ǰ����Ⱦ"""
    
    sample_config = {
        "option": {
            "title": {
                "text": "�����жԹ�������Ա�",
                "left": "center"
            },
            "tooltip": {
                "trigger": "axis"
            },
            "xAxis": {
                "type": "category",
                "data": ["��������", "�Ϻ�����", "�Ͼ�����", "�Ϸʷ���"],
                "name": "��������"
            },
            "yAxis": {
                "type": "value",
                "name": "���(��Ԫ)"
            },
            "series": [{
                "name": "���(��Ԫ)",
                "type": "bar",
                "data": [7731.07, 9892.58, 5368.80, 8017.85],
                "itemStyle": {
                    "color": "#5470c6"
                }
            }]
        }
    }
    
    html_content = f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ECharts����</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div id="chart" style="width: 800px; height: 400px;"></div>
    
    <script>
        var chart = echarts.init(document.getElementById('chart'));
        var option = {json.dumps(sample_config["option"], ensure_ascii=False, indent=2)};
        chart.setOption(option);
    </script>
</body>
</html>
'''
    
    with open("echarts_test.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print("? ���ɲ���HTML�ļ�: echarts_test.html")

if __name__ == "__main__":
    print("��ʼECharts���ɲ���...\n")
    
    # ���Ի�������
    test_echarts_fallback()
    print("\n" + "="*60 + "\n")
    
    # ����������ȡ
    test_echarts_config_extraction()
    print("\n" + "="*60 + "\n")
    
    # ������������
    test_complete_visualization_flow()
    print("\n" + "="*60 + "\n")
    
    # ����ʾ��HTML
    generate_sample_html()
    
    print("\n? ECharts���ɲ�����ɣ�")
