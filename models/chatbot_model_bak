# encoding=utf-8
import json
import logging

from config import RELATED_INTENT_THRESHOLD
from scene_processor.impl.common_processor import CommonProcessor
from utils.data_format_utils import extract_continuous_digits, extract_float
from utils.helpers import send_message


class ChatbotModel:
    def __init__(self, scene_templates: dict):
        self.scene_templates: dict = scene_templates
        self.current_purpose: str = ''
        self.processors = {}
        # 添加对话历史记录
        self.conversation_history = []

    # 添加获取历史对话的方法
    def get_history_str(self, max_turns=3):
        """
        获取格式化的历史对话字符串
        :param max_turns: 获取最近的几轮对话，默认3轮
        :return: 格式化的历史对话字符串
        """
        history_str = ""
        for turn in self.conversation_history[-max_turns:]:  # 只取最近的几轮对话
            history_str += f"用户: {turn['user']}\n系统: {turn['system']}\n"
        return history_str
    
    # 添加更新历史对话的方法
    def update_conversation_history(self, user_input, response, max_history=100):
        """
        更新对话历史记录
        :param user_input: 用户输入
        :param response: 系统响应
        :param max_history: 保留的最大历史记录数量
        """
        self.conversation_history.append({
            'user': user_input,
            'system': response
        })
        
        # 限制历史记录长度，防止过长
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]

    @staticmethod
    def load_scene_processor(self, scene_config):
        try:
            return CommonProcessor(scene_config)
        except (ImportError, AttributeError, KeyError):
            raise ImportError(f"未找到场景处理器 scene_config: {scene_config}")

    # 在ChatbotModel类中添加以下方法
    def get_dynamic_threshold(self):
        """
        根据对话复杂度动态调整意图相关阈值
        :return: 调整后的阈值(0.0-1.0)
        """
        base_threshold = RELATED_INTENT_THRESHOLD
        history_length = len(self.conversation_history)
        
        # 对话越长，阈值越低(容忍更多相关变化)
        if history_length > 10:
            return max(0.3, base_threshold * 0.7)
        elif history_length > 5:
            return max(0.4, base_threshold * 0.8)
        return base_threshold
    
    # 修改is_related_to_last_intent方法
    def summarize_history(self, max_length=500):
        """
        对对话历史进行摘要压缩，保留关键信息
        :param max_length: 摘要最大长度
        :return: 压缩后的摘要文本
        """
        if len(self.conversation_history) <= 3:  # 对话轮次少时不压缩
            return self.get_history_str()
        
        prompt = """请对以下对话历史进行摘要，保留与当前意图相关的关键信息:
        
    对话历史:
    {history}
    
    请用简洁的语言总结对话中的关键信息，特别是与当前场景"{current_scene}"相关的内容:"""
        
        full_history = self.get_history_str(max_turns=len(self.conversation_history))
        summary = send_message(
            prompt.format(
                history=full_history,
                current_scene=self.scene_templates.get(self.current_purpose, {}).get('name', '')
            ),
            None
        )
        return summary[:max_length]  # 限制摘要长度
    
    # 修改is_related_to_last_intent方法
    def is_related_to_last_intent(self, user_input):
        print("判断当前输入是否与上一次意图场景相关")
        if not self.current_purpose:
            return False
        
        # 使用摘要代替完整历史
        history_str = self.summarize_history()
        
        # 检查是否为日期相关场景和输入
        is_date_scene = "日期" in self.scene_templates[self.current_purpose]['description']
        
        # 扩展日期词汇列表，包含更多时间表达
        date_terms = ['今天', '明天', '昨天', '下周', '上周', '本月', '下个月', '后天', 
                     '大后天', '上个月', '下一年', '上一年', '刚才', '现在', '之前']
        
        # 检查是否包含时间词汇的简短回复
        is_time_related_short_reply = len(user_input) < 10 and any(term in user_input for term in date_terms)
        
        # 如果是简短的时间相关回复，直接判定为相关
        if is_time_related_short_reply:
            print("检测到时间相关简短回复，判定为相关")
            return True
            
        # 如果是日期场景且用户输入包含日期词汇，提高关联度
        if is_date_scene and any(term in user_input for term in date_terms):
            print("检测到日期相关输入，提高关联度")
            return True
        
        # 改进的提示词，强调考虑上下文连贯性和简短回复的情况
        prompt = f"""判断当前用户输入内容与当前对话场景的关联性:
    
    历史对话:
    {history_str}
    
    当前对话场景: {self.scene_templates[self.current_purpose]['description']}
    当前用户输入: {user_input}
    
    请特别注意：
    1. 如果用户输入是简短回复（如"明天呢"、"那上海呢"等），请结合历史对话判断其完整意图
    2. 对于时间、地点等补充性质的简短回复，应视为对上一轮查询的延续，关联度较高
    
    请从以下三个维度评估关联度:
    1. 语义相关性: 输入内容与场景主题的相关程度
    2. 上下文连贯性: 输入是否是对上一轮对话的直接回应，或者是对历史对话的扩展
    3. 意图匹配度: 用户意图是否与当前场景一致
    
    综合以上三点，给出最终关联度得分（仅用小数回答，范围0.0至1.0）"""
    
        result = send_message(prompt, None)
        print("result:", result)
        current_threshold = self.get_dynamic_threshold()
        
        # 对于日期场景，降低阈值要求
        if is_date_scene:
            current_threshold = min(0.3, current_threshold)
            
        # 对于简短回复，降低阈值要求
        if len(user_input) < 10:
            current_threshold = min(0.25, current_threshold)
            
        return extract_float(result) > current_threshold

    def recognize_intent(self, user_input):
        # 根据场景模板生成选项
        purpose_options = {}
        purpose_description = {}
        index = 1
        for template_key, template_info in self.scene_templates.items():
            purpose_options[str(index)] = template_key
            purpose_description[str(index)] = template_info["description"]
            index += 1
        options_prompt = "\n".join([f"{key}. {value} - 请回复{key}" for key, value in purpose_description.items()])
        options_prompt += "\n0. 其他场景 - 请回复0"

        # 使用新方法获取历史对话字符串
        history_str = self.get_history_str()
        print("history_str: ",history_str)
        
        # 发送选项给用户，包含历史对话
        prompt = f"""有下面多种场景，需要你根据用户输入和历史对话进行判断，只答选项

历史对话:
{history_str}

可选场景:
{options_prompt}

用户输入：{user_input}
将历史对话和用户输入一起进行判断，尤其看重最后一轮对话,如果用户输入和历史对话无关则仅根据用户新的输入进行判断，请选择最相关的场景，
请回复序号："""
        
        user_choice = send_message(prompt, user_input)

        logging.debug(f'purpose_options: %s', purpose_options)
        logging.debug(f'user_choice: %s', user_choice)

        user_choices = extract_continuous_digits(user_choice)

        # 根据用户选择获取对应场景
        if user_choices and user_choices[0] != '0':
            self.current_purpose = purpose_options[user_choices[0]]

        if self.current_purpose:
            print(f"用户选择了场景：{self.scene_templates[self.current_purpose]['name']}")
        else:
            # 用户输入的选项无效的情况，可以进行相应的处理
            print("无效的选项，请重新选择")

    def get_processor_for_scene(self, scene_name):
        """
        根据场景名称获取相应的处理器实例。
        
        如果处理器实例已存在，则直接返回该实例。
        如果处理器实例不存在，则尝试根据场景配置创建处理器类，并将其缓存以供后续使用。
        
        参数:
        - scene_name: 场景名称，用于查找对应的处理器或配置。
        
        返回:
        - 处理器实例，如果找到并成功创建。
        
        异常:
        - ValueError: 如果未找到指定名称的场景配置。
        """
        # 检查是否已缓存处理器实例
        if scene_name in self.processors:
            return self.processors[scene_name]
    
        # 尝试从场景模板中获取配置
        scene_config = self.scene_templates.get(scene_name)
        if not scene_config:
            raise ValueError(f"未找到名为{scene_name}的场景配置")
    
        # 根据场景配置加载或创建处理器类
        processor_class = self.load_scene_processor(self, scene_config)
        # 将新创建的处理器实例缓存
        print("将新创建的处理器实例缓存")
        self.processors[scene_name] = processor_class
        return self.processors[scene_name] 

    # 在ChatbotModel类中添加以下方法
    def get_clarification_question(self, user_input):
        """
        当意图模糊时生成澄清问题
        :return: 澄清问题文本 or None(当不需要澄清时)
        """
        if not self.current_purpose:
            return None
        
        # 计算当前输入与场景的匹配度
        prompt = f"""判断用户输入与场景的匹配度:
    
    当前场景: {self.scene_templates[self.current_purpose]['name']}
    场景描述: {self.scene_templates[self.current_purpose]['description']}
    用户输入: {user_input}
    
    请评估匹配程度(0.0-1.0)，并建议是否需要澄清(当得分在0.3-0.7之间时):
    匹配度得分: <请只返回一个0.0-1.0的小数>
    是否需要澄清: <是/否>
    建议澄清问题: <问题文本>"""
    
        response = send_message(prompt, None)
        parts = response.split('\n')
        print(parts)
        if len(parts) >= 3 and "是" in parts[1]:
            return parts[2].replace("建议澄清问题: ", "")
        return None
    
    def complete_user_intent(self, user_input):
        """
        对简短的用户输入进行上下文理解和补充
        :param user_input: 用户输入
        :return: 基于上下文补充后的用户输入
        """
        # 获取历史对话
        history_str = self.get_history_str(max_turns=3)
        
        # 获取当前场景信息（如果有）
        current_scene_info = ""
        if self.current_purpose and self.current_purpose in self.scene_templates:
            scene = self.scene_templates[self.current_purpose]
            current_scene_info = f"""
    当前对话场景: {scene['name']}
    场景描述: {scene['description']}
    """
        
        # 首先检查是否为常见的闲聊问题
        chat_patterns = ["你是谁", "你好", "你叫什么", "你能做什么", "谢谢"]
        if any(pattern in user_input for pattern in chat_patterns):
            print(f"检测到闲聊问题，不进行补全: {user_input}")
            return user_input
        
        # 使用大模型进行上下文理解和补充，更强调保持原始意图
        prompt = f"""根据历史对话和当前场景，对用户的简短输入进行上下文理解和补充:
    
        历史对话:
        {history_str}
    
        {current_scene_info}
        当前用户输入: {user_input}
    
        请注意:
        1. 如果用户输入是闲聊类问题(如"你是谁"、"你好"等)，请直接返回原始输入，不要进行补充
        2. 如果用户输入是完全新的查询，与历史对话无关，请直接返回原始输入
        3. 只有当用户输入明确是对历史对话的延续或补充时，才进行上下文补充
    
        例如:
        - 如果历史对话是查询"北京分行今天的贷款余额"，而当前输入是"明天呢"，则补充为"北京分行明天的贷款余额"
        - 如果当前输入是"你是谁"，则保持不变，返回"你是谁"
        - 如果当前输入是"上海分行呢"，而历史对话是关于北京分行的查询，则补充为"上海分行的[相同查询内容]"
    
        请直接输出补充后的结果(不要包含解释): """
        
        completed_intent = send_message(prompt, None)
        print(f"原始输入: {user_input}")
        print(f"上下文补充: {completed_intent}")
        
        # 检查补全结果是否合理
        if completed_intent is None:
            return user_input
            
        if len(completed_intent) < len(user_input) or completed_intent == user_input:
            # 补全结果不合理，返回原始输入
            return user_input
        
        return completed_intent
    
    def identify_intent_type(self, user_input):
        """
        识别用户输入的意图类型
        :param user_input: 用户输入
        :return: 意图类型 (query/help/system/chat)
        """
        # 获取历史对话上下文
        history_context = self.get_history_str(max_turns=2)
        
        # 快速模式匹配，提高效率
        system_keywords = ["你是谁", "你叫什么", "你是什么", "你能做什么"]
        help_keywords = ["怎么用", "如何使用", "帮助", "使用说明", "怎么查", "如何查询"]
        chat_keywords = ["你好", "谢谢", "再见", "晚安", "早上好"]
        
        # 优先检查闲聊和系统问题，避免被错误识别为查询
        if any(keyword in user_input for keyword in system_keywords):
            print(f"快速匹配到系统相关问题: {user_input}")
            return "system"
        
        if any(keyword in user_input for keyword in chat_keywords):
            print(f"快速匹配到闲聊问题: {user_input}")
            return "chat"
        
        if any(keyword in user_input for keyword in help_keywords):
            print(f"快速匹配到帮助问题: {user_input}")
            return "help"
        
        # 构建场景描述信息，用于增强查询意图识别
        scene_descriptions = []
        for scene_key, scene_info in self.scene_templates.items():
            scene_descriptions.append(f"- {scene_info['name']}: {scene_info['description']}")
        
        scenes_info = "\n".join(scene_descriptions)
        
        # 使用大模型进行更精确的意图分类，加入场景描述
        prompt = f"""作为智能助手，请判断用户输入的意图类型：
    
        历史对话:
        {history_context}
    
        用户输入: "{user_input}"
    
        系统支持的查询场景:
        {scenes_info}
    
        请从以下意图类型中选择最匹配的一个:
        - query: 数据查询意图，用户想获取具体业务指标数据，与上述查询场景相关
        - help: 帮助意图，用户想了解系统功能或使用方法
        - system: 系统相关意图，用户询问系统定位、能力等(如"你是谁"、"你能做什么")
        - chat: 闲聊意图，用户进行普通性质的对话(如问候、感谢等)
    
        判断时请特别注意：
        1. 如果用户输入包含"你是谁"、"你叫什么"等询问系统身份的问题，应判断为system意图
        2. 如果用户输入是简单的问候或社交性质的对话，应判断为chat意图
        3. 只有明确询问业务数据的问题才判断为query意图
    
        请只返回意图类型的关键词(query/help/system/chat)，不要包含其他内容: """
        
        intent = send_message(prompt, None)
        # 清理并标准化结果
        if intent:
            intent = intent.strip().lower()
            
            # 确保返回有效的意图类型
            valid_intents = ["query", "help", "system", "chat"]
            for valid_intent in valid_intents:
                if valid_intent in intent:
                    print(f"LLM识别到的意图类型: {valid_intent}")
                    return valid_intent
        
        # 如果包含"你是谁"等关键词，强制返回system类型
        if any(keyword in user_input for keyword in ["你是谁", "你叫什么", "你是什么"]):
            print("强制识别为system类型")
            return "system"
        
        # 默认返回查询意图
        print("默认返回query类型")
        return "query"

    def process_multi_question(self, user_input):
        # 保存原始输入，用于历史记录
        original_input = user_input
        from utils.date_utils import get_current_date
        current_date = get_current_date()
           # 创建上下文信息，包含当前日期
        context = {
        "history": self.get_history_str(),
        "current_date": current_date
        }
        
        # 第一步：识别意图类型（基于原始输入）
        intent_type = self.identify_intent_type(user_input)
        print(f"识别到的意图类型: {intent_type}")
        
        # 对于闲聊和系统问题，不进行意图补全
        if intent_type not in ["system", "chat", "help"]:
            # 第二步：对简短输入进行上下文补充
            if len(user_input) < 15 and self.conversation_history:
                # 对简短输入进行上下文补充
                user_input = self.complete_user_intent(user_input)
                print(f"上下文补充: {original_input} -> {user_input}")
        
        # 第三步：根据意图类型分发处理
        if intent_type == "system":
            # 系统相关意图
            response = self.handle_system_intent(user_input)
            self.update_conversation_history(original_input, response)
            return response
        elif intent_type == "help":
            # 帮助意图
            response = self.handle_help_intent(user_input)
            self.update_conversation_history(original_input, response)
            return response
        elif intent_type == "chat":
            # 闲聊意图，直接使用handle_basic_chat
            response = self.handle_basic_chat(user_input)
            if response:
                self.update_conversation_history(original_input, response)
                return response
            else:
                # 如果handle_basic_chat返回None，但意图类型是chat，则直接处理闲聊
                chat_prompt = f"""
        作为智能助手，请对用户的问题给出简洁、准确的回答:
    
        用户问题: {user_input}
    
        请直接回答问题，不需要解释或引导: """
                response = send_message(chat_prompt, None)
                self.update_conversation_history(original_input, response)
                return response
        
        # 第四步：处理查询意图
        # 检查当前输入是否与上一次的意图场景相关
        if self.current_purpose and self.is_related_to_last_intent(user_input):
            print("相关")
        else:
            print("不相关")
            # 先尝试获取澄清问题
            clarification = self.get_clarification_question(user_input)
            if clarification:
                self.update_conversation_history(original_input, clarification)
                return clarification
            
            # 没有澄清问题或用户确认后，重新识别意图
            self.recognize_intent(user_input)
        logging.info('current_purpose: %s', self.current_purpose)
    
        if self.current_purpose in self.scene_templates:
            # 根据场景模板调用相应场景的处理逻辑
            self.get_processor_for_scene(self.current_purpose)
            # 获取历史对话作为上下文传递给处理器
            
            # 使用补全后的意图进行处理，但在历史记录中保存原始输入
            response = self.processors[self.current_purpose].process(user_input, context)
            self.update_conversation_history(original_input, response)
            return response
        
        # 第六步：处理未命中场景的情况
        guide_keywords = ["怎么查", "如何查询", "怎样使用", "如何使用", "查询方法", "使用说明", "帮助"]
        if any(keyword in user_input for keyword in guide_keywords):
            guide_response = """您好！您可以通过以下方式查询数据：
    1. 直接询问具体业务数据，例如："北京分行昨天的存款余额"
    2. 指定分行名称、日期和查询类型，例如："查询上海分行2024年3月的贷款新增数据"
    3. 组合多个查询条件，例如："对比北京和南京分行上季度的对公存款余额"
    需要其他帮助请随时告诉我！"""
            self.update_conversation_history(original_input, guide_response)
            return guide_response
            
        response = '未命中场景，您可以尝试询问特定分行的数据，例如："北京分行昨天的存款余额是多少"'
        self.update_conversation_history(original_input, response)
        return response

    def load_mappings(self):
        with open('config/mapping_config.json', 'r', encoding='utf-8') as f:
            self.mappings = json.load(f)
        # 可添加定期刷新或监听文件变化逻辑
    
    # 处理simple-chat 处理逻辑
    def handle_basic_chat(self, user_input):
        """
        处理基础对话，如问候、身份询问等
        :param user_input: 用户输入
        :return: 回复文本或None(如果不是基础对话)
        """
        basic_patterns = {
            "你是谁": "我是数据查询智能助手，可以帮您查询银行业务数据和回答相关问题。",
            "你叫什么": "我是数据查询智能助手，很高兴为您服务。",
            "你好": "您好！我是数据查询智能助手，有什么可以帮助您的吗？",
            "你能做什么": "我可以帮您查询银行业务数据、回答金融相关问题，以及提供其他信息服务。请告诉我您需要什么帮助。"
        }
        
        # 简单模式匹配 - 这些模式通常是明确的闲聊
        for pattern, response in basic_patterns.items():
            if pattern in user_input:
                return response
        
        # 获取历史对话上下文
        history_context = self.get_history_str(max_turns=3)  # 获取最近3轮对话
        
        # 判断是否有活跃的业务场景
        has_active_business = False
        if self.current_purpose and len(self.conversation_history) > 0:
            # 检查最近的对话是否与业务相关
            business_keywords = ["查询", "余额", "转账", "存款", "贷款", "利率", "账户", "分行","支行"]
            last_system_msg = self.conversation_history[-1].get('system', '')
            if any(keyword in last_system_msg for keyword in business_keywords):
                has_active_business = True
        
        # 如果有活跃业务场景且用户输入简短，可能是业务对话的继续而非闲聊
        if has_active_business and len(user_input) < 10:
            # 简短回复在业务场景中通常不是闲聊
            return None
        
        # 结合历史对话判断是否为闲聊
        prompt = f"""
根据历史对话和当前用户输入，判断用户当前输入是否为闲聊:

历史对话:
{history_context}

当前用户输入: {user_input}

请考虑以下因素:
1. 当前输入是否是对之前业务问题的回答或补充
2. 当前输入是否引入了新的业务查询意图
3. 当前输入是否仅为社交性质的对话(如问候、感谢等)

综合判断当前输入是否为闲聊，请只回答true或false: """
        
        # 只有当输入不太长时才进行判断，避免性能浪费
        if len(user_input) < 20:
            result = send_message(prompt, None)
            if result is None:
                print("警告: 闲聊判断API调用失败")
                # 保守处理，当API调用失败时假设不是闲聊
                return None
            
            print("闲聊判断结果:", result)  # 添加调试输出
            
            # 使用更精确的判断逻辑
            result_lower = result.lower().strip()
            is_chat = "true" in result_lower
            
            print(f"闲聊判断结果解析: {'是闲聊' if is_chat else '非闲聊'}")
                
            if is_chat:
                print("判断为闲聊，生成回复")
                # 生成合适的闲聊回复，同时考虑历史上下文
                chat_prompt = f"""
作为智能助手，请根据历史对话上下文，对用户的闲聊输入给出友好、专业、连贯的回复:

历史对话:
{history_context}

用户输入: {user_input}

请给出自然、友好的回复，保持对话的连贯性: """
                return send_message(chat_prompt, None)
            else:
                # 明确不是闲聊，返回None
                print("判断为非闲聊，返回None")
                return None
        
        return None  # 不是基础对话，返回None



