# encoding=utf-8
import json
import logging

from config import RELATED_INTENT_THRESHOLD
from scene_processor.impl.common_processor import CommonProcessor
from utils.data_format_utils import extract_continuous_digits, extract_float
from utils.helpers import send_message
from utils.data_api import query_data_by_scene  # 添加导入

logger = logging.getLogger(__name__)  # 添加logger定义

class ChatbotModel:
    def __init__(self, scene_templates: dict):
        self.scene_templates: dict = scene_templates
        self.current_purpose: str = ''
        self.processors = {}
        # 添加对话历史记录
        self.conversation_history = []

    # 添加获取历史对话的方法
    def get_history_str(self, max_turns=3):
        """
        获取格式化的历史对话字符串
        :param max_turns: 获取最近的几轮对话，默认3轮
        :return: 格式化的历史对话字符串
        """
        history_str = ""
        for turn in self.conversation_history[-max_turns:]:  # 只取最近的几轮对话
            history_str += f"用户: {turn['user']}\n系统: {turn['system']}\n"
        return history_str
    
    # 添加更新历史对话的方法
    def update_conversation_history(self, user_input, response, max_history=100):
        """
        更新对话历史记录
        :param user_input: 用户输入
        :param response: 系统响应
        :param max_history: 保留的最大历史记录数量
        """
        self.conversation_history.append({
            'user': user_input,
            'system': response
        })
        
        # 限制历史记录长度，防止过长
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]

    @staticmethod
    def load_scene_processor(self, scene_config):
        try:
            return CommonProcessor(scene_config)
        except (ImportError, AttributeError, KeyError):
            raise ImportError(f"未找到场景处理器 scene_config: {scene_config}")

    # 在ChatbotModel类中添加以下方法
    def get_dynamic_threshold(self):
        """
        根据对话复杂度动态调整意图相关阈值
        :return: 调整后的阈值(0.0-1.0)
        """
        base_threshold = RELATED_INTENT_THRESHOLD
        history_length = len(self.conversation_history)
        
        # 对话越长，阈值越低(容忍更多相关变化)
        if history_length > 10:
            return max(0.3, base_threshold * 0.7)
        elif history_length > 5:
            return max(0.4, base_threshold * 0.8)
        return base_threshold
    
    # 修改is_related_to_last_intent方法
    async def summarize_history(self, max_length=500):
        """
        对对话历史进行摘要压缩，保留关键信息
        :param max_length: 摘要最大长度
        :return: 压缩后的摘要文本
        """
        if len(self.conversation_history) <= 3:  # 对话轮次少时不压缩
            return self.get_history_str()
        
        prompt = """请对以下对话历史进行摘要，保留与当前意图相关的关键信息:
        
    对话历史:
    {history}
    
    请用简洁的语言总结对话中的关键信息，特别是与当前场景"{current_scene}"相关的内容:"""
        
        full_history = self.get_history_str(max_turns=len(self.conversation_history))
        # 添加await关键字
        summary = await send_message(
        prompt.format(
            history=full_history,
            current_scene=self.scene_templates.get(self.current_purpose, {}).get('name', '')
        ),
        None
        )
        return summary[:max_length]  # 限制摘要长度
    
    # 修改is_related_to_last_intent方法
    async def is_related_to_last_intent(self, user_input):
        print("判断当前输入是否与上一次场景相关")
        if not self.current_purpose:
            return False
        
        # 使用摘要代替完整历史
        history_str = await self.summarize_history()
        
        # 检查是否为日期相关场景和输入
        is_date_scene = "日期" in self.scene_templates[self.current_purpose]['description']
        
        # 扩展日期词汇列表，包含更多时间表达
        date_terms = ['今天', '明天', '昨天', '下周', '上周', '本月', '下个月', '后天', 
                     '大后天', '上个月', '下一年', '上一年', '刚才', '现在', '之前']
        
        # 检查是否包含时间词汇的简短回复
        is_time_related_short_reply = len(user_input) < 4 and any(term in user_input for term in date_terms)
        
        # 如果是简短的时间相关回复，直接判定为相关
        if is_time_related_short_reply:
            print("检测到时间相关简短回复，判定为相关")
            return True
            
        # 如果是日期场景且用户输入包含日期词汇，提高关联度
        if is_date_scene and any(term in user_input for term in date_terms):
            print("检测到日期相关输入，提高关联度")
            return True
        # 构建场景描述信息，用于增强查询意图识别
    # 构建场景描述信息，用于增强查询意图识别
        scene_descriptions = []
        for scene_key, scene_info in self.scene_templates.items():
            # 增加更详细的场景描述，包括参数和示例
            params_desc = ""
            if "parameters" in scene_info:
                params = [f"{p['name']}({p['desc']})" for p in scene_info["parameters"]]
                params_desc = f"参数: {', '.join(params)}"
            
            example = f"示例: {scene_info.get('example', '无')}" if "example" in scene_info else ""
            
            scene_descriptions.append(
                f"- {scene_info['name']}: {scene_info['description']} {params_desc} {example}"
            )
        
        scenes_info = "\n".join(scene_descriptions)
        
        # 改进的提示词，强调考虑上下文连贯性和简短回复的情况
        prompt = f"""判断当前用户输入内容与当前对话场景的关联性:
    
    历史对话:
    {history_str}
    
    当前对话场景: {self.scene_templates[self.current_purpose]['description']}
    当前用户输入: {user_input}
    所有的场景 {scenes_info}
    
    请特别注意：
    1. 如果用户输入是简短回复（如"明天呢"、"那上海呢"等），请结合历史对话判断其完整意图
    2. 对于时间、地点等补充性质的简短回复，应视为对上一轮查询的延续，关联度较高
    
    请从以下三个维度评估关联度:
    1. 关键词和维度必要性: 输入内容与场景scenes_info关键词和维度的匹配程度，尤其是输入内容中有的维度，scenes_info中应包含，
    同时注意：scenes_info 必要的维度，query 中必须有， 
    案例：“今天南京分行对公板块的对公一般性贷款余额” 为 "银行总行、分行板块、条线经营数据服务"场景 与“今天南京分行对公一般性贷款余额” 为银行总分行经营查询“ 场景
    2. 上下文连贯性: 输入是否是对上一轮对话的直接回应，或者是对历史对话的扩展
    3. 场景匹配度: 用户场景是否与当前场景是否一致
    
    综合以上三点，给出最终关联度得分（仅用小数回答，范围0.0至1.0）"""

    
        prompt2 = f"""判断当前用户输入内容与当前对话场景的关联性:
    
    历史对话:
    {history_str}
    
    当前对话场景: {self.scene_templates[self.current_purpose]['description']}
    当前场景必要维度: {self.scene_templates[self.current_purpose].get('required_dimensions', '分行名称, 板块名称, 条线名称, 项目名称')}
    当前用户输入: {user_input}
    所有的场景: {scenes_info}
    
    请特别注意：
    1. 如果用户输入是简短回复（如"明天呢"、"那上海呢"等），请结合历史对话判断其完整意图
    2. 对于时间、地点等补充性质的简短回复，应视为对上一轮查询的延续，关联度较高
    3. 对于缺少必要维度的查询，应判定为低关联度
    
    请从以下三个维度评估关联度:
    1. 必要维度匹配度(权重50%): 
       - 用户输入必须包含当前场景的必要维度，缺少任一必要维度都应大幅降低关联度
       - 对于"银行总行、分行板块、条线经营数据服务"场景，必须同时包含分行名称、板块名称和条线名称
    2. 上下文连贯性(权重30%): 
       - 输入是否是对上一轮对话的直接回应或扩展
       - 仅更换查询对象但保持查询结构相同的情况，连贯性较高
    3. 场景匹配度(权重20%): 
       - 用户意图是否与当前场景的核心功能一致
    
    评分标准:
    - 0.0-0.3: 低关联，缺少多个必要维度或完全不同的查询意图
    - 0.3-0.7: 中等关联，缺少部分必要维度但查询意图相似
    - 0.7-1.0: 高关联，包含所有必要维度且查询意图一致
    
    综合以上三点，给出最终关联度得分（仅用小数回答，范围0.0至1.0）"""


    # 结构化场景对比描述
        scene_comparison = """核心场景对比表：
| 场景名称               | 必要维度                          | 典型查询示例                          | 排除规则                          |
|------------------------|-----------------------------------|---------------------------------------|-----------------------------------|
| 银行总分行经营查询      | 分行名称+项目名称                 | "北京分行存款余额"                    | 缺少分行/项目维度                  |
| 银行板块条线经营查询    | 分行名称+板块名称+条线名称        | "北京分行对公板块对公条线贷款余额"    | 缺少板块/条线维度                  |
| 对公客户经营指标查询    | 客户名称+项目名称                 | "比亚迪公司贷款余额"                 | 缺少客户名称维度                  |"""

    # 优化后的提示词
        prompt3 = f"""判断当前用户输入内容与当前对话场景的关联性：

{scene_comparison}

历史对话摘要：
{history_str}

当前场景要求：
场景名称：{self.scene_templates[self.current_purpose]['name']}
必要维度：{self.scene_templates[self.current_purpose].get('required_dimensions', '分行名称、板块名称、条线名称')}
典型特征：{self.scene_templates[self.current_purpose]['description']}

待判断输入：
"{user_input}"

评估要点：
1. (权重50%) 维度完备性检查：
   - 是否包含当前场景所有必要维度？
   - 是否包含其他场景的排除性维度？

2. (权重30%) 上下文一致性：
   - 是否延续历史对话的查询结构？
   - 是否改变核心维度组合？

3. (权重20%) 意图匹配度：
   - 是否符合当前场景的典型特征？
   - 是否存在跨场景的维度组合？

评分标准：
0.7-1.0：完全匹配必要维度，延续上下文结构
0.4-0.6：部分匹配维度，但核心意图一致
0.0-0.3：缺少必要维度或符合排除规则

请给出 0.0-1.0 的关联度评分："""





        # 添加await关键字
        result = await send_message(prompt3, None)
        print("result:", result)
        current_threshold = self.get_dynamic_threshold()
        
        # 对于日期场景，降低阈值要求
        if is_date_scene:
            current_threshold = min(0.3, current_threshold)
            
        # 对于简短回复，降低阈值要求
        if len(user_input) < 10:
            current_threshold = min(0.25, current_threshold)
            
        return extract_float(result) > current_threshold

    async def recognize_intent(self, user_input):
        # 根据场景模板生成选项
        purpose_options = {}
        purpose_description = {}
        index = 1
        for template_key, template_info in self.scene_templates.items():
            purpose_options[str(index)] = template_key
            purpose_description[str(index)] = template_info["description"]
            index += 1
        options_prompt = "\n".join([f"{key}. {value} - 请回复{key}" for key, value in purpose_description.items()])
        options_prompt += "\n0. 其他场景 - 请回复0"

        # 使用新方法获取历史对话字符串
        history_str = self.get_history_str()
        print("history_str: ",history_str)
        
        # 发送选项给用户，包含历史对话
        prompt = f"""有下面多种场景，需要你根据用户输入和历史对话进行判断，只答选项

历史对话:
{history_str}

可选场景:
{options_prompt}

用户输入：{user_input}
将历史对话和用户输入一起进行判断，尤其看重最后一轮对话,如果用户输入和历史对话无关则仅根据用户新的输入进行判断，请选择最相关的场景，
请回复序号："""
        
        # 添加await关键字
        user_choice = await send_message(prompt, user_input)

        logging.debug(f'purpose_options: %s', purpose_options)
        logging.debug(f'user_choice: %s', user_choice)

        user_choices = extract_continuous_digits(user_choice)

        # 根据用户选择获取对应场景
        if user_choices and user_choices[0] != '0':
            self.current_purpose = purpose_options[user_choices[0]]

        if self.current_purpose:
            print(f"用户选择了场景：{self.scene_templates[self.current_purpose]['name']}")
        else:
            # 用户输入的选项无效的情况，可以进行相应的处理
            print("无效的选项，请重新选择")

    def get_processor_for_scene(self, scene_name):
        """
        根据场景名称获取相应的处理器实例。
        
        如果处理器实例已存在，则直接返回该实例。
        如果处理器实例不存在，则尝试根据场景配置创建处理器类，并将其缓存以供后续使用。
        
        参数:
        - scene_name: 场景名称，用于查找对应的处理器或配置。
        
        返回:
        - 处理器实例，如果找到并成功创建。
        
        异常:
        - ValueError: 如果未找到指定名称的场景配置。
        """
        # 检查是否已缓存处理器实例
        if scene_name in self.processors:
            return self.processors[scene_name]
    
        # 尝试从场景模板中获取配置
        scene_config = self.scene_templates.get(scene_name)
        if not scene_config:
            raise ValueError(f"未找到名为{scene_name}的场景配置")
    
        # 根据场景配置加载或创建处理器类
        processor_class = self.load_scene_processor(self, scene_config)
        # 将新创建的处理器实例缓存
        print("将新创建的处理器实例缓存")
        self.processors[scene_name] = processor_class
        return self.processors[scene_name] 

    # 在ChatbotModel类中添加以下方法
    # 将 get_clarification_question 方法改为异步方法
    async def get_clarification_question(self, user_input):
        """
        当意图模糊时生成澄清问题
        :return: 澄清问题文本 or None(当不需要澄清时)
        """
        if not self.current_purpose:
            return None
        
        # 计算当前输入与场景的匹配度
        prompt = f"""判断用户输入与场景的匹配度:
    
    当前场景: {self.scene_templates[self.current_purpose]['name']}
    场景描述: {self.scene_templates[self.current_purpose]['description']}
    用户输入: {user_input}
    
    请评估匹配程度(0.0-1.0)，并建议是否需要澄清(当得分在0.3-0.7之间时):
    匹配度得分: <请只返回一个0.0-1.0的小数>
    是否需要澄清: <是/否>
    建议澄清问题: <问题文本>"""
    
        # 添加 await 关键字
        response = await send_message(prompt, None)
        parts = response.split('\n')
        print(parts)
        if len(parts) >= 3 and "是" in parts[1]:
            return parts[2].replace("建议澄清问题: ", "")
        return None
    
    # 在identify_intent_type方法中添加await关键字
    async def identify_intent_type(self, user_input):
        """识别用户输入的意图类型"""
        # 获取历史对话上下文
        history_context = self.get_history_str(max_turns=2)
        
        # 定义意图类型
        intent_types = {
            "query": "数据查询意图，用户想获取具体业务数据",
            "help": "帮助意图，用户想了解系统功能或使用方法",
            "system": "系统相关意图，用户询问系统定位、能力、当前时间 等",
            "chat": "除了上面的意图之外的意图，如闲聊、普通对话，用户进行社交性质的对话等"
        }
        
        # 快速模式匹配，提高效率
        system_keywords = ["你是谁", "你叫什么", "你是什么", "你能做什么"]
        help_keywords = ["怎么用", "如何使用", "帮助", "使用说明", "怎么查", "如何查询"]
        
        if any(keyword in user_input for keyword in system_keywords):
            return "system"
        
        if any(keyword in user_input for keyword in help_keywords):
            return "help"
        
    # 构建场景描述信息，用于增强查询意图识别
    # 构建场景描述信息，用于增强查询意图识别
        scene_descriptions = []
        for scene_key, scene_info in self.scene_templates.items():
            # 增加更详细的场景描述，包括参数和示例
            params_desc = ""
            if "parameters" in scene_info:
                params = [f"{p['name']}({p['desc']})" for p in scene_info["parameters"]]
                params_desc = f"参数: {', '.join(params)}"
            
            example = f"示例: {scene_info.get('example', '无')}" if "example" in scene_info else ""
            
            scene_descriptions.append(
                f"- {scene_info['name']}: {scene_info['description']} {params_desc} {example}"
            )
        
        scenes_info = "\n".join(scene_descriptions)
        
        # 使用大模型进行更精确的意图分类，加入场景描述
        prompt = f"""作为智能助手，请判断用户输入的意图类型：

历史对话:
{history_context}

用户输入: "{user_input}"

系统支持的查询场景:
{scenes_info}

请从以下意图类型中选择最匹配的一个:
- query: 数据查询意图，用户想获取具体业务指标数据，与上述查询场景相关
- help: 帮助意图，用户想了解系统功能或使用方法
- system: 系统相关意图，用户询问系统定位、能力等
- chat: 闲聊意图，用户进行普通性质的对话

判断时请特别注意：
1. 如果用户输入与系统支持的查询场景相关，判断为query意图
2. 即使表述不明确，只要是询问数据的问题，也应判断为query意图
3. 只有明确的系统询问、帮助请求或纯普通对话才判断为其他意图类型

请只返回意图类型的关键词(query/help/system/chat)，不要包含其他内容: """
        
        intent = await send_message(prompt, None)
        # 清理并标准化结果
        intent = intent.strip().lower()
        
        # 确保返回有效的意图类型
        for intent_type in intent_types.keys():
            if intent_type in intent:
                return intent_type
        
        # 默认返回查询意图
        return "query"
    
    # 修改handle_system_intent为异步方法
    async def handle_system_intent(self, user_input):
        """
        处理系统相关的问题
        """
        # 导入日期工具
        from utils.date_utils import get_current_date
        
        system_info = {
            "name": "数据查询智能助手",
            "version": "1.0.0",
            "capabilities": [
                "总分行经营数据查询",
                "绩效数据查询",
                #"多轮对话理解",
               # "上下文记忆"
            ],
            "current_date": get_current_date()  # 添加当前日期信息
        }
        
        # 使用现有的历史对话
        history_str = self.get_history_str()
        
        prompt = f"""作为数据查询智能助手，请回答用户关于系统的问题。

系统信息:
- 名称: {system_info['name']}
- 版本: {system_info['version']}
- 主要功能: {', '.join(system_info['capabilities'])}
- 当前日期: {system_info['current_date']}

历史对话:
{history_str}

用户问题: "{user_input}"

请提供专业、准确、简洁的回答。如果用户询问当前日期或时间，请直接告知系统当前日期。: """
        
        # 添加await关键字
        response = await send_message(prompt, None)
        return response
    
    # 修改handle_help_intent方法中的identify_help_scene调用
    async def handle_help_intent(self, user_input):
        """
        处理帮助类问题
        """
        # 导入日期工具
        from utils.date_utils import get_current_date
        
        # 获取历史对话
        history_str = self.get_history_str()
        
        # 尝试识别用户想了解哪个场景的帮助
        # 修改为await调用
        scene_help = await self.identify_help_scene(user_input)
        
        # 添加当前日期信息
        current_date = get_current_date()
        
        if scene_help and scene_help in self.scene_templates:
            # 提供特定场景的帮助信息
            scene_info = self.scene_templates[scene_help]
            parameters_info = "\n".join([f"- {param['name']}: {param['desc']}" 
                                      for param in scene_info.get('parameters', [])])
            
            help_text = f"""【{scene_info['name']}】使用帮助：

{scene_info['description']}

可用参数：
{parameters_info}

示例：
{scene_info.get('example', '暂无示例').split('JSON：')[0]}

当前系统日期：{current_date}"""
            
            return help_text
        else:
            # 提供通用帮助信息
            scenes_info = "\n".join([f"- {info['name']}: {info['description']}" 
                                   for key, info in self.scene_templates.items()])
            
            prompt = f"""作为数据查询智能助手，请根据用户的帮助请求提供使用指南。

系统支持的场景:
{scenes_info}

当前系统日期: {current_date}

历史对话:
{history_str}

用户问题: "{user_input}"

请提供一个友好、详细的使用指南，包括如何查询数据、支持的查询类型和示例。如果用户询问当前日期，请直接告知系统当前日期。: """
            
            # 修改为await调用
            return await send_message(prompt, None)
    
    # 将identify_help_scene方法修改为异步方法
    async def identify_help_scene(self, user_input):
        """
        识别用户希望了解哪个场景的帮助
        """
        scenes_info = "\n".join([f"{key}: {info['name']} - {info['description']}" 
                               for key, info in self.scene_templates.items()])
        
        prompt = f"""分析用户的帮助请求，判断与哪个场景最相关：

可用场景：
{scenes_info}

用户输入：{user_input}

如果用户的问题是关于系统使用帮助或特定场景的使用方法，请返回最相关的场景ID。
如果无法确定或不是帮助请求，请返回"none"。

请只返回场景ID或"none"："""
        
        # 修改为await调用
        result = await send_message(prompt, None)
        result = result.strip().lower()
        
        if result in self.scene_templates:
            return result
        return None
    
    def query_data(self, scene_name, params):
        """
        根据场景和参数查询数据
        
        Args:
            scene_name: 场景名称
            params: 查询参数
            
        Returns:
            查询结果
        """
        if scene_name not in self.scene_templates:
            return {"error": f"未找到场景: {scene_name}", "status": "error"}
        
        scene_config = self.scene_templates[scene_name]
        return query_data_by_scene(scene_name, scene_config, params)
    
    # 修改现有的处理方法，集成API调用
    # 确保所有异步调用都使用await
    async def process_multi_question(self, user_input):
        """处理多轮问题，支持流式响应"""
        # 保存原始输入，用于历史记录
        original_input = user_input
        from utils.date_utils import get_current_date
        current_date = get_current_date()
        # 创建上下文信息，包含当前日期
        context = {
            "history": self.get_history_str(),
            "current_date": current_date
        }
        
        # 第一步：对简短输入进行意图补全
        if len(user_input) < 10 and self.conversation_history:
            user_input = await self.complete_user_intent(user_input)
            print(f"意图补全: {original_input} -> {user_input}")
        
        # 第二步：识别意图类型
        # 确保这里使用await
        intent_type = await self.identify_intent_type(user_input)
        
        print(f"识别到的意图类型: {intent_type}")
        
        # 第三步：根据意图类型分发处理
        if intent_type == "system":
            response = await self.handle_system_intent(user_input)
            self.update_conversation_history(original_input, response)
            yield response
            return
        elif intent_type == "help":
            response = await self.handle_help_intent(user_input)
            self.update_conversation_history(original_input, response)
            yield response
            return
        elif intent_type == "chat":
            # 修改这里，添加 await 关键字
            response = await self.handle_chat_intent(user_input)
            if response:
                self.update_conversation_history(original_input, response)
                yield response
                return
            else:
                chat_prompt = f"""
        作为智能助手，请对用户的问题给出简洁、准确的回答:
    
        用户问题: {user_input}
    
        请直接回答问题，不需要解释或引导: """
                # 修改这里，添加 await 关键字
                response = await send_message(chat_prompt, None)
                self.update_conversation_history(original_input, response)
                yield response
                return
        #todo: 优化意图和场景识别，此外在确定intent_type 时，是否就将场景也确定下来？
        # 第四步：处理查询意图
        if self.current_purpose and await self.is_related_to_last_intent(user_input):
            print("相关")
        else:
            print("不相关")
            clarification = await self.get_clarification_question(user_input)
            if clarification:
                self.update_conversation_history(original_input, clarification)
                yield clarification
                return
            
            # 修改这里，添加await关键字
            await self.recognize_intent(user_input)
        logging.info('current_purpose: %s', self.current_purpose)
        #进行查询场景的处理
        if self.current_purpose in self.scene_templates:
            processor = self.get_processor_for_scene(self.current_purpose)
            # 修改这里：使用 try-except 处理异常，并通过 yield 返回结果
            try:
                # 使用异步流式处理
                full_response = ""
                async for chunk in processor.process(user_input, context):
                    if chunk:
                        full_response += chunk
                        yield chunk
                # 更新对话历史
                self.update_conversation_history(original_input, full_response)
                return
            except Exception as e:
                import traceback
                error_msg = f"处理请求时出错: {str(e)}\n{traceback.format_exc()}"
                print(error_msg)
                error_response = f"处理您的请求时出现了错误: {str(e)}"
                self.update_conversation_history(original_input, error_response)
                yield error_response
                return
        
        # 处理未命中场景的情况
        guide_keywords = ["怎么查", "如何查询", "怎样使用", "如何使用", "查询方法", "使用说明", "帮助"]
        if any(keyword in user_input for keyword in guide_keywords):
            guide_response = """您好！您可以通过以下方式查询数据：
    1. 直接询问具体业务数据，例如："北京分行昨天的存款余额"
    2. 指定分行名称、日期和查询类型，例如："查询上海分行2024年3月的贷款新增数据"
    3. 组合多个查询条件，例如："对比北京和南京分行上季度的对公存款余额"
    需要其他帮助请随时告诉我！"""
            self.update_conversation_history(original_input, guide_response)
            yield guide_response
            return
            
        response = '未命中场景，您可以尝试询问特定分行的数据，例如："北京分行昨天的存款余额是多少"'
        self.update_conversation_history(original_input, response)
        yield response

    def load_mappings(self):
        with open('config/mapping_config.json', 'r', encoding='utf-8') as f:
            self.mappings = json.load(f)
        # 可添加定期刷新或监听文件变化逻辑
    
    # 处理simple-chat 处理逻辑
    # 修改 handle_basic_chat 为异步方法
    async def handle_basic_chat(self, user_input):
        """
        处理基础对话，如问候、身份询问等
        :param user_input: 用户输入
        :return: 回复文本或None(如果不是基础对话)
        """
        basic_patterns = {
            "你是谁": "我是数据查询智能助手，可以帮您查询银行业务数据和回答相关问题。",
            "你叫什么": "我是数据查询智能助手，很高兴为您服务。",
            "你好": "您好！我是数据查询智能助手，有什么可以帮助您的吗？",
            "你能做什么": "我可以帮您查询银行业务数据、回答金融相关问题，以及提供其他信息服务。请告诉我您需要什么帮助。"
        }
        
        # 简单模式匹配 - 这些模式通常是明确的闲聊
        for pattern, response in basic_patterns.items():
            if pattern in user_input:
                return response
        
        # 获取历史对话上下文
        history_context = self.get_history_str(max_turns=3)  # 获取最近3轮对话
        
        # 判断是否有活跃的业务场景
        has_active_business = False
        if self.current_purpose and len(self.conversation_history) > 0:
            # 检查最近的对话是否与业务相关
            business_keywords = ["查询", "余额", "转账", "存款", "贷款", "利率", "账户", "分行","支行"]
            last_system_msg = self.conversation_history[-1].get('system', '')
            if any(keyword in last_system_msg for keyword in business_keywords):
                has_active_business = True
        
        # 如果有活跃业务场景且用户输入简短，可能是业务对话的继续而非闲聊
        if has_active_business and len(user_input) < 10:
            # 简短回复在业务场景中通常不是闲聊
            return None
        
        # 结合历史对话判断是否为闲聊
        prompt = f"""
根据历史对话和当前用户输入，判断用户当前输入是否为闲聊:

历史对话:
{history_context}

当前用户输入: {user_input}

请考虑以下因素:
1. 当前输入是否是对之前业务问题的回答或补充
2. 当前输入是否引入了新的业务查询意图
3. 当前输入是否仅为社交性质的对话(如问候、感谢等)

综合判断当前输入是否为闲聊，请只回答true或false: """
        
        # 只有当输入不太长时才进行判断，避免性能浪费
        if len(user_input) < 20:
            # 添加 await 关键字
            result = await send_message(prompt, None)
            if result is None:
                print("警告: 闲聊判断API调用失败")
                # 保守处理，当API调用失败时假设不是闲聊
                return None
            
            print("闲聊判断结果:", result)  # 添加调试输出
            
            # 使用更精确的判断逻辑
            result_lower = result.lower().strip()
            is_chat = "true" in result_lower
            
            print(f"闲聊判断结果解析: {'是闲聊' if is_chat else '非闲聊'}")
                
            if is_chat:
                print("判断为闲聊，生成回复")
                # 生成合适的闲聊回复，同时考
                chat_prompt = f"""
作为智能助手，请根据历史对话上下文，对用户的闲聊输入给出友好、专业、连贯的回复:

历史对话:
{history_context}

用户输入: {user_input}

请给出自然、友好的回复，保持对话的连贯性: """
                # 修改为await调用
                return await send_message(chat_prompt, None)
            else:
                # 明确不是闲聊，返回None
                print("判断为非闲聊，返回None")
                return None
        
        return None  # 不是基础对话，返回None

    # 修改 complete_user_intent 方法
    async def complete_user_intent(self, user_input):
        """补全用户意图"""
        # 如果用户输入已经足够详细，直接返回  # 对于每一个都进行补全，防止特殊情况
        # if len(user_input) > 20:
        #     return user_input
            
        # 获取历史对话
        history_str = self.get_history_str(max_turns=3)
        
        # 获取当前场景信息（如果有）
        current_scene_info = ""
        if self.current_purpose and self.current_purpose in self.scene_templates:
            scene = self.scene_templates[self.current_purpose]
            current_scene_info = f"""
    当前对话场景: {scene['name']}
    场景描述: {scene['description']}
    """
        
        # 使用大模型进行意图补全，结合历史对话和当前场景
        prompt = f"""根据历史对话、当前场景和简短输入，补全用户的完整意图，如果当前用户输入和历史的对话没有关联，请以当前对话判断意图。:
    
    历史对话:
    {history_str}
    
    {current_scene_info}
    当前用户输入: {user_input}
    
    请分析以下内容:
    1. 历史对话中的关键信息（如查询对象、时间、数据类型等）
    2. 当前对话场景的特点和要求（如果有）
    3. 当前输入的语义和可能的指代对象
    
    根据以上分析，推断用户的完整意图。例如:
    - 如果历史对话是查询"北京分行今天的贷款余额"，而当前输入是"明天呢"，则完整意图应为"查询北京分行明天的贷款余额"
    - 如果当前场景是"分行存款查询"，而用户输入"上海呢"，则完整意图应为"查询上海分行的存款情况"
    
    请直接输出补全后的完整意图（不要包含解释）: """
        
        completed_intent =  await send_message(prompt, None)
        print(f"原始输入: {user_input}")
        print(f"补全意图: {completed_intent}")
        
        # 检查补全结果是否合理
        if len(completed_intent) < len(user_input) or completed_intent == user_input:
            # 如果补全结果不合理，尝试更简单的补全方式
            simple_prompt = f"""请将用户的简短输入"{user_input}"结合上下文补充为完整的查询句子。
    历史对话:
    {history_str}
    
    请直接给出补全后的完整句子: """
            # 添加 await 关键字
            completed_intent = await send_message(simple_prompt, None)
        
        return completed_intent

    async def handle_chat_intent(self, user_input):
        """
        处理闲聊类问题
        """
        # 获取历史对话
        history_str = self.get_history_str()
        
        prompt = f"""作为数据查询智能助手，请回应用户的闲聊问题。
    
    历史对话:
    {history_str}
    
    用户问题: "{user_input}"
    
    请提供友好、简洁的回答，但也要提醒用户我主要擅长数据查询相关问题: """
        
        response = await send_message(prompt, None)
        return response



