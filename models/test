semaphore = asyncio.Semaphore(5)  # 限制并发数为5

# send_message异步实现
async def send_message(message, user_input):
    """
    异步请求chatGPT函数
    """
    print('--------------------------------------------------------------------')
    if config.DEBUG:
        print('prompt输入:', message)
    elif user_input:
        print('用户输入:', user_input)
    print('----------333--------------')
    
    headers = {
        "Authorization": f"Bearer {config.API_KEY}",
        "Content-Type": "application/json",
    }

    data = {
        "model": config.MODEL,
        "messages": [
            {"role": "system", "content": config.SYSTEM_PROMPT},
            {"role": "user", "content": f"{message}"}
        ]
    }
    async with semaphore:
        print(f'send_message started')
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(config.GPT_URL, headers=headers, json=data, ssl=False) as response:
                    if response.status == 200:
                        result = await response.json()
                        answer = result["choices"][0]["message"]['content']
                        print("000000000")
                        #print(answer)
                        print('LLM输出:', answer)
                        print('-----------------------44444------------------------------')
                        print(f'send_message finished')
                        return answer
                    else:
                        error_text = await response.text()
                        print(f"Error: {response.status_code}, {error_text}")
                        return None
        except Exception as e:
            print(f"Request error: {e}")
            return None