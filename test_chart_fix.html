<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ͼ���޸�����</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            min-height: 400px;
        }
        
        .echarts-chart {
            width: 100%;
            height: 400px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ͼ���޸�����</h1>
        
        <div class="test-section">
            <h3>����1: ģ���˷��ص�JSON����</h3>
            <p>�������ģ����������ʵ��JSON����</p>
            <div class="chart-container" id="mockContainer">
                <!-- ��������ģ���JSON���� -->
            </div>
            <button onclick="testMockConfig()">����ģ������</button>
            
            <h4>ģ���JSON����:</h4>
            <div class="json-display" id="mockJson"></div>
        </div>
        
        <div class="test-section">
            <h3>����2: �Ż��������</h3>
            <p>�������ʹ���Ż�������ø�ʽ</p>
            <div class="chart-container" id="optimizedContainer">
                <!-- ���������Ż������� -->
            </div>
            <button onclick="testOptimizedConfig()">�����Ż�����</button>
            
            <h4>�Ż���JSON����:</h4>
            <div class="json-display" id="optimizedJson"></div>
        </div>
        
        <div class="test-section">
            <h3>������־</h3>
            <div id="debugLog" class="log"></div>
            <button onclick="clearLog()">�����־</button>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        // ģ�������������ã��ӽ�ͼ����ȡ��
        function testMockConfig() {
            log('��ʼ����ģ������...');
            
            const mockConfig = {
                "title": {
                    "text": "����������Թ�������",
                    "subtext": "��������ʾ���Ա�",
                    "left": "center"
                },
                "tooltip": {
                    "trigger": "axis",
                    "axisPointer": {
                        "type": "shadow"
                    }
                },
                "legend": {
                    "data": ["���"],
                    "right": "10%"
                },
                "xAxis": {
                    "type": "category",
                    "name": "��������",
                    "data": ["belgOrgNm"]
                },
                "yAxis": {
                    "type": "value",
                    "name": "��� (��λ: Ԫ)"
                },
                "series": [{
                    "name": "���",
                    "type": "bar",
                    "data": ["���"],
                    "label": {
                        "show": true,
                        "position": "top",
                        "formatter": "{c} Ԫ"
                    },
                    "itemStyle": {
                        "color": "#5793f3"
                    }
                }]
            };
            
            document.getElementById('mockJson').textContent = JSON.stringify(mockConfig, null, 2);
            
            const container = document.getElementById('mockContainer');
            container.innerHTML = JSON.stringify(mockConfig);
            
            setTimeout(() => {
                processChartContainer(container, 'mock');
            }, 100);
        }
        
        // �Ż��������
        function testOptimizedConfig() {
            log('��ʼ�����Ż�����...');
            
            const optimizedConfig = {
                "title": {
                    "text": "���������Ա�",
                    "left": "center",
                    "textStyle": {
                        "fontSize": 16
                    }
                },
                "tooltip": {
                    "trigger": "axis",
                    "axisPointer": {
                        "type": "shadow"
                    },
                    "formatter": "{b}: {c}"
                },
                "grid": {
                    "left": "10%",
                    "right": "10%",
                    "bottom": "15%",
                    "top": "15%",
                    "containLabel": true
                },
                "xAxis": {
                    "type": "category",
                    "data": ["��������", "�Ϻ�����", "�Ͼ�����", "�Ϸʷ���", "���ݷ���"],
                    "axisLabel": {
                        "rotate": 0,
                        "interval": 0,
                        "fontSize": 12
                    }
                },
                "yAxis": {
                    "type": "value",
                    "name": "���(��Ԫ)",
                    "nameTextStyle": {
                        "fontSize": 12
                    }
                },
                "series": [{
                    "name": "���(��Ԫ)",
                    "type": "bar",
                    "data": [7731.07, 9892.58, 5368.80, 2693.49, 1214.51],
                    "itemStyle": {
                        "color": "#5470c6",
                        "borderRadius": [4, 4, 0, 0]
                    },
                    "label": {
                        "show": true,
                        "position": "top",
                        "fontSize": 11,
                        "formatter": "{c}"
                    },
                    "barWidth": "60%"
                }]
            };
            
            document.getElementById('optimizedJson').textContent = JSON.stringify(optimizedConfig, null, 2);
            
            const container = document.getElementById('optimizedContainer');
            container.innerHTML = JSON.stringify(optimizedConfig);
            
            setTimeout(() => {
                processChartContainer(container, 'optimized');
            }, 100);
        }
        
        // ����ͼ�������ĺ��������Ʋ��Ľ�����ҳ�棩
        function processChartContainer(container, testType) {
            let content = container.innerHTML.trim();
            log(`[${testType}] ����ͼ��������ԭʼ���ݳ���: ${content.length}`);
            
            // ����HTML��ǩ��ֻ����JSON����
            content = content.replace(/<[^>]*>/g, '').trim();
            log(`[${testType}] ��������ݳ���: ${content.length}`);
            
            // ����Ƿ����ECharts����
            if (content.startsWith('{') && (content.includes('title') || content.includes('series') || content.includes('xAxis'))) {
                try {
                    // �����޸����ܵ�JSON��ʽ����
                    let jsonContent = content;
                    
                    // �޸�������JSON��ʽ����
                    jsonContent = jsonContent.replace(/'/g, '"'); // �������滻Ϊ˫����
                    jsonContent = jsonContent.replace(/,\s*}/g, '}'); // �Ƴ�β�涺��
                    jsonContent = jsonContent.replace(/,\s*]/g, ']'); // �Ƴ�����β�涺��
                    
                    log(`[${testType}] ���Խ���JSON...`);
                    
                    // ����ECharts����
                    const chartConfig = JSON.parse(jsonContent);
                    log(`[${testType}] ? JSON�����ɹ�`);
                    
                    // ����ͼ������
                    const chartDiv = document.createElement('div');
                    chartDiv.className = 'echarts-chart';
                    chartDiv.id = `chart_${testType}_${Date.now()}`;
                    chartDiv.style.width = '100%';
                    chartDiv.style.height = '400px';
                    chartDiv.style.border = '1px solid #ddd';
                    chartDiv.style.borderRadius = '4px';
                    
                    // ������������ͼ��div
                    container.innerHTML = '';
                    container.appendChild(chartDiv);
                    
                    // �ӳٳ�ʼ��ȷ��DOM����Ⱦ
                    setTimeout(() => {
                        try {
                            // ���ECharts�Ƿ����
                            if (typeof echarts === 'undefined') {
                                throw new Error('ECharts��δ����');
                            }
                            
                            // ��ʼ��EChartsʵ��
                            const chart = echarts.init(chartDiv);
                            
                            // ��ȡ����ѡ��
                            const option = chartConfig.option || chartConfig;
                            log(`[${testType}] ��ʼ����EChartsѡ��...`);
                            
                            // ����ͼ��ѡ��
                            chart.setOption(option);
                            
                            log(`[${testType}] ? EChartsͼ���ʼ���ɹ�`);
                            
                        } catch (chartError) {
                            log(`[${testType}] ? ECharts��ʼ��ʧ��: ${chartError.message}`);
                            container.innerHTML = `<div style="color: red; padding: 20px; border: 1px solid red; border-radius: 4px;">
                                <strong>ͼ����Ⱦʧ��</strong><br>
                                ������Ϣ: ${chartError.message}
                            </div>`;
                        }
                    }, 200);
                    
                } catch (error) {
                    log(`[${testType}] ? JSON����ʧ��: ${error.message}`);
                    container.innerHTML = `<div style="color: orange; padding: 20px; border: 1px solid orange; border-radius: 4px;">
                        <strong>���ý���ʧ��</strong><br>
                        ������Ϣ: ${error.message}
                    </div>`;
                }
            } else {
                log(`[${testType}] ? ������Ч��ECharts����`);
            }
        }
        
        // ҳ�������ɺ�ĳ�ʼ��
        document.addEventListener('DOMContentLoaded', function() {
            log('ҳ�������ɣ���ʼ����...');
            log(`ECharts�汾: ${echarts.version || 'δ֪'}`);
            
            // �Զ����в���
            setTimeout(() => {
                testOptimizedConfig();
            }, 1000);
        });
    </script>
</body>
</html>
