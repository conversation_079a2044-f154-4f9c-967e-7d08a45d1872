# encoding=utf-8
import glob
import json
import re
import requests
import config
import aiohttp
import asyncio


def filename_to_classname(filename):
    """
    Convert a snake_case filename to a CamelCase class name.

    Args:
    filename (str): The filename in snake_case, without the .py extension.

    Returns:
    str: The converted CamelCase class name.
    """
    parts = filename.split('_')
    class_name = ''.join(part.capitalize() for part in parts)
    return class_name


def load_scene_templates(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return json.load(file)


def load_all_scene_configs():
    # 用于存储所有场景配置的字典
    all_scene_configs = {}
    
    # 添加调试信息，打印当前工作目录
    import os
    print(f"当前工作目录: {os.getcwd()}")
    
    # 搜索目录下的所有json文件
    json_files = glob.glob("scene_config/**/*.json", recursive=True)
    print(f"找到的JSON文件: {json_files}")
    
    if not json_files:
        # 尝试使用绝对路径
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        json_files = glob.glob(os.path.join(base_dir, "scene_config/**/*.json"), recursive=True)
        print(f"使用绝对路径找到的JSON文件: {json_files}")
    
    for file_path in json_files:
        try:
            print(f"正在加载文件: {file_path}")
            current_config = load_scene_templates(file_path)
            print(f"成功加载配置，包含场景: {list(current_config.keys())}")
            
            for key, value in current_config.items():
                # todo 可以有加载优先级
                # 只有当键不存在时，才添加到all_scene_configs中
                if key not in all_scene_configs:
                    all_scene_configs[key] = value
                    print(f"添加场景: {key}")
        except Exception as e:
            print(f"加载文件 {file_path} 时出错: {e}")
    
    print(f"最终加载的场景总数: {len(all_scene_configs)}")
    print(f"场景列表: {list(all_scene_configs.keys())}")
    
    return all_scene_configs


# send_message异步实现
async def send_message(message, user_input):
    """
    异步请求chatGPT函数
    """
    print('--------------------------------------------------------------------')
    if config.DEBUG:
        print('prompt输入:', message)
    elif user_input:
        print('用户输入:', user_input)
    print('----------333--------------')
    
    headers = {
        "Authorization": f"Bearer {config.API_KEY}",
        "Content-Type": "application/json",
    }

    data = {
        "model": config.MODEL,
        "messages": [
            {"role": "system", "content": config.SYSTEM_PROMPT},
            {"role": "user", "content": f"{message}"}
        ]
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(config.GPT_URL, headers=headers, json=data, ssl=False) as response:
                if response.status == 200:
                    result = await response.json()
                    answer = result["choices"][0]["message"]['content']
                    print("000000000")
                    #print(answer)
                    print('LLM输出:', answer)
                    print('-----------------------44444------------------------------')
                    return answer
                else:
                    error_text = await response.text()
                    print(f"Error: {response.status_code}, {error_text}")
                    return None
    except Exception as e:
        print(f"Request error: {e}")
        return None

# 为了兼容现有代码，可以提供一个同步版本的包装函数
def send_message_sync(message, user_input):
    """同步版本的send_message，内部调用异步版本"""
    return asyncio.run(send_message(message, user_input))


def is_slot_fully_filled(json_data):
    """
    检查槽位是否完整填充
    """
    # 遍历JSON数据中的每个元素
    for item in json_data:
        # 检查value字段是否为空字符串
        if item.get('value') == '':
            return False  # 如果发现空字符串，返回False
    return True  # 如果所有value字段都非空，返回True


def get_raw_slot(parameters):
    # 创建新的JSON对象
    output_data = []
    for item in parameters:
        new_item = {"name": item["name"], "desc": item["desc"], "type": item["type"], "value": ""}
        output_data.append(new_item)
    return output_data


def get_dynamic_example(scene_config):
    # 创建新的JSON对象
    if 'example' in scene_config:
        return scene_config['example']
    else:
        return '答：{"name":"xx","value":"xx"}'


def get_slot_update_json(slot):
    # 创建新的JSON对象
    output_data = []
    for item in slot:
        new_item = {"name": item["name"], "desc": item["desc"], "value": item["value"]}
        output_data.append(new_item)
    return output_data


def get_slot_query_user_json(slot):
    # 创建新的JSON对象
    output_data = []
    for item in slot:
        if not item["value"]:
            new_item = {"name": item["name"], "desc": item["desc"], "value":  item["value"]}
            output_data.append(new_item)
    return output_data


def update_slot(json_data, dict_target):
    """
    更新槽位slot参数
    """
    # 遍历JSON数据中的每个元素
    for item in json_data:
        # 检查value字段是否为空字符串
        if item['value'] != '':
            for target in dict_target:
                if target['name'] == item['name']:
                    target['value'] = item.get('value')
                    break


def format_name_value_for_logging(json_data):
    """
    抽取参数名称和value值
    """
    log_strings = []
    for item in json_data:
        name = item.get('name', 'Unknown name')  # 获取name，如果不存在则使用'Unknown name'
        value = item.get('value', 'N/A')  # 获取value，如果不存在则使用'N/A'
        log_string = f"name: {name}, Value: {value}"
        log_strings.append(log_string)
    return '\n'.join(log_strings)


def load_institution_mapping():
    """
    加载机构映射配置
    """
    try:
        import os
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        mapping_file = os.path.join(base_dir, "config", "mapping_config.json")
        
        with open(mapping_file, 'r', encoding='utf-8') as file:
            mapping_data = json.load(file)
            return mapping_data.get("institution_mapping", {})
    except Exception as e:
        print(f"加载机构映射配置出错: {e}")
        return {}

def get_belgOrgId(branch_name, institution_mapping=None):
    """
    根据分行名称获取对应的机构代码
    """
    if institution_mapping is None:
        institution_mapping = load_institution_mapping()
    
    # 反转映射关系，键为分行名称，值为代码
    reverse_mapping = {v: k for k, v in institution_mapping.items()}
    print("reverse_mapping:", reverse_mapping)
    print( reverse_mapping.get(branch_name, branch_name))
    # 返回对应的代码，如果找不到则返回原始名称
    return reverse_mapping.get(branch_name, branch_name)

def extract_json_from_string(input_string, scene_name=None):
    """
    从字符串中提取JSON结构
    
    Args:
        input_string: 输入的字符串
        scene_name: 场景名称，用于日志记录
        
    Returns:
        包含原始JSON对象的列表
    """
    print("首先尝试直接解析完整的JSON数组0")
    try:
        # 首先尝试直接解析完整的JSON数组
        if input_string.strip().startswith('[') and input_string.strip().endswith(']'):
            print("尝试直接解析完整的JSON数组1")
            try:
                # 清理输入字符串，移除可能的代码块标记
                json_str = input_string.strip()
                if '```json' in json_str:
                    json_str = json_str.split('```json')[1]
                if '```' in json_str:
                    json_str = json_str.split('```')[0]
                
                # 尝试解析JSON数组
                json_data = json.loads(json_str)
                if isinstance(json_data, list):
                    # 提取name和value字段
                    raw_jsons = []
                    for item in json_data:
                        if isinstance(item, dict) and "name" in item and "value" in item:
                            raw_jsons.append({
                                "name": item["name"],
                                "value": item["value"]
                            })
                    
                    print("成功解析完整JSON数组0")
                    
                    if raw_jsons:
                        print("成功解析完整JSON数组1")
                        return transform_parameters(raw_jsons, scene_name)
            except json.JSONDecodeError as e:
                print(f"完整JSON解析失败: {e}")
        
        # 如果直接解析失败，使用正则表达式匹配所有的name-value对
        pattern = r"'name':\s*'([^']+)',\s*'value':\s*'([^']+)'"
        matches = re.findall(pattern, input_string)
        
        # 将匹配到的键值对转换为JSON对象列表
        raw_jsons = []
        
        for name, value in matches:
            json_obj = {
                "name": name.strip(),
                "value": value.strip()
            }
            raw_jsons.append(json_obj)
            print(f"提取的参数对: name={name}, value={value.strip()}")
        
        if raw_jsons:
            print("成功提取的原始JSON对象:", raw_jsons)
            return transform_parameters(raw_jsons, scene_name)
            
        # 如果上面的方法失败，尝试标准JSON解析
        json_matches = re.findall(r'\{[^}]+\}', input_string)
        for match in json_matches:
            try:
                fixed_json = match.replace("'", '"')
                json_obj = json.loads(fixed_json)
                if isinstance(json_obj, dict) and "name" in json_obj and "value" in json_obj:
                    raw_jsons.append(json_obj)
            except json.JSONDecodeError:
                continue
        
        if raw_jsons:
            return transform_parameters(raw_jsons, scene_name)
        return []
    except Exception as e:
        print(f"Error occurred during JSON extraction: {e}")
        return []

#支持根据场景和表名进行参数转换
def transform_parameters(raw_params, scene_name):
    """
    转换参数，应用所有注册的参数处理器
    
    Args:
        raw_params: 原始参数列表
        scene_name: 场景名称
        
    Returns:
        转换后的参数列表
    """
    try:
        # 导入处理器注册表
        from utils.parameter_processors import get_parameter_processors
        
        # 打印原始参数，用于调试
        print(f"raw_params {raw_params}")
        
        # 获取场景配置
        scene_config = None
        try:
            all_scene_configs = load_all_scene_configs()
            scene_config = all_scene_configs.get(scene_name, {})
        except Exception as e:
            print(f"获取场景配置出错: {e}")
        
        # 获取表名
        table_name = scene_config.get("table_name", "") if scene_config else ""
        
        processor_registry = get_parameter_processors()
        transformed_params = []
        
        for param in raw_params:
            name = param.get("name")
            value = param.get("value", "")
            
            # 应用所有适用的处理器，传入表名
            for processor in processor_registry:
                try:
                    old_value = value  # 保存处理前的值
                    value = processor.process(name, value, scene_name, table_name)
                    if old_value != value:
                        print(f"处理器 {processor.__class__.__name__} 将参数 {name} 从 '{old_value}' 转换为 '{value}'")
                except Exception as e:
                    print(f"处理器 {processor.__class__.__name__} 处理参数 {name} 时出错: {e}")
            
            transformed_params.append({
                "name": name,
                "value": value
            })
        
        print(f"转换后的参数: {transformed_params}")
        return transformed_params
    except Exception as e:
        print(f"参数转换过程中出错: {e}")
        # 出错时返回原始参数
        return raw_params

