import json
import logging
import requests
import time
import os
import pandas as pd  # 添加pandas导入
from typing import Dict, Any, Optional, Union, List
from services.llm_service import send_llm_request
# 配置日志
logger = logging.getLogger(__name__)

# 加载API配置
def load_api_configs():
    """
    从配置文件加载API配置
    
    Returns:
        Dict: API配置字典
    """
    config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'api_configs.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载API配置文件失败: {str(e)}")
        return {}

# 加载API配置
API_CONFIGS = load_api_configs()

def call_api_by_scene(scene_name: str, params: Dict[str, Any] = None) -> Dict:
    """
    根据场景名称调用对应的API
    
    Args:
        scene_name: 场景名称，必须是API_CONFIGS中定义的场景之一
        params: 查询参数，用于更新body_template中的queryParams
        
    Returns:
        API响应结果
    """
    # 检查场景是否存在
    if scene_name not in API_CONFIGS:
        error_msg = f"未找到场景配置: {scene_name}"
        logger.error(error_msg)
        return {"error": error_msg, "status": "error"}
    print("API请求中5。。")
    # 获取场景配置
    scene_config = API_CONFIGS[scene_name]
    url = scene_config["url"]
    headers = scene_config["headers"]
    
    # 深拷贝body模板，避免修改原始配置
    body = json.loads(json.dumps(scene_config["body_template"]))
    
    # 更新查询参数
    if params:
        for key, value in params.items():
            if key in body["queryParams"]:
                body["queryParams"][key] = value
                
            # 特殊处理：确保dataDt和dt参数值一致
            if "dataDt" in body["queryParams"] and "dt" in body["queryParams"]:
                body["queryParams"]["dt"] = body["queryParams"]["dataDt"]
                logger.debug(f"已同步dt参数值为dataDt值: {body['queryParams']['dataDt']}")
    
    # 特殊处理：belgOrgNm参数转换逻辑
    if "belgOrgNm" in body["queryParams"]:
        belg_org_nm = body["queryParams"]["belgOrgNm"]
        if isinstance(belg_org_nm, str) and "分行" in belg_org_nm:
            # 将包含'分行'的belgOrgNm转换为'挖旗'
            converted_value = belg_org_nm.replace("分行", "挖旗")
            body["queryParams"]["belgOrgNm"] = converted_value +","+ belg_org_nm
            logger.info(f"belgOrgNm参数已转换: {belg_org_nm} -> {converted_value}")
    logger.info(f"调用API: {scene_name}, URL: {url}")
    logger.debug(f"请求头: {headers}")
    logger.debug(f"请求体: {body}")
    
    start_time = time.time()
    
    try:
        # 发送请求
        print("API请求中6。。")
        response = requests.post(url, json=body, headers=headers, verify=False)
        
        # 记录请求耗时
        elapsed_time = time.time() - start_time
        logger.info(f"API请求耗时: {elapsed_time:.2f}秒")
        
        # 检查响应状态
        if response.status_code == 200:
            try:
                result = response.json()
                logger.info(f"API调用成功: {scene_name}")
                return {"status": "success", "data": result}
            except json.JSONDecodeError:
                logger.warning(f"API响应不是有效的JSON: {response.text[:100]}...")
                return {"status": "success", "data": response.text}
        else:
            error_msg = f"API请求失败，状态码: {response.status_code}, 响应: {response.text[:100]}..."
            logger.error(error_msg)
            return {"error": error_msg, "status": "error"}
            
    except requests.exceptions.RequestException as e:
        error_msg = f"API请求异常: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg, "status": "error"}
    
    except Exception as e:
        error_msg = f"处理API请求时发生错误: {str(e)}"
        logger.exception(error_msg)
        return {"error": error_msg, "status": "error"}

# 添加数据转换映射配置
FIELD_MAPPINGS = {
    "bank_branch_query": {
        "dataDt": "日期",
        "fstLvlBrchOrgNm": "机构所属一级分行名称",
        "belgOrgNm": "机构名称",
        "CurCd": "币种",
        "statsObj": "统计方式",
        "projNm": "项目名称",
        "projLevel": "项目层级",
        "projSqnum": "项目序号",
        "indxVal": "利息/非息净收入",
        "indxAvg": "日均余额",
        "yAvgExecIntRateVal": "成本率/收益率",
        "indxBal": "余额",
        "indxBalCmtLastM": "余额上月环比",
        "indxBalSameLastY": "余额同比",
        "indxValCmtLastDWhoCntr": "利息/非息净收入_比上日贡献度",
        "indxValCmtLastMWhorCnt": "利息/非息净收入_比上月贡献度",
        "indxValCmtSameLastYWhoCntr": "利息/非息净收入_比上年贡献度"
    },
    "bank_customer_query": {
        "dataDt": "日期",
         "fstLvlBrchOrgNm": "一级分行机构名称",
        "belgOrgNm": "机构名称",
        "CurCd": "币种",
        "statsObj": "统计方式",
        "projNm": "项目名称",
        "custNm": "客户名称",
        "bigNm": "业务条线",
        "coreProdNm": "核心产品名称",
        # 添加更多字段映射...
    },
    "bank_org_id_query": {
        "belgOrgId": "机构ID",
        "belgOrgNm": "机构名称",
        "fstLvlBrchOrgId": "一级分行ID",
        "fstLvlBrchOrgNm": "一级分行名称",
        "dt": "日期",
        "orgLevel": "机构层级",
        "orgType": "机构类型"
    },
    "bank_kpi_query":{
        "dataDt": "数据日期",
    "bankNum": "银行号",
    "belgOrgId": "机构号",
    "belgOrgNm": "机构名称",
    "fstLvlBrchOrgId": "一级分行号",
    "fstLvlBrchOrgNm": "一级分行名称",
    "lvl1FstBrchOrgId": "分行一级机构号",
    "lvl1FstBrchOrgNm": "分行一级机构名称",
    "lvl1SecBrchOrgId": "分行二级机构号",
    "lvl1SecBrchOrgNm": "分行二级机构名称",
    "lvl1ThirBrchOrgId": "分行三级机构号",
    "lvl1ThirBrchOrgNm": "分行三级机构名称",
    "belgLevel": "机构级别",
    "bizBrchInd": "机构营业网点标识",
    "paGrpId": "考核分组",
    "paGrpBelgNum": "考核分组内成员数",
    "indxNum": "指标编号",
    "indxNm": "指标名称",
    "indxAlias": "指标别名",
    "curCd": "币种",
    "statsObj": "统计对象",
    "statsMethd": "统计方式",
    "adjustCd": "是否包含调整项",
    "indxTyp": "指标类型",
    "orderTyp": "排名方式",
    "indxResInd": "是否经营结果汇总指标",
    "indxYesInd": "规模批是否取前一天效益批结果指标",
    "indxVal": "指标值",
    "indxValLastD": "指标值_上日",
    "indxValLastM": "指标值_上月",
    "indxValLastY": "指标值_上年",
    "indxValCmtBeginY": "指标值_增量_上年",
    "indxValAvgCmtBeginY": "指标值_每日平均增量_上年",
    "indxValSameLastY": "指标值_上年同期",
    "indxValSameLastY": "指标值_增量_上年同期",
    "acctAmtLastD": "指标值_上日发生额",
    "acctAmtLastM": "指标值_上月发生额",
    "v1CmtPlan": "年度增量计划",
    "comprRate": "完成率",
    "planGap": "计划差额",
    "surAvgBalReqD": "剩余期间日平均余额要求",
    "timeRate": "序时进度",
    "comprTimeRate": "超序时进度",
    "ach1TimeRateInd": "是否达成序时进度",
    "ach1IndxNums": "达成序时进度指标数",
    "ach1IndxNumsRank": "达成序时进度指标数_组内排名",
    "ach1IndxList": "达成序时进度指标列表",
    "beh1TimeRateInd": "是否落后序时进度",
    "beh1IndxNums": "落后序时进度指标数",
    "beh1IndxNumsRank": "落后序时进度指标数_组内排名",
    "beh1IndxList": "落后序时进度指标列表",
    "seriBehindTimeRateInd": "是否严重落后序时进度",
    "seriBehindIndxNums": "严重落后序时进度指标数",
    "seriBehindIndxNumsRank": "严重落后序时进度指标数_组内排名",
    "seriBehindIndxList": "严重落后序时进度指标列表",
    "totWhrRank": "指标值_全行(有分组)内排名",
    "totBrnRank": "指标值_机构级别内排名",
    "totGrpRank": "指标值_组内排名",
    "totGrpAvg": "指标值_组内平均",
    "m1CmtWhrRank": "指标值_月增量_全行(有分组)内排名",
    "m1CmtBrnRank": "指标值_月增量_机构级别内排名",
    "m1CmtGrpRank": "指标值_月增量_组内排名",
    "m1CmtGrpAvg": "指标值_月增量_组内平均",
    "y1CmtWhrRank": "指标值_年增量_全行(有分组)内排名",
    "y1CmtBrnRank": "指标值_年增量_机构级别内排名",
    "y1CmtGrpRank": "指标值_年增量_组内排名",
    "y1CmtGrpAvg": "指标值_年增量_组内平均",
    "ySame1CmtWhrRank": "指标值_上年同期增量_全行(有分组)内排名",
    "ySame1CmtBrnRank": "指标值_上年同期增量_机构级别内排名",
    "ySame1CmtGrpRank": "指标值_上年同期增量_组内排名",
    "ySame1CmtGrpAvg": "指标值_上年同期增量_组内平均",
    "m1CmtRankChgLastM": "指标值_月增量_组内排名_比上月变化",
    "m1CmtRankChgLastY": "指标值_月增量_组内排名_比上年同期变化",
    "y1CmtRankChgLastM": "指标值_年增量_组内排名_比上月变化",
    "y1CmtRankChgLastY": "指标值_年增量_组内排名_比上年同期变化",
    "ySame1CmtRankChgLastM": "指标值_上年同期增量_组内排名_比上月变化",
    "ySame1CmtRankChgLastY": "指标值_上年同期增量_组内排名_比上年同期变化"


    }
}

def transform_api_data(scene_name: str, api_result: Dict, user_question: str = "") -> Dict:
    """
    转换API响应数据，将英文字段名映射为中文字段名，并使用大模型智能筛选相关字段
    
    Args:
        scene_name: 场景名称
        api_result: API响应结果
        user_question: 用户问题，用于智能筛选字段
        
    Returns:
        转换后的数据，包含DataFrame的markdown表示
    """
    if scene_name not in FIELD_MAPPINGS:
        logger.warning(f"未找到场景的字段映射配置: {scene_name}")
        return api_result
    
    # 处理嵌套的数据结构（保持原有逻辑）
    data_to_transform = None
    
    if "data" in api_result:
        if isinstance(api_result["data"], list):
            data_to_transform = api_result["data"]
        elif isinstance(api_result["data"], dict) and "data" in api_result["data"]:
            if isinstance(api_result["data"]["data"], list):
                data_to_transform = api_result["data"]["data"]
            else:
                logger.warning(f"嵌套的data字段不是列表: {api_result}")
                return api_result
        else:
            logger.warning(f"API响应数据格式不符合预期: {api_result}")
            return api_result
    else:
        logger.warning(f"API响应中没有data字段: {api_result}")
        return api_result
        
    # 获取字段映射
    field_mapping = FIELD_MAPPINGS[scene_name]
    
    # 转换数据
    transformed_data = []
    for item in data_to_transform:
        transformed_item = {}
        # 应用字段映射
        for eng_field, cn_field in field_mapping.items():
            if eng_field in item:
                transformed_item[cn_field] = item[eng_field]
        # 保留未映射的字段
        for key, value in item.items():
            if key not in field_mapping:
                transformed_item[key] = value
        transformed_data.append(transformed_item)
    
    # 返回转换后的结果
    result = api_result.copy()
    
    if isinstance(api_result["data"], dict) and "data" in api_result["data"]:
        result_data = api_result["data"].copy()
        result_data["data"] = transformed_data
        result["data"] = result_data
    else:
        result["data"] = transformed_data
        
    result["field_mapping"] = field_mapping
    
    # 创建DataFrame并进行智能字段筛选
    try:
        df = pd.DataFrame(transformed_data)
        
        if user_question and len(user_question.strip()) > 0:
            # 使用大模型进行智能字段筛选
            available_fields = df.columns.tolist()
            
            try:
                # 首先尝试使用LLM筛选
                selected_fields = llm_filter_fields(scene_name, user_question, available_fields)
                logger.info(f"使用LLM筛选字段: {len(selected_fields)}个")
            except Exception as e:
                # LLM失败时使用备用方案
                logger.warning(f"LLM筛选失败，使用备用方案: {e}")
                selected_fields = fallback_filter_fields(scene_name, user_question, available_fields)
                logger.info(f"使用备用方案筛选字段: {len(selected_fields)}个")
            
            # 筛选DataFrame
            df_filtered = df[selected_fields] if selected_fields else df
            
            # 记录筛选信息
            result["field_selection"] = {
                "method": "llm_intelligent",
                "user_question": user_question,
                "total_fields": len(available_fields),
                "selected_fields": len(selected_fields),
                "selected_field_names": selected_fields
            }
        else:
            # 没有用户问题时，使用基础字段
            base_fields = SCENE_BASE_FIELDS.get(scene_name, [])
            available_base_fields = [field for field in base_fields if field in df.columns]
            df_filtered = df[available_base_fields] if available_base_fields else df
            
            result["field_selection"] = {
                "method": "base_fields_only",
                "selected_fields": len(available_base_fields),
                "selected_field_names": available_base_fields
            }
        
        # **关键修复：返回筛选后的数据**
        filtered_data = df_filtered.to_dict('records')
        
        # 更新返回结果中的data字段为筛选后的数据
        if isinstance(api_result["data"], dict) and "data" in api_result["data"]:
            result_data = api_result["data"].copy()
            result_data["data"] = filtered_data
            result["data"] = result_data
        else:
            result["data"] = filtered_data
        
        # 生成HTML表示（使用筛选后的数据）
        result["markdown_table"] = df_filtered.to_html(
            index=False, 
            table_id="data-table", 
            classes="table table-striped table-bordered", 
            escape=False, 
            float_format='{:.2f}'.format,
            border=0
        )
        
        logger.info(f"成功创建数据的HTML表示，显示 {len(df_filtered.columns)} 个字段，{len(filtered_data)} 行数据")
        
    except Exception as e:
        logger.error(f"创建HTML表示时出错: {str(e)}")
        result["markdown_table"] = None
        
    return result

def query_bank_org_id(params: Dict[str, Any] = None) -> Dict:
    """
    银行机构ID查询接口
    
    Args:
        params: 查询参数，可包含belgOrgId, dt等
        
    Returns:
        查询结果，包含机构ID和对应的分行ID信息
    """
    return call_api_by_scene("bank_org_id_query", params)

def get_first_level_branch_id_from_api(belg_org_nm: str = None, belg_org_id: str = None, date: str = None) -> Dict:
    """
    通过API查询获取一级分行ID
    
    Args:
        belg_org_nm: 机构名称（新增参数）
        belg_org_id: 机构ID（保留兼容性）
        date: 查询日期，格式为YYYYMMDD，默认为当前日期
        
    Returns:
        包含一级分行ID信息的字典
    """
    if date is None:
        from datetime import datetime,timedelta
        date = (datetime.now()-timedelta(days=2)).strftime('%Y%m%d')
    print("date33333:",date,belg_org_nm)
    # 如果提供了机构名称，需要先转换为机构ID
    if belg_org_nm and not belg_org_id:
        from utils.helpers import get_belgOrgId
        belg_org_id = get_belgOrgId(belg_org_nm)
        print("belg_org_id3334:",belg_org_id)
        logger.info(f"根据机构名称 {belg_org_nm} 获取到机构ID: {belg_org_id}")
    
    if not belg_org_id:
        return {
            "status": "error",
            "message": "缺少机构ID或机构名称参数"
        }
    
    # 构建查询参数
    params = {
        "belgOrgId": int(belg_org_id) if belg_org_id.isdigit() else belg_org_id,
        "dt": int(date) if date.isdigit() else date
    }
    
    logger.info(f"查询机构ID {belg_org_id} 的一级分行ID信息")
    
    try:
        print("query_bank_org_id")
        # 调用API
        result = query_bank_org_id(params)
        
        if result.get("status") == "success" and "data" in result:
            # 处理API响应数据
            api_data = result["data"]
            
            # 根据实际API响应结构提取一级分行ID
            # 这里需要根据实际API响应格式进行调整
            if isinstance(api_data, dict) and "data" in api_data:
                data_list = api_data["data"]
                if data_list and len(data_list) > 0:
                    first_record = data_list[0]
                    
                    # 提取一级分行ID相关信息
                    fst_lvl_brch_org_id = first_record.get("fstLvlBrchOrgId")
                    fst_lvl_brch_org_nm = first_record.get("fstLvlBrchOrgNm")
                    
                    return {
                        "status": "success",
                        "belgOrgId": belg_org_id,
                        "fstLvlBrchOrgId": fst_lvl_brch_org_id,
                        "fstLvlBrchOrgNm": fst_lvl_brch_org_nm,
                        "date": date,
                        "raw_data": first_record
                    }
                else:
                    return {
                        "status": "error",
                        "message": f"未找到机构ID {belg_org_id} 的相关数据"
                    }
            else:
                return {
                    "status": "error",
                    "message": "API响应数据格式不符合预期",
                    "raw_response": api_data
                }
        else:
            return {
                "status": "error",
                "message": result.get("error", "API调用失败"),
                "raw_response": result
            }
            
    except Exception as e:
        logger.error(f"查询机构ID {belg_org_id} 的一级分行ID时出错: {str(e)}")
        return {
            "status": "error",
            "message": f"查询过程中发生异常: {str(e)}"
        }

# 更新SCENE_TO_API_KEY映射
SCENE_TO_API_KEY = {
    "银行总行、分行经营查询": "bank_branch_query",
    "对公客户各个项目经营指标查询": "bank_customer_query",
    "银行板块条线经营查询": "bank_segment_query",
    "银行机构ID查询": "bank_org_id_query",
    "银行总行、分行绩效考核指标查询": "bank_kpi_query",
    # 可以添加更多映射...
}

# 更新字段映射配置
FIELD_MAPPINGS = {
    "bank_branch_query": {
        "dataDt": "日期",
        "fstLvlBrchOrgNm": "机构所属一级分行名称",
        "belgOrgNm": "机构名称",
        "CurCd": "币种",
        "statsObj": "统计方式",
        "projNm": "项目名称",
        "projLevel": "项目层级",
        "projSqnum": "项目序号",
        "indxVal": "利息/非息净收入",
        "indxAvg": "日均余额",
        "yAvgExecIntRateVal": "成本率/收益率",
        "indxBal": "余额",
        "indxBalCmtLastM": "余额上月环比",
        "indxBalSameLastY": "余额同比",
        "indxValCmtLastDWhoCntr": "利息/非息净收入_比上日贡献度",
        "indxValCmtLastMWhorCnt": "利息/非息净收入_比上月贡献度",
        "indxValCmtSameLastYWhoCntr": "利息/非息净收入_比上年贡献度"
    },
    "bank_customer_query": {
        "dataDt": "日期",
         "fstLvlBrchOrgNm": "一级分行机构名称",
        "belgOrgNm": "机构名称",
        "CurCd": "币种",
        "statsObj": "统计方式",
        "projNm": "项目名称",
        "custNm": "客户名称",
        "bigNm": "业务条线",
        "coreProdNm": "核心产品名称",
        # 添加更多字段映射...
    },
    "bank_org_id_query": {
        "belgOrgId": "机构ID",
        "belgOrgNm": "机构名称",
        "fstLvlBrchOrgId": "一级分行ID",
        "fstLvlBrchOrgNm": "一级分行名称",
        "dt": "日期",
        "orgLevel": "机构层级",
        "orgType": "机构类型"
    },
    "bank_kpi_query":{
        "dataDt": "数据日期",
    "bankNum": "银行号",
    "belgOrgId": "机构号",
    "belgOrgNm": "机构名称",
    "fstLvlBrchOrgId": "一级分行号",
    "fstLvlBrchOrgNm": "一级分行名称",
    "lvl1FstBrchOrgId": "分行一级机构号",
    "lvl1FstBrchOrgNm": "分行一级机构名称",
    "lvl1SecBrchOrgId": "分行二级机构号",
    "lvl1SecBrchOrgNm": "分行二级机构名称",
    "lvl1ThirBrchOrgId": "分行三级机构号",
    "lvl1ThirBrchOrgNm": "分行三级机构名称",
    "belgLevel": "机构级别",
    "bizBrchInd": "机构营业网点标识",
    "paGrpId": "考核分组",
    "paGrpBelgNum": "考核分组内成员数",
    "indxNum": "指标编号",
    "indxNm": "指标名称",
    "indxAlias": "指标别名",
    "curCd": "币种",
    "statsObj": "统计对象",
    "statsMethd": "统计方式",
    "adjustCd": "是否包含调整项",
    "indxTyp": "指标类型",
    "orderTyp": "排名方式",
    "indxResInd": "是否经营结果汇总指标",
    "indxYesInd": "规模批是否取前一天效益批结果指标",
    "indxVal": "指标值",
    "indxValLastD": "指标值_上日",
    "indxValLastM": "指标值_上月",
    "indxValLastY": "指标值_上年",
    "indxValCmtBeginY": "指标值_增量_上年",
    "indxValAvgCmtBeginY": "指标值_每日平均增量_上年",
    "indxValSameLastY": "指标值_上年同期",
    "indxValSameLastY": "指标值_增量_上年同期",
    "acctAmtLastD": "指标值_上日发生额",
    "acctAmtLastM": "指标值_上月发生额",
    "v1CmtPlan": "年度增量计划",
    "comprRate": "完成率",
    "planGap": "计划差额",
    "surAvgBalReqD": "剩余期间日平均余额要求",
    "timeRate": "序时进度",
    "comprTimeRate": "超序时进度",
    "ach1TimeRateInd": "是否达成序时进度",
    "ach1IndxNums": "达成序时进度指标数",
    "ach1IndxNumsRank": "达成序时进度指标数_组内排名",
    "ach1IndxList": "达成序时进度指标列表",
    "beh1TimeRateInd": "是否落后序时进度",
    "beh1IndxNums": "落后序时进度指标数",
    "beh1IndxNumsRank": "落后序时进度指标数_组内排名",
    "beh1IndxList": "落后序时进度指标列表",
    "seriBehindTimeRateInd": "是否严重落后序时进度",
    "seriBehindIndxNums": "严重落后序时进度指标数",
    "seriBehindIndxNumsRank": "严重落后序时进度指标数_组内排名",
    "seriBehindIndxList": "严重落后序时进度指标列表",
    "totWhrRank": "指标值_全行(有分组)内排名",
    "totBrnRank": "指标值_机构级别内排名",
    "totGrpRank": "指标值_组内排名",
    "totGrpAvg": "指标值_组内平均",
    "m1CmtWhrRank": "指标值_月增量_全行(有分组)内排名",
    "m1CmtBrnRank": "指标值_月增量_机构级别内排名",
    "m1CmtGrpRank": "指标值_月增量_组内排名",
    "m1CmtGrpAvg": "指标值_月增量_组内平均",
    "y1CmtWhrRank": "指标值_年增量_全行(有分组)内排名",
    "y1CmtBrnRank": "指标值_年增量_机构级别内排名",
    "y1CmtGrpRank": "指标值_年增量_组内排名",
    "y1CmtGrpAvg": "指标值_年增量_组内平均",
    "ySame1CmtWhrRank": "指标值_上年同期增量_全行(有分组)内排名",
    "ySame1CmtBrnRank": "指标值_上年同期增量_机构级别内排名",
    "ySame1CmtGrpRank": "指标值_上年同期增量_组内排名",
    "ySame1CmtGrpAvg": "指标值_上年同期增量_组内平均",
    "m1CmtRankChgLastM": "指标值_月增量_组内排名_比上月变化",
    "m1CmtRankChgLastY": "指标值_月增量_组内排名_比上年同期变化",
    "y1CmtRankChgLastM": "指标值_年增量_组内排名_比上月变化",
    "y1CmtRankChgLastY": "指标值_年增量_组内排名_比上年同期变化",
    "ySame1CmtRankChgLastM": "指标值_上年同期增量_组内排名_比上月变化",
    "ySame1CmtRankChgLastY": "指标值_上年同期增量_组内排名_比上年同期变化"


    }
}


# 在FIELD_MAPPINGS后面添加基础字段配置
SCENE_BASE_FIELDS = {
    "bank_branch_query": ["日期", "机构名称", "项目名称"],
    "bank_customer_query": ["日期", "机构名称", "客户名称"],
    "bank_org_id_query": ["机构ID", "机构名称", "日期"],
    "bank_kpi_query": ["数据日期", "机构名称", "指标名称"]
}


import json
from services.llm_service import send_llm_request  # 导入实际可用的函数

def llm_filter_fields(scene_name: str, user_question: str, available_fields: list) -> list:
    """
    使用大模型根据用户问题智能筛选相关字段
    
    Args:
        scene_name: 场景名称
        user_question: 用户问题
        available_fields: 可用的所有字段列表（中文字段名）
        
    Returns:
        筛选后的字段列表
    """
    # 获取基础字段
    base_fields = SCENE_BASE_FIELDS.get(scene_name, [])
    system_prompt = """
你是一个数据分析专家，需要根据用户问题从可用字段中筛选出最相关的字段。
请返回一个字段列表，只包含与用户问题直接相关的字段名。

返回格式： ["field1", "field2", ...]
"""
    # 构建提示词
    user_prompt = f"""
你是一个数据分析专家，需要根据用户问题从可用字段中筛选出最相关的字段。

场景：{scene_name}
用户问题：{user_question}

基础字段（必须包含）：{base_fields}

可用字段列表：
{json.dumps(available_fields, ensure_ascii=False, indent=2)}

请根据用户问题的意图，从可用字段中筛选出最相关的字段。筛选原则：
1. 必须包含所有基础字段
2. 根据用户问题的关键词和意图，选择最相关的业务字段
3. 避免选择过多无关字段，保持结果简洁有用
4. 如果用户问题涉及对比、排名、趋势等，要包含相关的对比字段
5. 如果用户问题涉及特定指标，要包含该指标的相关字段

请直接返回一个数组，包含筛选后的字段名称，不要包含任何其他文字说明。
示例格式：
["日期", "机构名称", "余额", "余额同比"]
"""
    
    try:
        # 调用LLM服务
        response = send_llm_request(system_prompt, user_prompt)
        
        # 解析LLM响应
        try:
            selected_fields = json.loads(response.strip())
            if isinstance(selected_fields, list):
                # 确保基础字段都包含在内
                final_fields = list(set(base_fields + selected_fields))
                # 只保留在available_fields中的字段
                final_fields = [field for field in final_fields if field in available_fields]
                
                logger.info(f"LLM筛选字段成功: {len(final_fields)}个字段")
                logger.debug(f"筛选结果: {final_fields}")
                return final_fields
            else:
                logger.warning("LLM返回的不是有效的字段列表")
                return base_fields
        except json.JSONDecodeError as e:
            logger.error(f"解析LLM响应失败: {e}, 响应内容: {response}")
            return base_fields
            
    except Exception as e:
        logger.error(f"LLM字段筛选失败: {e}")
        # 降级到基础字段
        return base_fields

def fallback_filter_fields(scene_name: str, user_question: str, available_fields: list) -> list:
    """
    备用字段筛选方案（基于关键词匹配）
    当LLM服务不可用时使用
    """
    base_fields = SCENE_BASE_FIELDS.get(scene_name, [])
    
    # 关键词到字段的映射
    keyword_mappings = {
        "余额": ["余额", "日均余额", "余额上月环比", "余额同比"],
        "收入": ["利息/非息净收入", "成本率/收益率"],
        "贡献": ["利息/非息净收入_比上日贡献度", "利息/非息净收入_比上月贡献度", "利息/非息净收入_比上年贡献度"],
        "排名": ["指标值_全行(有分组)内排名", "指标值_组内排名"],
        "指标": ["指标值", "指标值_上日", "指标值_上月", "指标值_上年"],
        "完成": ["年度增量计划", "完成率", "序时进度"],
        "客户": ["客户名称", "业务条线", "核心产品名称"]
    }
    
    selected_fields = set(base_fields)
    user_question_lower = user_question.lower()
    
    for keyword, fields in keyword_mappings.items():
        if keyword in user_question_lower:
            for field in fields:
                if field in available_fields:
                    selected_fields.add(field)
    
    return list(selected_fields)


def query_data_by_scene(scene_name: str, scene_config: Dict = None, params: Dict[str, Any] = None, user_question: str = "") -> Dict:
    """
    根据场景名称查询数据，是对call_api_by_scene的封装
    
    Args:
        scene_name: 场景名称，必须是API_CONFIGS中定义的场景之一
        scene_config: 场景配置，可选参数
        params: 查询参数，用于更新body_template中的queryParams
        user_question: 用户问题，用于智能筛选字段
        
    Returns:
        API响应结果，包含DataFrame的markdown表示
    """
    print("API请求中3。。")

    # 记录调用信息
    logger.info(f"查询场景数据: {scene_name}")
    if scene_config:
        logger.debug(f"场景配置: {scene_config.get('name', 'unknown')}")
    if user_question:
        logger.debug(f"用户问题: {user_question}")
    
    # 获取API配置键
    api_key = SCENE_TO_API_KEY.get(scene_name)
    if not api_key:
        # 如果映射中没有找到，尝试直接使用场景名称
        api_key = scene_name
        logger.warning(f"未在映射中找到场景名称 '{scene_name}'，尝试直接使用作为API键")
    print("API请求中4。。")
    # 调用API获取原始数据
    api_result = call_api_by_scene(api_key, params)
    
    # 如果API调用成功，转换数据
    if api_result.get("status") == "success" and "data" in api_result:
        # 传递user_question参数给transform_api_data函数
        transformed_result = transform_api_data(api_key, api_result, user_question)
        return transformed_result
    
    # 如果API调用失败，直接返回原始结果
    return api_result

def query_bank_branch(params: Dict[str, Any] = None) -> Dict:
    """
    银行经营查询接口
    
    Args:
        params: 查询参数，可包含dataDt, belgOrgId, curCd, statsObj, projNm, dt等
        
    Returns:
        查询结果
    """
    return call_api_by_scene("bank_branch_query", params)

def query_bank_customer(params: Dict[str, Any] = None) -> Dict:
    """
    银行客户查询接口
    
    Args:
        params: 查询参数，可包含dataDt, df, paOrgNm, bigNm, coreProdNm等
        
    Returns:
        查询结果
    """
    return call_api_by_scene("bank_customer_query", params)

def add_api_config(scene_name: str, url: str, headers: Dict, body_template: Dict) -> None:
    """
    添加新的API配置
    
    Args:
        scene_name: 场景名称
        url: API URL
        headers: 请求头
        body_template: 请求体模板
    """
    if scene_name in API_CONFIGS:
        logger.warning(f"覆盖已存在的API配置: {scene_name}")
    
    API_CONFIGS[scene_name] = {
        "url": url,
        "headers": headers,
        "body_template": body_template
    }
    
    logger.info(f"已添加API配置: {scene_name}")

def reload_api_configs() -> None:
    """
    重新加载API配置
    """
    global API_CONFIGS
    API_CONFIGS = load_api_configs()
    logger.info("已重新加载API配置")
