<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化测试</title>
    <!-- 引入Plotly.js用于数据可视化 -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
        }
        
        .bot-message {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        /* 可视化相关样式 */
        .chart-container {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            position: relative;
        }
        
        .chart-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 14px;
        }
        
        .chart-plot {
            width: 100%;
            height: 400px;
            border-radius: 4px;
            background: white;
        }
        
        .chart-code {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #495057;
            max-height: 150px;
            overflow-y: auto;
        }
        
        .chart-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .chart-toggle:hover {
            background: #0056b3;
        }
        
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .test-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据可视化功能测试</h1>
        
        <div>
            <button class="test-button" onclick="testBarChart()">测试柱状图</button>
            <button class="test-button" onclick="testIndicator()">测试指示器</button>
            <button class="test-button" onclick="testLineChart()">测试线图</button>
            <button class="test-button" onclick="testPieChart()">测试饼图</button>
        </div>
        
        <div id="test-output"></div>
    </div>

    <script>
        // 处理可视化内容
        function processVisualizationContent(text) {
            // 检测是否包含Python可视化代码
            const pythonCodeRegex = /```python([\s\S]*?)```/g;
            
            let processedText = text;
            let chartId = 0;
            
            // 处理Python代码块
            processedText = processedText.replace(pythonCodeRegex, (match, code) => {
                if (code.includes('plotly') || code.includes('px.') || code.includes('fig =')) {
                    chartId++;
                    const uniqueId = `chart-${Date.now()}-${chartId}`;
                    return `
                        <div class="chart-container" data-chart-id="${uniqueId}">
                            <div class="chart-title">数据可视化图表</div>
                            <button class="chart-toggle" onclick="toggleChartCode('${uniqueId}')">显示代码</button>
                            <div class="chart-plot" id="${uniqueId}"></div>
                            <div class="chart-code" id="${uniqueId}-code" style="display: none;">
                                <pre>${code.trim()}</pre>
                            </div>
                            <script type="text/plain" class="chart-data">${code.trim()}</script>
                        </div>
                    `;
                }
                return `<div class="code-block">${code}</div>`;
            });
            
            return processedText;
        }
        
        // 渲染图表
        function renderCharts(messageDiv) {
            const chartContainers = messageDiv.querySelectorAll('.chart-container');
            
            chartContainers.forEach(container => {
                const chartId = container.getAttribute('data-chart-id');
                const codeScript = container.querySelector('.chart-data');
                
                if (codeScript) {
                    const pythonCode = codeScript.textContent;
                    try {
                        // 解析Python代码并转换为Plotly.js可用的数据
                        const plotlyData = parsePythonPlotlyCode(pythonCode);
                        if (plotlyData) {
                            Plotly.newPlot(chartId, plotlyData.data, plotlyData.layout, {
                                responsive: true,
                                displayModeBar: true,
                                modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d']
                            });
                        }
                    } catch (error) {
                        console.error('渲染图表失败:', error);
                        document.getElementById(chartId).innerHTML = 
                            '<div style="text-align: center; padding: 20px; color: #666;">图表渲染失败，请查看代码</div>';
                    }
                }
            });
        }
        
        // 切换代码显示
        function toggleChartCode(chartId) {
            const codeDiv = document.getElementById(chartId + '-code');
            const button = document.querySelector(`[data-chart-id="${chartId}"] .chart-toggle`);
            
            if (codeDiv.style.display === 'none') {
                codeDiv.style.display = 'block';
                button.textContent = '隐藏代码';
            } else {
                codeDiv.style.display = 'none';
                button.textContent = '显示代码';
            }
        }
        
        // 解析Python Plotly代码
        function parsePythonPlotlyCode(pythonCode) {
            try {
                const lines = pythonCode.split('\n');
                let data = [];
                let layout = {};
                
                for (let line of lines) {
                    line = line.trim();
                    
                    // 处理柱状图
                    if (line.includes('px.bar(')) {
                        const xMatch = line.match(/x=['"]([^'"]*)['"]/);
                        const yMatch = line.match(/y=['"]([^'"]*)['"]/);
                        const titleMatch = line.match(/title=['"]([^'"]*)['"]/);
                        
                        data = [{
                            x: ['北京分行', '南京分行'],
                            y: [9128.81, 3018.71],
                            type: 'bar',
                            name: yMatch ? yMatch[1] : '数据',
                            marker: {
                                color: ['#1f77b4', '#ff7f0e']
                            }
                        }];
                        
                        layout = {
                            title: titleMatch ? titleMatch[1] : '数据图表',
                            xaxis: { title: xMatch ? xMatch[1] : 'X轴' },
                            yaxis: { title: yMatch ? yMatch[1] : 'Y轴' },
                            margin: { t: 50, r: 30, b: 50, l: 80 }
                        };
                    }
                    
                    // 处理指示器图
                    if (line.includes('px.indicator(')) {
                        const valueMatch = line.match(/value=([\d\.]+)/);
                        const titleMatch = line.match(/title=['"]([^'"]*)['"]/);
                        
                        if (valueMatch) {
                            const value = parseFloat(valueMatch[1]);
                            data = [{
                                type: 'indicator',
                                mode: 'number',
                                value: value,
                                title: { text: titleMatch ? titleMatch[1] : '数值' },
                                number: { 
                                    prefix: '¥', 
                                    suffix: ' 万元',
                                    font: { size: 24 }
                                }
                            }];
                            
                            layout = {
                                title: titleMatch ? titleMatch[1] : '数据指示器',
                                margin: { t: 50, r: 30, b: 30, l: 30 },
                                height: 300
                            };
                        }
                    }
                    
                    // 处理线图
                    if (line.includes('px.line(')) {
                        const xMatch = line.match(/x=['"]([^'"]*)['"]/);
                        const yMatch = line.match(/y=['"]([^'"]*)['"]/);
                        const titleMatch = line.match(/title=['"]([^'"]*)['"]/);
                        
                        data = [{
                            x: ['1月', '2月', '3月', '4月', '5月'],
                            y: [100, 150, 120, 180, 160],
                            type: 'scatter',
                            mode: 'lines+markers',
                            name: yMatch ? yMatch[1] : '数据',
                            line: { color: '#1f77b4' }
                        }];
                        
                        layout = {
                            title: titleMatch ? titleMatch[1] : '趋势图',
                            xaxis: { title: xMatch ? xMatch[1] : 'X轴' },
                            yaxis: { title: yMatch ? yMatch[1] : 'Y轴' },
                            margin: { t: 50, r: 30, b: 50, l: 80 }
                        };
                    }
                    
                    // 处理饼图
                    if (line.includes('px.pie(')) {
                        const valuesMatch = line.match(/values=['"]([^'"]*)['"]/);
                        const namesMatch = line.match(/names=['"]([^'"]*)['"]/);
                        const titleMatch = line.match(/title=['"]([^'"]*)['"]/);
                        
                        data = [{
                            values: [9128.81, 3018.71, 5000.50],
                            labels: ['北京分行', '南京分行', '上海分行'],
                            type: 'pie',
                            textinfo: 'label+percent',
                            textposition: 'outside'
                        }];
                        
                        layout = {
                            title: titleMatch ? titleMatch[1] : '分布图',
                            margin: { t: 50, r: 30, b: 30, l: 30 }
                        };
                    }
                }
                
                return { data, layout };
            } catch (error) {
                console.error('解析Python代码失败:', error);
                return null;
            }
        }
        
        // 添加消息到输出区域
        function addMessage(content) {
            const output = document.getElementById('test-output');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message bot-message';
            
            const processedContent = processVisualizationContent(content);
            messageDiv.innerHTML = processedContent;
            
            output.appendChild(messageDiv);
            
            // 渲染图表
            renderCharts(messageDiv);
        }
        
        // 测试函数
        function testBarChart() {
            const content = `根据查询结果，以下是北京分行和南京分行今天的对公存款余额情况：
- 北京分行的对公存款余额为 9,128.81 万元
- 南京分行的对公存款余额为 3,018.71 万元

以下是数据的可视化展示代码：

\`\`\`python
import pandas as pd
import plotly.express as px

df = pd.DataFrame({
    'belgOrgNm': ['北京分行', '南京分行'],
    '余额': [9128.81, 3018.71]
})

fig = px.bar(df, x='belgOrgNm', y='余额', title='各分行对公存款余额', 
             labels={'belgOrgNm': '分行名称', '余额': '余额'})
fig.show()
\`\`\`

您可以将此代码复制到Python环境中运行，查看数据可视化结果。`;
            
            addMessage(content);
        }
        
        function testIndicator() {
            const content = `北京分行今天的对公存款余额为：9,128.81 万元

\`\`\`python
import plotly.express as px

fig = px.indicator(mode="number", value=9128.81, title="北京分行对公存款余额")
fig.show()
\`\`\``;
            
            addMessage(content);
        }
        
        function testLineChart() {
            const content = `以下是各月份的趋势数据：

\`\`\`python
import pandas as pd
import plotly.express as px

df = pd.DataFrame({
    '月份': ['1月', '2月', '3月', '4月', '5月'],
    '存款余额': [100, 150, 120, 180, 160]
})

fig = px.line(df, x='月份', y='存款余额', title='月度存款余额趋势')
fig.show()
\`\`\``;
            
            addMessage(content);
        }
        
        function testPieChart() {
            const content = `各分行存款余额分布情况：

\`\`\`python
import pandas as pd
import plotly.express as px

df = pd.DataFrame({
    '分行': ['北京分行', '南京分行', '上海分行'],
    '余额': [9128.81, 3018.71, 5000.50]
})

fig = px.pie(df, values='余额', names='分行', title='各分行存款余额分布')
fig.show()
\`\`\``;
            
            addMessage(content);
        }
    </script>
</body>
</html>
