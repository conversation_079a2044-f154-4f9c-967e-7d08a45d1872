<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts��ʾ����</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            min-height: 400px;
        }
        
        .echarts-chart {
            width: 100%;
            height: 400px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ECharts��ʾ����</h1>
        
        <div class="test-section">
            <h3>����1: ֱ��ECharts��ʼ��</h3>
            <div id="directChart" class="echarts-chart"></div>
            <button onclick="testDirectChart()">����ֱ��ͼ��</button>
        </div>
        
        <div class="test-section">
            <h3>����2: ģ����JSON����</h3>
            <div class="chart-container" id="jsonContainer">
                <!-- ��������JSON���� -->
            </div>
            <button onclick="testJsonConfig()">����JSON����</button>
        </div>
        
        <div class="test-section">
            <h3>����3: ģ����������</h3>
            <div class="chart-container" id="fullFlowContainer">
                <!-- �����ģ����������Ϣ���� -->
            </div>
            <button onclick="testFullFlow()">������������</button>
        </div>
        
        <div class="test-section">
            <h3>������־</h3>
            <div id="debugLog" class="log"></div>
            <button onclick="clearLog()">�����־</button>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        // ����1: ֱ��ECharts��ʼ��
        function testDirectChart() {
            log('��ʼ����ֱ��ECharts��ʼ��...');
            
            try {
                const chartDiv = document.getElementById('directChart');
                const chart = echarts.init(chartDiv);
                
                const option = {
                    title: {
                        text: '���������Աȣ�ֱ�Ӳ��ԣ�',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    xAxis: {
                        type: 'category',
                        data: ['��������', '�Ϻ�����', '�Ͼ�����', '�Ϸʷ���']
                    },
                    yAxis: {
                        type: 'value',
                        name: '���(��Ԫ)'
                    },
                    series: [{
                        name: '���(��Ԫ)',
                        type: 'bar',
                        data: [7731.07, 9892.58, 5368.80, 8017.85],
                        itemStyle: {
                            color: '#5470c6'
                        }
                    }]
                };
                
                chart.setOption(option);
                log('? ֱ��ECharts��ʼ���ɹ�');
                
            } catch (error) {
                log(`? ֱ��ECharts��ʼ��ʧ��: ${error.message}`);
            }
        }
        
        // ����2: ģ����JSON����
        function testJsonConfig() {
            log('��ʼ����JSON���ý���...');
            
            const jsonConfig = {
                "title": {
                    "text": "���������Աȣ�JSON���ã�",
                    "left": "center"
                },
                "tooltip": {
                    "trigger": "axis"
                },
                "xAxis": {
                    "type": "category",
                    "data": ["��������", "�Ϻ�����", "�Ͼ�����", "�Ϸʷ���"]
                },
                "yAxis": {
                    "type": "value",
                    "name": "���(��Ԫ)"
                },
                "series": [{
                    "name": "���(��Ԫ)",
                    "type": "bar",
                    "data": [7731.07, 9892.58, 5368.80, 8017.85],
                    "itemStyle": {
                        "color": "#91cc75"
                    }
                }]
            };
            
            try {
                const container = document.getElementById('jsonContainer');
                container.innerHTML = JSON.stringify(jsonConfig);
                
                // ģ��ǰ�˴����߼�
                setTimeout(() => {
                    processChartContainer(container);
                }, 100);
                
                log('? JSON�������óɹ�');
                
            } catch (error) {
                log(`? JSON���ò���ʧ��: ${error.message}`);
            }
        }
        
        // ����3: ģ����������
        function testFullFlow() {
            log('��ʼ������������...');
            
            try {
                const container = document.getElementById('fullFlowContainer');
                
                // ģ���˷��ص�����HTML����
                const mockResponse = `
                    <strong>ϵͳ:</strong> ���ݲ�ѯ����������еĶԹ����������£�
                    
                    <div class='chart-container'>
                    {
                        "title": {
                            "text": "���������Աȣ��������̣�",
                            "left": "center"
                        },
                        "tooltip": {
                            "trigger": "axis"
                        },
                        "xAxis": {
                            "type": "category",
                            "data": ["��������", "�Ϻ�����", "�Ͼ�����", "�Ϸʷ���"]
                        },
                        "yAxis": {
                            "type": "value",
                            "name": "���(��Ԫ)"
                        },
                        "series": [{
                            "name": "���(��Ԫ)",
                            "type": "bar",
                            "data": [7731.07, 9892.58, 5368.80, 8017.85],
                            "itemStyle": {
                                "color": "#fac858"
                            }
                        }]
                    }
                    </div>
                `;
                
                container.innerHTML = mockResponse;
                
                // ����ͼ������
                setTimeout(() => {
                    const chartContainers = container.querySelectorAll('.chart-container');
                    chartContainers.forEach(processChartContainer);
                }, 100);
                
                log('? �������̲��������ɹ�');
                
            } catch (error) {
                log(`? �������̲���ʧ��: ${error.message}`);
            }
        }
        
        // ����ͼ�������ĺ�������������ҳ�棩
        function processChartContainer(container) {
            const content = container.innerHTML.trim();
            log(`����ͼ�����������ݳ���: ${content.length}`);
            
            // ����Ƿ����ECharts����
            if (content.startsWith('{') && (content.includes('option') || content.includes('series'))) {
                try {
                    // ����ECharts����
                    const chartConfig = JSON.parse(content);
                    log('? JSON�����ɹ�');
                    
                    // ����ͼ������
                    const chartDiv = document.createElement('div');
                    chartDiv.className = 'echarts-chart';
                    chartDiv.id = `chart_${Date.now()}_${Math.random()}`;
                    chartDiv.style.width = '100%';
                    chartDiv.style.height = '400px';
                    
                    // ������������ͼ��div
                    container.innerHTML = '';
                    container.appendChild(chartDiv);
                    
                    // �ӳٳ�ʼ��ȷ��DOM����Ⱦ
                    setTimeout(() => {
                        try {
                            // ��ʼ��EChartsʵ��
                            const chart = echarts.init(chartDiv);
                            
                            // ��ȡ����ѡ��
                            const option = chartConfig.option || chartConfig;
                            log('? ��ʼ����EChartsѡ��');
                            
                            // ����ͼ��ѡ��
                            chart.setOption(option);
                            log('? EChartsͼ����Ⱦ�ɹ�');
                            
                        } catch (chartError) {
                            log(`? ECharts��ʼ��ʧ��: ${chartError.message}`);
                            container.innerHTML = `<div style="color: red;">ͼ���ʼ��ʧ��: ${chartError.message}</div>`;
                        }
                    }, 100);
                    
                } catch (error) {
                    log(`? JSON����ʧ��: ${error.message}`);
                    container.innerHTML = `<div style="color: red;">���ý���ʧ��: ${error.message}</div>`;
                }
            } else {
                log('? ������Ч��ECharts����');
            }
        }
        
        // ҳ�������ɺ�ĳ�ʼ��
        document.addEventListener('DOMContentLoaded', function() {
            log('ҳ�������ɣ�ECharts����ҳ�����');
            log(`ECharts�汾: ${echarts.version || 'δ֪'}`);
        });
    </script>
</body>
</html>
