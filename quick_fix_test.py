#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
�����޸�����
ֱ�Ӳ���ECharts�������ɺ͸�ʽ
"""

import json
import pandas as pd

def test_config_generation():
    """������������"""
    print("=" * 60)
    print("�����޸�����")
    print("=" * 60)
    
    # ģ������ʵ������
    test_data = [
        {"��������": "711100", "��������": "��������", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 7731.07},
        {"��������": "731109", "��������": "�Ϻ�����", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 9892.58},
        {"��������": "703220", "��������": "�Ͼ�����", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 5368.80},
        {"��������": "703260", "��������": "�Ϸʷ���", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 2693.49},
        {"��������": "703400", "��������": "���ݷ���", "����": "20250617", "��Ŀ����": "�Թ����", "���(��Ԫ)": 1214.51}
    ]
    
    df = pd.DataFrame(test_data)
    print(f"������״: {df.shape}")
    print(f"����: {list(df.columns)}")
    
    # ������������
    numeric_cols = df.select_dtypes(include=["number"]).columns.tolist()
    categorical_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()
    
    print(f"��ֵ��: {numeric_cols}")
    print(f"������: {categorical_cols}")
    
    # ѡ����ʵ���
    institution_col = "��������"
    amount_col = "���(��Ԫ)"
    
    categories = df[institution_col].astype(str).tolist()
    values = [float(v) if pd.notna(v) else 0 for v in df[amount_col].tolist()]
    
    print(f"��������: {categories}")
    print(f"��ֵ����: {values}")
    
    # �����Ż���ECharts����
    colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
    
    config = {
        "title": {
            "text": "�����жԹ�������Ա�",
            "left": "center",
            "textStyle": {
                "fontSize": 16
            }
        },
        "tooltip": {
            "trigger": "axis",
            "axisPointer": {
                "type": "shadow"
            },
            "formatter": "{b}: {c}��Ԫ"
        },
        "grid": {
            "left": "10%",
            "right": "10%",
            "bottom": "15%",
            "top": "15%",
            "containLabel": True
        },
        "xAxis": {
            "type": "category",
            "data": categories,
            "axisLabel": {
                "rotate": 0,
                "interval": 0,
                "fontSize": 12
            },
            "axisTick": {
                "alignWithLabel": True
            }
        },
        "yAxis": {
            "type": "value",
            "name": "���(��Ԫ)",
            "nameTextStyle": {
                "fontSize": 12
            },
            "axisLabel": {
                "fontSize": 12
            }
        },
        "series": [{
            "name": "���(��Ԫ)",
            "type": "bar",
            "data": values,
            "itemStyle": {
                "color": colors[0],
                "borderRadius": [4, 4, 0, 0]
            },
            "label": {
                "show": True,
                "position": "top",
                "fontSize": 11,
                "formatter": "{c}"
            },
            "barWidth": "60%"
        }],
        "color": colors
    }
    
    # ���л�ΪJSON
    json_str = json.dumps(config, ensure_ascii=False, indent=2, separators=(',', ': '))
    
    print("\n" + "=" * 60)
    print("���ɵ�ECharts����:")
    print("=" * 60)
    print(json_str)
    
    # ��֤JSON��ʽ
    try:
        parsed = json.loads(json_str)
        print("\n? JSON��ʽ��֤ͨ��")
        
        # ����Ҫ�ֶ�
        required_fields = ["title", "xAxis", "yAxis", "series"]
        for field in required_fields:
            if field in parsed:
                print(f"? ����{field}�ֶ�")
            else:
                print(f"? ȱ��{field}�ֶ�")
                
        # ���浽�ļ�
        with open("generated_chart_config.json", "w", encoding="utf-8") as f:
            f.write(json_str)
        print("? �����ѱ��浽 generated_chart_config.json")
        
        # ����HTML�����ļ�
        generate_test_html(json_str)
        
        return json_str
        
    except json.JSONDecodeError as e:
        print(f"? JSON��ʽ����: {e}")
        return None

def generate_test_html(config_json):
    """���ɲ���HTML�ļ�"""
    
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>�����޸�����</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .chart-container {{
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }}
        #chart {{
            width: 100%;
            height: 400px;
        }}
        .config-display {{
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>�����޸�����</h1>
        <p>����ֱ��ʹ�����ɵ����ý��в���</p>
        
        <div class="chart-container">
            <div id="chart"></div>
        </div>
        
        <h3>ʹ�õ�����:</h3>
        <div class="config-display">{config_json}</div>
        
        <div id="status" style="margin-top: 20px; padding: 10px; border-radius: 4px;"></div>
    </div>

    <script>
        function setStatus(message, type = 'info') {{
            const statusDiv = document.getElementById('status');
            const colors = {{
                'success': '#d4edda',
                'error': '#f8d7da',
                'info': '#d1ecf1'
            }};
            statusDiv.style.backgroundColor = colors[type] || colors.info;
            statusDiv.style.border = `1px solid ${{colors[type] || colors.info}}`;
            statusDiv.innerHTML = message;
            console.log(message);
        }}
        
        try {{
            setStatus('��ʼ��ʼ��ECharts...', 'info');
            
            // ���ECharts�Ƿ����
            if (typeof echarts === 'undefined') {{
                throw new Error('ECharts��δ����');
            }}
            
            const chartDiv = document.getElementById('chart');
            const chart = echarts.init(chartDiv);
            
            const config = {config_json};
            
            setStatus('����ͼ��ѡ��...', 'info');
            chart.setOption(config);
            
            setStatus('? ͼ����Ⱦ�ɹ���', 'success');
            
            // ��Ӧʽ����
            window.addEventListener('resize', () => {{
                chart.resize();
            }});
            
        }} catch (error) {{
            setStatus(`? ͼ����Ⱦʧ��: ${{error.message}}`, 'error');
            console.error('��ϸ����:', error);
        }}
    </script>
</body>
</html>'''
    
    with open("quick_fix_test.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print("? ����HTML������: quick_fix_test.html")

def test_json_parsing():
    """����JSON����"""
    print("\n" + "=" * 60)
    print("����JSON����")
    print("=" * 60)
    
    # ģ������������JSON
    problematic_json = '''{"title": { "text": "����������Թ�������", "subtext": "��������ʾ���Ա�", "left": "center" }, "tooltip": { "trigger": "axis", "axisPointer": { "type": "shadow" } }, "legend": { "data": ["���"], "right": "10%" }, "xAxis": { "type": "category", "name": "��������", "data": ["belgOrgNm"] }, "yAxis": { "type": "value", "name": "��� (��λ: Ԫ)" }, "series": [{ "name": "���", "type": "bar", "data": ["���"], "label": { "show": true, "position": "top", "formatter": "{c} Ԫ" }, "itemStyle": { "color": "#5793f3" } }]}'''
    
    print("ԭʼJSON:")
    print(problematic_json)
    
    try:
        parsed = json.loads(problematic_json)
        print("\n? JSON�����ɹ�")
        print("������Ľṹ:")
        print(json.dumps(parsed, ensure_ascii=False, indent=2))
    except json.JSONDecodeError as e:
        print(f"\n? JSON����ʧ��: {e}")

if __name__ == "__main__":
    print("? ��ʼ�����޸�����...")
    
    # ������������
    config = test_config_generation()
    
    # ����JSON����
    test_json_parsing()
    
    print("\n" + "=" * 60)
    print("? �����޸�������ɣ�")
    print("=" * 60)
    
    if config:
        print("�������ɵ��ļ�:")
        print("- quick_fix_test.html (ֱ�Ӳ���)")
        print("- generated_chart_config.json (�����ļ�)")
        print("- test_chart_fix.html (��������)")
    else:
        print("? ��������ʧ�ܣ��������")
