
# 
## 介绍
 是一个开源项目，旨在提供一个基于大型语言模型（LLM）的多轮问答系统。该系统结合了先进的意图识别和词槽填充（Slot Filling）技术，致力于提升对话系统的理解深度和响应精确度。本项目为开发者社区提供了一个灵活、高效的解决方案，用于构建和优化各类对话型应用。

<img src="https://github.com/answerlink//blob/main/images/demo.gif"  height="388" width="690">

<img src="https://github.com/answerlink//blob/main/images/slot_multi-turn-flow.png"  height="388" width="690">

## 特性
1. 多轮对话管理：能够处理复杂的对话场景，支持连续多轮交互。
2. 意图识别：准确判定用户输入的意图，支持自定义意图扩展。
3. 词槽填充：动态识别并填充关键信息（如时间、地点、对象等）。
4. 接口槽技术：直接与外部APIs对接，实现数据的实时获取和处理。
5. 自适应学习：不断学习用户交互，优化回答准确性和响应速度。
6. 易于集成：提供了详细的API文档，支持多种编程语言和平台集成。
7. v-0331版本 增加历史对话记录list，
   slot 部分添加基于历史对话的提取
   todo:用户查看对话历史记录,后续添加function call 将支行id 调用实现
   需要在一些input处 增加历史对话的扩充，然后再提取信息，
   v-0402 
   判断为basic 对话时候，也需要进行历史对话的扩充，然后再判断是否是basic对话
   v-0407 
   对场景 schema 信息进行扩展，在prompt 中丰富schema用途描述和字段描述 ，提高query 多表识别的准确率 
   添加查询帮助功能
   v-0408 
   意图识别的健壮性与从识别到场景到流程架构的合理性，之前process_multi_question  为先进行simple chat 的判断，然后再进行multi-turn 的判断，
   - 用户输入 ：用户提交查询请求
- 意图识别 ：

- 系统判断用户意图是否为查询请求
- 如果是查询请求，进入场景识别步骤
- 如果不是查询请求，转为普通对话处理
- 场景识别 ：

- 根据用户输入识别适用的场景（如银行分行查询、客户查询等）
- 加载对应场景的配置信息（参数模板、API配置等）
- 参数提取 ：

- 使用LLM从用户输入中提取关键参数
- 基于场景配置中的参数模板进行提取
- 输出JSON格式的参数列表
- 参数处理 ：

- 应用各种参数处理器对提取的参数进行处理
- 包括分行名称处理、项目名称模糊匹配、指标类型处理等
- 补充默认参数（如币种、统计口径等）
- API调用 ：

- 根据场景配置中的API配置构建请求
- 将处理后的参数映射到API参数
- 发送请求并获取响应
- 数据处理 ：

- 解析API响应数据
- 进行字段映射和格式转换
- 生成可读性好的表格或图表
- 结果展示 ：

- 将处理后的数据展示给用户
- 可能包括表格、图表或文字描述
- 提供进一步查询的建议
   v-0409 
   项目名称required， 指标的不一定requred，
   现在先基于历史对话和现在的query进行意图识别，再进行意图分发，根据不同意图以及场景进行处理。
   todo:不同场景提取的参数和转化的参数优化。 在提示词中添加数据mapping 信息，提高query 提取的准确率
   指标的含义，对历史对话的总结和输出，两个表的不同信息的整合，查询语句涉及两个表呢，排序和内容分析输出，所有分行的解析，分析输出，不同内容的整合，
   参数的提取方案：
   v-0410
   1添加多表
   2、将chat 闲聊，改为simple_chat
   1. 解耦数据源与场景 ：通过 table_name 字段将场景与数据源关联，而不是硬编码
2. 参数处理更灵活 ：处理器可以根据场景和表名进行差异化处理
3. 场景定义更清晰 ：每个场景专注于特定维度的查询，参数定义更精准
4. 扩展性更好 ：
   - 新增查询维度只需添加新场景
   - 新增数据表只需更新 tables.json
   - 新增参数处理器只需实现接口并注册
## 后续扩展建议
1. 增加场景自动识别 ：根据用户输入自动判断应该使用哪个场景
2. 增加参数映射配置 ：在 tables.json 中增加参数映射配置，使参数处理更灵活
3. 增加数据源适配器 ：支持不同类型的数据源（API、数据库、文件等）
4. 增加缓存机制 ：对频繁查询的数据进行缓存，提高响应速度
   v-0411
   在意图type的识别中，添加对query场景的知识和分类，而不是“场景”这个词，以及其他意图的分类,query的prompt 增加区分性，另外对于非列出的意图，直接让大模型进行判断，
   对于query不同场景的区分，让大模型进行每个schema 的总结，尤其是用途和schema关键词的description  
   在查询中不能进行下一次查询，每个用户每次只能进行一次查询，不能进行多次查询，
   记录从开始到最后的流程和每个阶段的输出，以及每阶段的时间
   历史对话的总结，并添加到能力中
   添加最后输出端的格式规范和是否满足需求，如不满足，需要xx
  


    “北京分行对公一般性贷款余额是多少” 1+1 等于3吗
    最后的输出也需要经过大模型，是否合理，不合理的直接让大模型输出

- 分层处理 ：将复杂的对话处理拆分为多个层次，每层负责特定功能
- 意图分发 ：根据不同意图类型采用不同处理策略，提高处理精度
- 上下文理解 ：通过意图补全和相关性判断，实现多轮对话理解
- 场景化处理 ：针对不同业务场景使用专门的处理器，提高回复质量
- 兜底机制 ：对于未命中场景的情况提供合理引导
--0417
表中添加维度和指标区分



### LlamaIndex与RAG在当前架构中的潜在价值
1. 场景/表识别增强 ：
   
   - 当前系统似乎是基于预定义的场景模板进行意图识别
   - LlamaIndex可以帮助构建知识库，包含各种表结构、字段含义和业务规则
   - 这可以提高系统对用户查询意图的理解准确性，特别是在多表混合查询场景
2. 数据索引与检索 ：
   
   - LlamaIndex专为构建高效的向量索引而设计
   - 可以对您的表结构、业务规则和历史查询进行索引
   - 当用户提出查询时，系统可以检索最相关的表结构和查询模式
3. 上下文增强 ：
   
   - 当前系统已有对话历史记录功能
   - LlamaIndex可以帮助从历史对话中提取更丰富的上下文信息
   - 这对于处理复杂的多轮查询特别有价值

0418
1、开始时，如果意图明确直接查询？
2、rag 的使用是否对于现在表的查询有益
3、function call 

0421

多轮对话上下文优化，提示词优化
深圳比亚迪公司的对公存款余额呢
09:43:11
请问客户所在机构名称是？（例如：中信银行、总行、南京分行、上海分行等，简称如“北分”也可） 请问所需日期是？（时间格式：yyyyMMdd，可以是月末、年末、季末等相关日期，或近一年、近一个季度等）
09:43:37
深圳分行，今天
09:43:50
请问客户所在机构名称是哪个？例如中信银行总行、南京分行、上海分行等。如果使用简称，比如“北分”，请告诉我，我会为您转换为“北京分行”。


解决参数提取的混乱问题，简化参数提取的流程，提高效率。
- 场景自动识别 ：增强场景自动识别能力
- 参数映射配置 ：更灵活的参数处理
- 多数据源适配 ：支持更多类型的数据源
- 缓存机制 ：对频繁查询的数据进行缓存
- 维度和指标区分 ：在表中添加维度和指标区分，提升语义理解

0422  
完成session 信息历史的分组与匹配，
session信息的本地存储
todo:
0、user 信息的分组和匹配，参数的处理
1、分支行权限，
2、分支行名称和 id 匹配
3、排名信息的查询
mapping 信息的缓存和优化，
支行的参数的提取方案：
beldOrgID 机构号
fstLVlBrchOrgID  一级分行机构号
curCd 币种
bigNm 业务条线名称
statsObj  1 会计方式 0 绩效方式
brchLoca 地区 西部地区
projNm 营业净收入
prodcd 0 营业净收入 1 利息净收入（营收） 2 非息收入 3 个贷收入

利润信息：
营业净收入

userid =1 默认为登录

if  userid =1 or
users  进行权限控制

0424 

用户所属的机构id 的转化与权限界定
机构id的转化，分支行id的转化

用户所属机构的转化，
支行名称的转化，机构id 匹配的 
方案用api 请求，use_id ，方案


 

前端获取用户信息， 机构信息
后端处理，匹配
0428
 update_conversation_history
 :param session_id: 会话ID，如果提供则同时更新会话服务

添加会话锁管理是否需要 
        self._session_locks = {}  # 每个会话一个锁
        self._global_lock = asyncio.Lock()  # 全局锁，用于创建新会话
        self._active_sessions = set()  # 活跃会话集合

0506
1、修改调用api 获取结果,各个表的调用修改与字段的映射关系，
哪个config 获取表和api信息，机构分组，各个分行，各个支行识别
内容识别能力，绩效表，更多字段，以及策略,案例
2、用户权限的识别和控制，不同用户的权限不同，redis 缓存用户id ,机构id ,分行iD
3、结果缓存机制 ：对频繁查询的结果进行缓存
当前参数处理器的注册是硬编码的，可以考虑实现动态注册机制：

1. 基于配置的处理器注册 ：通过配置文件注册处理器
2. 处理器链 ：支持多个处理器按顺序处理同一参数
3. 处理器条件 ：基于条件决定是否应用处理器

0507 
案例 ，绩效表识别，完善更多字段
user_id + session_id 缓存api
mysql api 
分行，支行id,机构id和分行id配置

分行名称与机构id的匹配，
redis 方案还是mysql方案？


0508 
数据流最后的字段转化完成，success 字段的转化，


0509
 以及一级分行和机构id 的区分，dataframe 的markdown() , redis的session userid 的变化


0512
id 的转化，，
梳理问题：
用户
数据日期 只有
机构名称和机构id 即可解决单查询的问题
0A 本外币折人民币
156人民币
0b 外币

开始为156 后面无币种的指定时，需默认为OA

0513
用户转id 还是直接用name? √ 
项目名称的模糊查询，和匹配查询√ 利用jacarrd
设计准确率评估公式√ 
可以查询支行数据，√
0514
参数以名称填充，√
metirc的模糊查询 √



0515
客户数据梳理，
0516
多个问题，客户数据提出，绩效数据查看如何验证，redis缓存key，mysql表结构，



0519
问题的识别与分割，放后面吧，
识别为单任务还是多任务，将多任务拆解为多个任务分别执行，然后再汇总结果，question1 和answer1，question2，answer2，question3，answer3，question4，
从意图的识别到用户问题是否需要的合并（比如问到同一个场景下的，相同项目，不同机构）或者拆解（不同机构，不同项目，）到参数提取和请求


0520
用户日志信息的保存与查看,mysql 
app redis，mysql 异步，

create_session 
append_message 



521 

一个任务的并发workflow
现在历史记录从缓存中获取还是从mysql中获取
数据表提出，mysql 缓存，
前端历史记录展示
内存大小， 600M-1.5G
单个的session的记录和所有的sessions，
522
图表，数据，权限，mysql缓存，配置中心
work-flow
从本地获取session_data 方式注释，改为从redis中获取,
查询权限：最佳实现位置是在 data_api2.py 文件的 call_api_by_scene 函数中，因为这是所有数据查询的公共入口点

firstLevelorgNo: null  -->fstLvlBrchOrgid
orgName:"总行"   --》belgOrgNm
orgNo: "711000" --》belgOrgId
orgType: "head office'
[[Prototype]]: object
userName:"


0526

前后端接口，梳理 半天 完成
login 时的信息缓存 2小时 

rag 验证  2天
绩效考核表与经营查询表构建与验证， 1-2 天
rag 验证  2天，session_service 精简

如果超过5列，前端显示问题，等待，表格

0603
mysql 表上线，入贴源层 2小时
fex接口

， 1-2 天
权限控制,


0604
只能查昨天以前的数据
绩效考核表与经营查询表构建与验证
两张表，其中项目查询以考核表为主表，经营查询表为附表，如果项目查询在考核表中没有，则使用经营查询表
考核表的项目名称字段为projectName，经营查询表的项目名称字段为projNm

前端数据传输
项目参数的模糊查询，对公一般性贷款，不是一个，而是可以多个
KPI 数据



 如果提供了机构名称，需要先转换为机构ID 此处有问题
get_first_level_branch_id_from_api
0606
1、kpi 数据跑通，
2、前端输出
3、参数值的处理


排名，组内信息等字段的处理
大模型应用与落地思考
0609
1、安全评审，ip 权限的申请（大模型的，aaas），负载均衡（没有多余服务器）
2、权限的控制验证（总分，最后一步骤对于不带分行的再次验证），前端传入
3、数据多的情况下，表的处理，metric 的控制
4、

0610
1、最后一步骤对于不带分行的再次验证
2、安全评审
3、kpi 表和基础表区分
4、前端提示词，查询说明
5、项目词匹配提取优化
6、根据用户的意图选取metric， 控制最终的结果生成

0613 
ner词的提取，最开始的时候llm


登录时前端传过的数据有
{
    "fstLvlBrchOrgId": null,
    "belgOrgNm": {

"fstLvlBrchOrgId": null,

"belgOrgNm": "总行",

"belgOrgId": "711000",

"userName": "张",

"user_id":"00162121"

}

其中 fstLvlBrchOrgId 分行id需要根据institution_mapping 获得分行名称fstLvlBrchOrgNm ，
如果fstLvlBrchOrgId为空则为填充为总行，需要将用户名称userName，user_id，belgOrgNm，
fstLvlBrchOrgId和对应的fstLvlBrchOrgNm 缓存到session中"总行",
  
  其中 fstLvlBrchOrgId 分行id需要根据institution_mapping 获得分行名称，如果为空则为总行，
  ,
用户权限的控制，

  用户为query时，check 用户所属的分行，如果是总行，则没有限制，其他的如果查询的分行和自己所属的分行不一致则反馈没有权限，
  如果查询的是支行，则需要此支行所属分行是否属于自己的分行一致，如果不属于则反馈没有权限，如果一致则继续进行查询。


  需要将用户user_id，userName，对应的分行名称缓存

  需要从前端获取

  belgOrgId


  分组的问题


多个问题并发执行
参数提取部分基于每个表的项目名等进行提取维护

查不到数和表的时候的回复
考核得分
前端显示问题



考核表对照，提出需求



前端传给后端的参数，方案，以及后端的缓存方案
后端对历史记录的查询

值： 南京分行 处理后值： 南京分行
处理前值： ******** 处理后值： ********
处理前值： ******** 处理后值： ********
处理前值： ******** 处理后值： ********
处理前值： ******** 处理后值： ********
处理前值： 对公存款 处理后值： 对公存款
处理前值： 对公存款 处理后值： 对公存款
进入项目名称处理器
处理前值： 对公存款 处理后值： 对公存款
处理前值： 对公存款 处理后值： 对公存款
处理前值： 0A 处理后值： 0A
处理前值： 0A 处理后值： 0A
处理前值： 0A 处理后值： 0A
处理前值： 0A 处理后值： 0A
处理前值： 1 处理后值： 1
处理前值： 1 处理后值： 1
处理前值： 1 处理后值： 1
处理前值： 1 处理后值： 1
处理前值： 余额 处理后值： 余额
处理前值： 余额 处理后值： 余额
处理前值： 余额 处理后值： 余额
处理前值： 余额 处理后值： 余额
转换后的参数: [{'name': 'belgOrgNm', 'value': '南京分行'}, {'name': 'dataDt', 'value': '********'}, {'name': 'projNm', 'value': '对公存款'}, {'name': 'curCd', 'value': '0A'}, {'name': 'statsObj', 'value': '1'}, {'name': 'metric_type', 'value': '余额'}]
参数已完整
result: {'status': 'success', 'data': [{'belgOrgId': '703220', 'belgOrgNm': '南京分行', 'date': '********', 'projNm': '对公存款', '余额': 1641.86}], 'total': 1, 'ranking_info': {'has_ranking': False, 'ranking_metrics': [], 'rank_range': None}}
D:\Anaconda\Lib\site-packages\urllib3\connectionpool.py:1100: InsecureRequestWarning: Unverified HTTPS request is being made to host '127.0.0.1'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(
markdown_table |   belgOrgId | belgOrgNm   |     date | projNm   |    余额 |
|------------:|:------------|---------:|:---------|--------:|
|      703220 | 南京分行    | ******** | 对公存款 | 1641.86 |
111
INFO:     127.0.0.1:55779 - "POST /multi_question HTTP/1.1" 200 OK
识别到的意图类型: query
原始输入: 北分呢
补全意图: 查询北京分行的对公存款余额、对公一般性贷款余额、收益率等指标（不包含具体客户数据）
意图补全: 北分呢 -> 查询北京分行的对公存款余额、对公一般性贷款余额、收益率等指标（不包含具体客户数据）
第四步：处理查询意图
判断当前输入是否与上一次场景相关
result: 0.9
0.9
判断提取的结果浮点数是否超过当前阈值 True
相关
table_info: {'table_name': 'fin_t_eps_fetr_jyfx_indx_org_pefm', 'supported_scenes': ['bank_branch_query'], 'api_url': 'https://api.example.com/data/xxx', 'description': '查询总行或者分行经营数据服务，比如南京分行对公存款余额、对公一般性贷款余额、收益率等指标', 'data_time_range': '2024年1月31日-昨天，每天数据', 'format': 'daily', 'key_metrics': ['时点余额', '日均余额', '当年平均利率', '余额同比', '余额上月环比'], 'query_focus': ['总分 行经营数据查询', '分行对公存款等项目规模，收益率查询', '分行各种项目指标环比分析，同比分析'], 'columns': [{'name': '日期', 'type': 'string', 'description': '数据日期，时间格式：yyyyMMdd'}, {'name': 'branch', 'type': 'string', 'description': "银行总行或者分行的名称，比如：中信银行，总行，南京分行、上海分行,可以提取多个等。 用户可能使用简称如'北分'，需要转换、提取为'北京分行'"}, {'name': 'projNm', 'type': 'string', 'description': '银行经营项目，项目名称，如对公一般性贷款、对公存款等'}, {'name': '日均余额', 'type': 'number', 'description': '某项目的规模指标'}, {'name': '平均利率', 'type': 'number', 'description': '平均利率'}, {'name': '时点余 额', 'type': 'number', 'description': '时点余额'}, {'name': '余额上月环比', 'type': 'number', 'description': '时点余额上月环比'}, {'name': '余额同比', 'type': 'number', 'description': '时点余额同比'}, {'name': '利息 或非息净收入比上月贡献度', 'type': 'number', 'description': '利息或非息净收入比月上贡献度'}, {'name': '利息 或非息净收入比上年日贡献度', 'type': 'number', 'description': '利息或非息净收入比上年日贡献度'}, {'name': ' 余额比上月贡献度', 'type': 'number', 'description': '余额比上月贡献度'}, {'name': '余额比上年贡献度', 'type': 'number', 'description': '余额比上年贡献度'}]}
get_slot_update_message stage
message: 根据用户输入和场景，提取相关参数。
历史对话:
用户: 南京分行的对公存款余额是多少
系统: 根据查询结果，截至2025年5月29日，南京分行的对公存款余额为1,641.86亿元。

以下是数据的可视化展示：
<div class='chart-container'><html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script src="https://cdn.plot.ly/plotly-2.12.1.min.js"></script>                <div id="chart_2647939772880" class="plotly-graph-div" style="height:200px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                      
              if (document.getElementById("chart_2647939772880")) {                    Plotly.newPlot(                        "chart_2647939772880",                        [{"mode":"number","title":{"text":"对公存款<br><span style='font-size:0.8em;color:gray'>南京分行 - ********</span>"},"value":1641.86,"type":"indicator"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmapgl":[{"type":"heatmapgl","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"margin":{"l":0,"r":0,"t":30,"b":0},"height":200},                        {"displayModeBar": true, "responsive": true, "modeBarButtonsToRemove": ["pan2d", "lasso2d", "select2d"]}                    )                };                            </script>        </div>
</body>
</html></div>

当前系统日期: 2025-05-29

场景: 银行总行、分行经营查询

参数模板:
[
  {
    "name": "belgOrgNm",
    "desc": "机构名称，比如：中信银行，总行，南京分行、上海分行,上海南京路支行，可以提取多个，每个“，”分割，如果提到各个分行，则为所有分行的名称,南京分行,合肥分行,福州分行,北京分行,大连分行,沈阳分行,天津分行,石家庄分行,西安分行,太原分行,呼和浩特分行,南昌分行,南宁分行,昆明分行,上海分行,苏州分行,宁波分行,杭州分行,厦门分行,青岛分行,济南分行。用户可能使用简称如'北分'，需要转换、提取为'北京分行'",
    "type": "string",
    "required": true,
    "value": ""
  },
  {
    "name": "belgOrgId",
    "desc": "机构ID与机构名称的映射关系，比如：701100-中信银行，711000-总行，703220-南京分行、703400-福州分 行,735751-泰州靖江支行,可提取多个，如果提到各个分行，则为所有分行的名称，与名称匹配不上可以为空，默认为空", 
    "type": "string",
    "required": false,
    "value": ""
  },
  {
    "name": "fstLvlBrchOrgNm",
    "desc": "机构附属的一级分行的名称，比如：中信银行，总行，南京分行、上海分行,可以提取多个，每个“，”分割。用户可能使用简称如'北分'，需要转换、提取为'北京分行'",
    "type": "string",
    "required": false,
    "value": ""
  },
  {
    "name": "pagrpgid",
    "desc": "分行分组，如'中信银行-第一组'、'中信银行-第二组'、'中信银行-第三组'、'中信银行-第四组'等，可选 参数",
    "type": "string",
    "required": false,
    "value": ""
  },
  {
    "name": "dataDt",
    "desc": "所需日期，时间格式：yyyyMMdd，可以为多个日期，每个日期“，”分割，针对于月末，年末，季末等需要提 取相关日期，对近一年，近一个季度等，需要提取相关日期，比如当前是20250424 近一个季度指的是20250330 的日期，",
    "type": "string",
    "required": true,
    "value": ""
  },
  {
    "name": "projNm",
    "desc": "项目名称，如对公一般性贷款、对公存款，营业净收入，利息净收入，非息净收入等，其他非息收入，手续 费净收入可以提取多个项目，每个“，”分割，如果没具体说哪个则根据用户意图来多个提取还是单个提取",
    "type": "string",
    "required": true,
    "value": ""
  },
  {
    "name": "curCd",
    "desc": "币种，0A 本外币折人民币，156为人民币，0B为 外币，用户input中没有提及默认为0A ",
    "type": "string",
    "required": true,
    "value": ""
  },
  {
    "name": "statsObj",
    "desc": "统计口径，1-会计口径，0-绩效口径，默认为1，会计口径，用户input中没有提及默认为1",
    "type": "string",
    "required": true,
    "value": ""
  },
  {
    "name": "metric_type",
    "desc": "查询指标的类型，如，余额、日均余额、平均利率、时点余额，余额上月环比，余额同比，余额比上月贡献 度等，也可以多个，每个“,”分割，可以模糊匹配可以提取一个，注意如果没具体说哪个如果没说指标类型或者说了多个指 标，则空着，意味着查询所有指标,比如用户查询南京分行对公一般性贷款情况，则此处空着",
    "type": "string",
    "required": false,
    "value": ""
  }
]

示例:
JSON：[{'name': 'belgOrgNm', 'desc': '机构名称', 'value': ''}]
输入：查询南京分行2024年6月的对公存款余额
答：{'name': 'belgOrgNm', 'value': '南京分行', 'name': 'dataDt', 'value': '20240630', 'name': 'projNm', 'value': '对公存款',   'name': 'metric_type', 'value': '余额'}

用户输入: 查询北京分行的对公存款余额、对公一般性贷款余额、收益率等指标（不包含具体客户数据）

请严格按照以下要求返回JSON格式的参数：
1. 必须是有效的JSON数组格式
2. 不要添加任何解释性文本
3. 不要使用Markdown代码块（如```json）
4. 确保每个参数都有name和value字段
5. 所有参数值必须使用双引号包裹
6. 直接返回JSON数组，不要有其他内容

例如：
[
 {"name": "参数1", "value": "值1"},
  {"name": "参数2", "value": "值2"}
]

new_info_json_raw: [
  {"name": "belgOrgNm", "value": "北京分行"},
  {"name": "dataDt", "value": "********"},
  {"name": "projNm", "value": "对公存款,对公一般性贷款"},
  {"name": "curCd", "value": "0A"},
  {"name": "statsObj", "value": "1"},
  {"name": "metric_type", "value": "余额,收益率"}
]
首先尝试直接解析完整的JSON数组0
input_string [
  {"name": "belgOrgNm", "value": "北京分行"},
  {"name": "dataDt", "value": "********"},
  {"name": "projNm", "value": "对公存款,对公一般性贷款"},
  {"name": "curCd", "value": "0A"},
  {"name": "statsObj", "value": "1"},
  {"name": "metric_type", "value": "余额,收益率"}
]
尝试直接解析完整的JSON数组1
成功解析完整JSON数组0
成功解析完整JSON数组1
raw_params [{'name': 'belgOrgNm', 'value': '北京分行'}, {'name': 'dataDt', 'value': '********'}, {'name': 'projNm', 'value': '对公存款,对公一般性贷款'}, {'name': 'curCd', 'value': '0A'}, {'name': 'statsObj', 'value': '1'}, {'name': 'metric_type', 'value': '余额,收益率'}]
当前工作目录: D:\llm\chat_bi_version\IntelliQ-main_0414
找到的JSON文件: []
使用绝对路径找到的JSON文件: ['d:\\llm\\chat_bi_version\\IntelliQ-main_0414\\IntelliQ-main_0523\\IntelliQ-main_0506\\IntelliQ-main_0506\\IntelliQ-main\\scene_config\\scene_templates.json', 'd:\\llm\\chat_bi_version\\IntelliQ-main_0414\\IntelliQ-main_0523\\IntelliQ-main_0506\\IntelliQ-main_0506\\IntelliQ-main\\scene_config\\conf\\scene_custom1.json']
正在加载文件: d:\llm\chat_bi_version\IntelliQ-main_0414\IntelliQ-main_0523\IntelliQ-main_0506\IntelliQ-main_0506\IntelliQ-main\scene_config\scene_templates.json
成功加载配置，包含场景: ['bank_branch_query', 'bank_segment_query', 'bank_customer_query']
添加场景: bank_branch_query
添加场景: bank_segment_query
添加场景: bank_customer_query
正在加载文件: d:\llm\chat_bi_version\IntelliQ-main_0414\IntelliQ-main_0523\IntelliQ-main_0506\IntelliQ-main_0506\IntelliQ-main\scene_config\conf\scene_custom1.json
成功加载配置，包含场景: ['userid_gongbao']
添加场景: userid_gongbao
最终加载的场景总数: 4
场景列表: ['bank_branch_query', 'bank_segment_query', 'bank_customer_query', 'userid_gongbao']
处理前值： 北京分行 处理后值： 北京分行
处理前值： 北京分行 处理后值： 北京分行
处理前值： 北京分行 处理后值： 北京分行
处理前值： 北京分行 处理后值： 北京分行
处理前值： ******** 处理后值： ********
处理前值： ******** 处理后值： ********
处理前值： ******** 处理后值： ********
处理前值： ******** 处理后值： ********
处理前值： 对公存款,对公一般性贷款 处理后值： 对公存款,对公一般性贷款
处理前值： 对公存款,对公一般性贷款 处理后值： 对公存款,对公一般性贷款
进入项目名称处理器
project:  其他证券投资
param_value:  对公存款,对公一般性贷款
project:  存放同业
param_value:  对公存款,对公一般性贷款
project:  非息净收入
param_value:  对公存款,对公一般性贷款
project:  咨询顾问业务
param_value:  对公存款,对公一般性贷款
project:  其他咨询顾问业务
param_value:  对公存款,对公一般性贷款
project:  外币对公一般性贷款
param_value:  对公存款,对公一般性贷款
project:  人民币协议存款
param_value:  对公存款,对公一般性贷款
project:  二至三年期（不含）人民币对公一般定期
param_value:  对公存款,对公一般性贷款
project:  其他负债
param_value:  对公存款,对公一般性贷款
project:  保理业务
param_value:  对公存款,对公一般性贷款
project:  国内福费廷
param_value:  对公存款,对公一般性贷款
project:  消费贷款
param_value:  对公存款,对公一般性贷款
project:  买卖价差
param_value:  对公存款,对公一般性贷款
project:  其他系统内往来存放联行
param_value:  对公存款,对公一般性贷款
project:  同业拆入
param_value:  对公存款,对公一般性贷款
project:  自营存款（不含委托贷款）
param_value:  对公存款,对公一般性贷款
project:  金融机构借款
param_value:  对公存款,对公一般性贷款
project:  存放央行
param_value:  对公存款,对公一般性贷款
project:  代销基金
param_value:  对公存款,对公一般性贷款
project:  委托贷款
param_value:  对公存款,对公一般性贷款
project:  承诺业务
param_value:  对公存款,对公一般性贷款
project:  其他非息收入
param_value:  对公存款,对公一般性贷款
project:  其他证券投资
param_value:  对公存款,对公一般性贷款
project:  人民币对公通知
param_value:  对公存款,对公一般性贷款
project:  三年期及以上人民币对公一般定期
param_value:  对公存款,对公一般性贷款
project:  金融机构活期存款
param_value:  对公存款,对公一般性贷款
project:  其他负债（含委托贷款）
param_value:  对公存款,对公一般性贷款
project:  其他应收款投资
param_value:  对公存款,对公一般性贷款
project:  手续费净收入
param_value:  对公存款,对公一般性贷款
project:  应收款项类投资
param_value:  对公存款,对公一般性贷款
project:  二至三年期（不含）个人一般定期
param_value:  对公存款,对公一般性贷款
project:  其他个贷
param_value:  对公存款,对公一般性贷款
project:  代理国债
param_value:  对公存款,对公一般性贷款
project:  其他代理销售
param_value:  对公存款,对公一般性贷款
project:  保函
param_value:  对公存款,对公一般性贷款
project:  人民币对公一般定期
param_value:  对公存款,对公一般性贷款
project:  个人担保结构性
param_value:  对公存款,对公一般性贷款
project:  票据贴现
param_value:  对公存款,对公一般性贷款
project:  以摊余成本计量债券投资
param_value:  对公存款,对公一般性贷款
project:  存放央行
param_value:  对公存款,对公一般性贷款
project:  进出口业务
param_value:  对公存款,对公一般性贷款
project:  营业净收入
param_value:  对公存款,对公一般性贷款
project:  个人存款
param_value:  对公存款,对公一般性贷款
project:  总分行转移利息净收入
param_value:  对公存款,对公一般性贷款
project:  利息补贴
param_value:  对公存款,对公一般性贷款
project:  银行承兑汇票
param_value:  对公存款,对公一般性贷款
project:  保理业务
param_value:  对公存款,对公一般性贷款
project:  境内汇款
param_value:  对公存款,对公一般性贷款
project:  公允价值变动损益金融资产
param_value:  对公存款,对公一般性贷款
project:  外币对公存款
param_value:  对公存款,对公一般性贷款
project:  人民币对公一般定期
param_value:  对公存款,对公一般性贷款
project:  对公存款
param_value:  对公存款,对公一般性贷款
score： 0.4444444444444444
project:  对公存款
param_value:  对公存款,对公一般性贷款
score:  0.4444444444444444
project:  主要负债业务
param_value:  对公存款,对公一般性贷款
project:  三年期及以上个人一般定期
param_value:  对公存款,对公一般性贷款
project:  自营贷款
param_value:  对公存款,对公一般性贷款
project:  债券总行存款准备金存放联行
param_value:  对公存款,对公一般性贷款
project:  个人贷款和垫款
param_value:  对公存款,对公一般性贷款
project:  代销信托
param_value:  对公存款,对公一般性贷款
project:  银行卡业务
param_value:  对公存款,对公一般性贷款
project:  其他支付结算业务
param_value:  对公存款,对公一般性贷款
project:  公允价值变动损益金融资产（买卖价差）
param_value:  对公存款,对公一般性贷款
project:  外汇及其他金融工具衍生交易
param_value:  对公存款,对公一般性贷款
project:  金融机构存款
param_value:  对公存款,对公一般性贷款
project:  其他资产
param_value:  对公存款,对公一般性贷款
project:  外币对公一般性贷款
param_value:  对公存款,对公一般性贷款
project:  人民币对公存款
param_value:  对公存款,对公一般性贷款
project:  人民币对公大额存单
param_value:  对公存款,对公一般性贷款
project:  个人存款
param_value:  对公存款,对公一般性贷款
project:  二至三年期（不含）个人一般定期
param_value:  对公存款,对公一般性贷款
project:  其他个贷
param_value:  对公存款,对公一般性贷款
project:  同业资产
param_value:  对公存款,对公一般性贷款
project:  承诺业务
param_value:  对公存款,对公一般性贷款
project:  咨询顾问业务
param_value:  对公存款,对公一般性贷款
project:  公允价值变动损益金融资产
param_value:  对公存款,对公一般性贷款
project:  同业资产
param_value:  对公存款,对公一般性贷款
project:  以摊余成本计量债券投资
param_value:  对公存款,对公一般性贷款
project:  存放联行
param_value:  对公存款,对公一般性贷款
project:  代销贵金属
param_value:  对公存款,对公一般性贷款
project:  人民币对公活期
param_value:  对公存款,对公一般性贷款
project:  人民币对公大额存单
param_value:  对公存款,对公一般性贷款
project:  人民币对公一般性贷款
param_value:  对公存款,对公一般性贷款
project:  外币对公定期存款
param_value:  对公存款,对公一般性贷款
project:  担保承诺业务
param_value:  对公存款,对公一般性贷款
project:  人民币对公一般性贷款
param_value:  对公存款,对公一般性贷款
project:  缴存总行存款准备金存放联行
param_value:  对公存款,对公一般性贷款
project:  债券投资
param_value:  对公存款,对公一般性贷款
project:  境内汇款
param_value:  对公存款,对公一般性贷款
project:  个人贷款
param_value:  对公存款,对公一般性贷款
project:  个人一般定期
param_value:  对公存款,对公一般性贷款
处理前值： 对公存款,对公一般性贷款 处理后值： 对公存款,对公一般性贷款
处理前值： 对公存款,对公一般性贷款 处理后值： 对公存款,对公一般性贷款
处理前值： 0A 处理后值： 0A
处理前值： 0A 处理后值： 0A
处理前值： 0A 处理后值： 0A
处理前值： 0A 处理后值： 0A
处理前值： 1 处理后值： 1
处理前值： 1 处理后值： 1
处理前值： 1 处理后值： 1
处理前值： 1 处理后值： 1
处理前值： 余额,收益率 处理后值： 余额,收益率
处理前值： 余额,收益率 处理后值： 余额,收益率
处理前值： 余额,收益率 处理后值： 余额,收益率
处理前值： 余额,收益率 处理后值： 余额,收益率









前端传给后端的参数，方案，以及后端的缓存方案

1. 用户登录后 ：
   
   - 前端调用 loadHistory() 函数
   - 向后端发送 GET 请求到 /sessions/{currentUserId} 获取该用户的所有会话
2. 获取会话列表 ：
   
   - 后端从 Redis 中获取用户的会话 ID 列表（键格式为 user_sessions:{user_id} ）
   - 对于每个会话 ID，获取完整的会话信息
   - 返回会话列表给前端
3. 查看特定会话 ：
   
   - 用户点击历史记录中的会话
   - 前端向后端发送 GET 请求到 /sessions/{sessionId} 获取该会话的详细信息
   - 后端通过 get_session 方法获取会话信息并返回
4. 会话存储层次 ：
   
   - 内存缓存（最快）
   - 内存字典
   - Redis 缓存
   - 文件存储（最慢）




redis_service mysql_service,
用户数据保存，
支持表格、折线图等图表展示;支持下钻&前推机构图表生成;支持根据指标类型推导，生成近期指标趋势图



日志处理
绩效相关schema 与aaas字段处理 准备与api测试，大模型对绩效相关问题的识别的准确性测试，大模型对查询的帮助输出，如何查询的支行机构id快速匹配的设计，指引构建
redis和mysql 的存储
客户数据准备，日志数据
redis的token 验证
历史聊天记录，
服务监控，
重新上线后对历史记录的加载

查询排名的分析与图表输出与前端的适应
前端的图表支持
将从用户输入每一步骤的大模型输出到最终
输出结果的完成情况进行记录，
，为后续分析
问题做准备
工作流步骤流程记录，日志存储
用户信息识别与机构信息匹配查询、权限识别
中


支持表格、折线图等图表展示;支持下钻&前推机构图表生成;支持根据指标类型推导，生成近期指标趋势图
产品优化：
支持用户点踩、点赞，问题反馈收集等功能;
指标权限管理，确保每个用户根据账号确定其指标使用,只能访问自己权限的指标，不能查看其他指标，UM 鉴权
范围，保障数据安全

数据获取：api



先以机构名为主，如果没有分行的名称，则用行名称，如果行名称没有，则用机构名称。匹配的一级分行的匹配，如果匹配不到，则用机构名称。
其他表的测试，条线，客户数据准备
权限控制 
metirc的模糊查询 √
python 环境测试 √



（1）项目名称的模糊查询 do
（2）本外币查询 
（3）口径查询
如果没数据，为空

资产文档


 不同表的转化情况，梳理有哪些表和字段，
需求标准化
统计筛选、问题、结果
统计 where 条件  ，实体问题， 输出 
通过匹配需求案例和行业知识，对需求进行整理和改写，改成标准的需求格式
查询行业知识，获取使用表 ，
分析思路拆解 

模糊匹配 Fuzzy Matching 多表识别 Multi-Table Recognition 意图识别 Intent Recognition 词槽填充 Slot Filling 参数提取 Parameter Extraction 场景自动识别 Automatic Scene Recognition 数据源适配 Data Source Adaptation 缓存机制 Caching Mechanism 维度和指标区分 Dimension and Metric Differentiation 上下文增强 Context Enhancement 多轮对话管理 Multi-turn Dialogue Management 参数映射配置 Parameter Mapping Configuration




 对于多问题的处理，

 查询多个参数的配置和提取
 1、参数的提取，
 2、参数的转化，
 3、参数的校验，
 "queryParams": {
        "dataDt": "20240930",
        "belgOrgId": "703220",
        "dt": "20240930",
        "paOrgNm": "南京城南支行",
        "bigNm": "公司业务",
        "coreProdNm": "单位人民币结算账户存款"
      }

测试样例完成
问题: 哪个分行的对公一般性贷款余额上个月环比下降最多？
查询对公一般性贷款余额排名在第5到第10位之间的分行。
零售存款余额年度同比增长率最低的分行是哪个？

## 安装和使用

确保您已安装python3。然后执行以下步骤：
```
# 安装步骤

pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 修改配置
配置项在 config/__init__.py
GPT_URL: 可修改为OpenAI的代理地址
API_KEY: 修改ApiKey

# 启动
python app.py

# 可视化调试可以浏览器打开 demo/user_input.html 或 127.0.0.1:5000
```

## 文档

查阅详细的API文档和使用说明，请访问 [文档链接]。

