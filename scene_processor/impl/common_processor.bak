# encoding=utf-8
import json
import logging

from scene_config import scene_prompts
from scene_processor.scene_processor import SceneProcessor
from utils.helpers import get_raw_slot, update_slot, format_name_value_for_logging, is_slot_fully_filled, send_message, \
    extract_json_from_string, get_dynamic_example
from utils.prompt_utils import get_slot_update_message, get_slot_query_user_message
from utils.data_api import query_data_by_scene  # 添加导入

logger = logging.getLogger(__name__)  # 添加logger定义

class CommonProcessor(SceneProcessor):
    def __init__(self, scene_config):
        parameters = scene_config["parameters"]
        self.scene_config = scene_config
        self.scene_name = scene_config["name"]
        self.slot_template = get_raw_slot(parameters)
        self.slot_dynamic_example = get_dynamic_example(scene_config)
        self.slot = get_raw_slot(parameters)
        self.scene_prompts = scene_prompts
        self.api_result = None
    
    def set_api_result(self, result):
        """设置API查询结果"""
        logger.info(f"设置API查询结果: {result.get('status', 'unknown')}")
        self.api_result = result
    
    def get_params(self):
        """获取当前参数值"""
        params = {}
        for param in self.parameters:
            if "value" in param and param["value"]:
                params[param["name"]] = param["value"]
        logger.info(f"获取参数: {params}")
        return params
    
    # 修改现有的process方法，添加API结果处理
    def process(self, user_input, context=None):
        # 获取场景配置
        scene_config = self.scene_config
        
        # 从上下文中获取历史对话
        history = context.get("history", "")
        
        # 从上下文中获取当前日期
        current_date = context.get("current_date", "")
        
        # 获取表信息
        table_info = self.get_table_info(scene_config)
        print("table_info:", table_info)    
        
        # 提取参数
        try:
            # 处理用户输入，更新槽位，检查完整性，以及与用户交互
            # 在消息中加入历史对话和当前日期信息
            message = get_slot_update_message(
                self.scene_name, 
                self.slot_dynamic_example, 
                self.slot_template, 
                user_input,
                history=history,
                current_date=current_date
            )
            print("message:", message)
            
            new_info_json_raw = send_message(message, user_input)
            if not new_info_json_raw:
                logging.error("Failed to get response from LLM")
                return "抱歉，我暂时无法处理您的请求，请稍后再试。"
            
            # 提取并转换参数
            current_values = extract_json_from_string(new_info_json_raw, self.scene_name)
            
            if not current_values:
                logging.warning("No valid parameters extracted from: %s", new_info_json_raw)
                return "我没能理解您提供的信息，请尝试更清晰地表述。"
            
            logging.debug('current_values: %s', current_values)
            logging.debug('slot update before: %s', self.slot)
            update_slot(current_values, self.slot)
            logging.debug('slot update after: %s', self.slot)
            
            # 判断参数是否已经全部补全
            if is_slot_fully_filled(self.slot):
                print("参数已完整")
                return self.respond_with_complete_data()
            else:
                print("参数不完整")
                return self.ask_user_for_missing_data(user_input)
        except Exception as e:
            logging.exception("Error processing user input: %s", e)
            return "处理您的请求时出现了问题，请稍后再试。"

    def respond_with_complete_data(self):
        # 当所有数据都准备好后的响应
        logging.debug(f'%s ------ 参数已完整，详细参数如下', self.scene_name)
        logging.debug(format_name_value_for_logging(self.slot))
        logging.debug(f'正在请求%sAPI，请稍后……', self.scene_name)
        
        # 创建参数字典
        params = {}
        for item in self.slot:
            params[item["name"]] = item["value"]
        
        # 调用API获取数据
        try:
            logger.info(f"准备调用API，场景: {self.scene_name}, 参数: {params}")
            api_result = query_data_by_scene(self.scene_name, self.scene_config, params)
            logger.info(f"API调用结果: {api_result.get('status', 'unknown')}")
            
            # 保存API结果
            self.api_result = api_result
            
            # 如果API调用成功，使用大模型生成回答
            if api_result.get("status") == "success" and "data" in api_result:
                prompt = self.generate_result_prompt(params, api_result)
                response = send_message(prompt, None)
                return response
            elif api_result.get("error"):
                return f"抱歉，查询数据时出现错误: {api_result.get('error')}"
            else:
                # 返回格式化的JSON字符串
                return json.dumps(params, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"API调用过程中发生错误: {str(e)}")
            return f"抱歉，在处理您的请求时发生了错误: {str(e)}"
    
    def ask_user_for_missing_data(self, user_input):
        message = get_slot_query_user_message(self.scene_name, self.slot, user_input)
        # 请求用户填写缺失的数据
        result = send_message(message, user_input)
        return result

    def get_table_info(self, scene_config):
        """获取场景对应的表信息"""
        import json
        import os
        
        table_name = scene_config.get("table_name", "")
        if not table_name:
            return None
            
        try:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            tables_file = os.path.join(base_dir, "config", "tables.json")
            
            with open(tables_file, 'r', encoding='utf-8') as file:
                tables_data = json.load(file)
                
            for table in tables_data.get("tables", []):
                if table.get("table_name") == table_name:
                    return table
                    
            return None
        except Exception as e:
            print(f"获取表信息出错: {e}")
            return None
    
    def generate_result_prompt(self, user_input, api_result):
        """生成处理API结果的提示词"""
        prompt = f"""你是一个专业的银行数据分析助手。
        
用户问题: {user_input}

我已经查询到以下数据:
{json.dumps(api_result, ensure_ascii=False, indent=2)}

注意：
1、请根据这些数据，用专业、简洁的语言回答用户的问题。
2、如果数据中包含多个项目或日期，请进行适当的对比分析。
3、数字格式化要求：
   - 保留两位小数
   - 使用千位分隔符（例如：2,115.03）
   - 不要使用加粗等特殊标记
   - 涉及金额时需要明确单位（如：亿元、万元等）
"""
        logger.debug(f"生成的提示词: {prompt}")
        return prompt
