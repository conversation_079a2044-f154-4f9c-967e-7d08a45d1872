# encoding=utf-8
import json
import logging
import asyncio
import re
import pandas as pd
# 移除Plotly导入，改用ECharts
# import plotly
# import plotly.express as px
# import plotly.graph_objects as go

from scene_config import scene_prompts
from scene_processor.scene_processor import SceneProcessor
from utils.helpers import get_raw_slot, update_slot, format_name_value_for_logging, is_slot_fully_filled, send_message, \
    extract_json_from_string, get_dynamic_example
from utils.prompt_utils import get_slot_update_message, get_slot_query_user_message
from utils.data_api import query_data_by_scene  # 添加导入
# 修正导入路径
from services.llm_service import get_streaming_llm_response, send_llm_request

logger = logging.getLogger(__name__)  # 添加logger定义

class CommonProcessor(SceneProcessor):
    def __init__(self, scene_config):
        parameters = scene_config["parameters"]
        self.scene_config = scene_config
        self.scene_name = scene_config["name"]
        self.slot_template = get_raw_slot(parameters)
        self.slot_dynamic_example = get_dynamic_example(scene_config)
        self.slot = get_raw_slot(parameters)
        self.scene_prompts = scene_prompts
        self.api_result = None
        self.visualization = None

    def set_api_result(self, result):
        """设置API查询结果"""
        logger.info(f"设置API查询结果: {result.get('status', 'unknown')}")
        self.api_result = result
        # 如果API结果成功，尝试生成可视化
        if result.get("status") == "success" and "data" in result:
            self.generate_visualization(result)

    def get_params(self):
        """获取当前参数值"""
        params = {}
        for param in self.parameters:
            if "value" in param and param["value"]:
                params[param["name"]] = param["value"]
        logger.info(f"获取参数: {params}")
        return params
    
    async def extract_params_for_permission_check(self, user_input, context=None):
        """为权限验证提取查询参数
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            dict: 包含belgOrgNm和dataDt等关键参数的字典
        """
        try:
            # 从上下文中获取历史对话和当前日期
            history = context.get("history", "") if context else ""
            current_date = context.get("current_date", "") if context else ""
            
            # 构建参数提取消息
            message = get_slot_update_message(
                self.scene_name,
                self.slot_dynamic_example,
                self.slot_template,
                user_input,
                history=history,
                current_date=current_date
            )
            
            # 调用LLM提取参数
            new_info_json_raw = await send_message(message, user_input, is_query=True)
            
            if not new_info_json_raw:
                logger.warning("权限验证参数提取失败：LLM无响应")
                return {}
            
            # 提取并转换参数
            extracted_params = extract_json_from_string(new_info_json_raw, self.scene_name)
            
            if not extracted_params:
                logger.warning(f"权限验证参数提取失败：无法解析JSON - {new_info_json_raw}")
                return {}
            
            # 只返回权限验证需要的关键参数
            permission_params = {}
            for param in extracted_params:
                if param.get("name") == "belgOrgNm" and param.get("value"):
                    permission_params["belgOrgNm"] = param["value"]
                elif param.get("name") == "dataDt" and param.get("value"):
                    permission_params["dataDt"] = param["value"]
            
            logger.info(f"权限验证参数提取成功: {permission_params}")
            print("permission_params:", permission_params)
            return permission_params
            
        except Exception as e:
            logger.error(f"权限验证参数提取异常: {str(e)}")
            return {}

    # 修改现有的process方法，添加API结果处理
    # 将process方法改为返回异步迭代器
    async def process(self, user_input, context=None):
        # 获取场景配置
        scene_config = self.scene_config

        # 从上下文中获取历史对话
        history = context.get("history", "")

        # 从上下文中获取当前日期
        current_date = context.get("current_date", "")

        # 获取表信息
        table_info = self.get_table_info(scene_config)
        print("table_info:", table_info)

        # 提取参数
        try:
            # 处理用户输入，更新槽位，检查完整性，以及与用户交互
            # 在消息中加入历史对话和当前日期信息
            message = get_slot_update_message(
                self.scene_name,
                self.slot_dynamic_example,
                self.slot_template,
                user_input,
                history=history,
                current_date=current_date
            )
            print("message:", message)

            new_info_json_raw = await send_message(message, user_input,is_query=True)

            print("new_info_json_raw:", new_info_json_raw)
            if not new_info_json_raw:
                logging.error("Failed to get response from LLM")
                yield "抱歉，我暂时无法处理您的请求，请稍后再试。"
                return

            # 提取并转换参数
            current_values = extract_json_from_string(new_info_json_raw, self.scene_name)

            if not current_values:
                logging.warning("No valid parameters extracted from: %s", new_info_json_raw)
                yield "我没能理解您提供的信息，请尝试更清晰地表述。"
                return

            logging.debug('current_values: %s', current_values)
            logging.debug('slot update before: %s', self.slot)
            update_slot(current_values, self.slot)
            logging.debug('slot update after: %s', self.slot)

            # 判断参数是否已经全部补全
            if is_slot_fully_filled(self.slot):
                print("参数已完整")
                # 使用异步生成器返回结果，传递用户原始问题
                async for chunk in self.respond_with_complete_data_stream(user_input):
                    yield chunk
            else:
                print("参数不完整")
                response = await self.ask_user_for_missing_data(user_input)
                yield response
        except Exception as e:
            logging.exception("Error processing user input: %s", e)
            yield f"处理您的请求时出现了问题: {str(e)}，请稍后再试。"

    # 添加新方法，支持流式返回完整数据的响应
    async def respond_with_complete_data_stream(self, user_input=None):
        print("API请求中1。。")
        # 当所有数据都准备好后的响应
        logging.debug(f'%s ------ 参数已完整，详细参数如下', self.scene_name)
        logging.debug(format_name_value_for_logging(self.slot))
        logging.debug(f'正在请求%sAPI，请稍后……', self.scene_name)

        # 创建参数字典
        params = {}
        for item in self.slot:
            params[item["name"]] = item["value"]
        print("API请求中2。。")

        

        # 调用API获取数据
        try:
            logger.info(f"准备调用API，场景: {self.scene_name}, 参数: {params}")
            # 传递user_input作为user_question参数
            api_result = query_data_by_scene(self.scene_name, self.scene_config, params, user_input)
            print("api_result121",api_result)
            
            logger.info(f"API调用结果: {api_result.get('status', 'unknown')}")

            # 保存API结果
            self.api_result = api_result

            # 如果API调用成功，使用大模型生成回答
            if api_result.get("status") == "success" and "data" in api_result:
                # 修正：正确提取数据列表
                data_list = api_result.get("data", [])
                
                # 处理可能的嵌套结构
                if isinstance(data_list, dict) and "data" in data_list:
                    actual_data = data_list["data"]
                else:
                    actual_data = data_list
                    
                # 获取数据行数
                data_rows = len(actual_data) if isinstance(actual_data, list) else 0
                logger.info(f"API返回数据行数: {data_rows}")
                print("actual_data:", actual_data)
                
                # 尝试生成可视化
                has_visualization = False
                if data_rows >= 4:
                    # 尝试生成可视化，传递用户问题
                    self.generate_visualization(api_result, question=user_input)
                    has_visualization = self.visualization is not None
                    logger.info(f"可视化生成状态: {has_visualization}")

                # 使用用户原始问题或参数字典作为提示词的一部分
                user_question = user_input if user_input else str(params)
                
                # 生成文字说明（包含表格）
                prompt = self.generate_result_prompt(user_question, api_result, data_rows, has_visualization)
                async for chunk in self.stream_llm_response(prompt):
                    yield chunk
                
                # 修正表格展示逻辑
                markdown_table = api_result.get("markdown_table")
                if markdown_table and data_rows >= 4:
                    logger.info("输出HTML表格")
                    yield "\n\n以下是数据的表格展示：\n"
                    html_table = self._format_html_table(actual_data)  # 使用正确的数据
                    yield f"<div class='table-container'>{html_table}</div>\n"
                
                # 修正可视化逻辑中的数据传递
                if data_rows >= 4 and self.visualization:
                    try:
                        logger.info("执行可视化代码")
                        print("actual_data for visualization:", actual_data)
                        # 使用ECharts生成图表
                        chart_result = self.execute_visualization_code(self.visualization, actual_data)  # 使用正确的数据
                        if chart_result:
                            logger.info("输出图表")
                            yield "\n\n以下是数据的可视化展示：\n"
                            yield f"<div class='chart-container'>{chart_result}</div>"
                    except Exception as e:
                        logger.error(f"执行可视化代码失败: {e}")
            elif api_result.get("error"):
                yield f"抱歉，查询数据时出现错误: {api_result.get('error')}"
            else:
                yield json.dumps(params, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"API调用过程中发生错误: {str(e)}")
            yield f"抱歉，在处理您的请求时发生了错误: {str(e)}"
    
    def _format_html_table(self, data):
        """将数据格式化为HTML表格"""
        if not data or not isinstance(data, list) or len(data) == 0:
            return ""
        
        # 获取所有列名
        columns = list(data[0].keys())
        
        # 创建HTML表格
        html = "<table>\n"
        
        # 添加表头
        html += "<thead>\n<tr>\n"
        for col in columns:
            html += f"<th>{col}</th>\n"
        html += "</tr>\n</thead>\n"
        
        # 添加表格内容
        html += "<tbody>\n"
        for row in data:
            html += "<tr>\n"
            for col in columns:
                html += f"<td>{row.get(col, '')}</td>\n"
            html += "</tr>\n"
        html += "</tbody>\n"
        
        # 关闭表格标签
        html += "</table>"
        
        return html

    # 修改 stream_llm_response 方法，确保它正确实现为异步生成器
    async def stream_llm_response(self, prompt):
        try:
            print("111")
            # 不要直接返回函数调用结果，而是使用 async for 迭代它并 yield 结果
            async for chunk in get_streaming_llm_response(prompt):
                yield chunk
        except Exception as e:
            logger.error(f"流式生成回复时出错: {str(e)}")
            yield f"生成回复时出现错误: {str(e)}"

    # 将ask_user_for_missing_data方法修改为异步方法
    async def ask_user_for_missing_data(self, user_input):
        message = get_slot_query_user_message(self.scene_name, self.slot, user_input)
        # 请求用户填写缺失的数据
        result = await send_message(message, user_input)
        return result

    # stream_llm_response方法已经是异步的，保持不变

    def should_generate_chart(self, df):
        """
        检查是否应该为给定的DataFrame生成图表
        参考Vanna的实现，检查DataFrame是否有多行数据和数值列

        Args:
            df (pd.DataFrame): 要检查的DataFrame

        Returns:
            bool: 如果应该生成图表返回True，否则返回False
        """
        if df is None or df.empty:
            return False

        # 检查是否有多行数据和数值列
        if len(df) > 1 and df.select_dtypes(include=['number']).shape[1] > 0:
            return True

        return False

    def generate_visualization(self, api_result, question=None):
        """
        生成数据可视化代码
        优化版本，参考Vanna的实现方式

        Args:
            api_result: API查询结果
            question: 用户问题（可选）

        Returns:
            str: 生成的ECharts配置，如果失败返回None
        """
        try:
            # 检查API结果是否包含数据
            if not api_result or not api_result.get("data"):
                logger.warning("API结果中没有数据，无法生成可视化")
                return None

            # 尝试将API结果转换为DataFrame
            data = api_result.get("data")
            if isinstance(data, list) and len(data) > 0:
                try:
                    df = pd.DataFrame(data)
                    logger.info(f"DataFrame创建成功，形状: {df.shape}")

                    # 检查是否应该生成图表
                    if not self.should_generate_chart(df):
                        logger.info("数据不适合生成图表")
                        return None

                    # 获取更详细的DataFrame元数据信息（参考Vanna的方式）
                    df_metadata = f"Running df.dtypes gives:\n{df.dtypes}"

                    # 构建系统消息（参考Vanna的prompt结构）
                    if question is not None:
                        system_msg = f"The following is a pandas DataFrame that contains the results of the query that answers the question the user asked: '{question}'"
                    else:
                        system_msg = "The following is a pandas DataFrame"

                    system_msg += f"\n\nThe following is information about the resulting pandas DataFrame 'df': \n{df_metadata}"

                    # 构建用户消息（让LLM生成ECharts配置）
                    user_msg = ("Can you generate an ECharts configuration (JSON format) to chart the results of the dataframe? "
                              "Assume the data is in a pandas dataframe called 'df'. "
                              "Analyze the data structure and user question to choose the most appropriate chart type: "
                              "- Use bar charts for comparing categories, institutions, or discrete values "
                              "- Use line charts for time series, trends, or continuous data over time "
                              "- Use pie charts for showing proportions, percentages, or parts of a whole "
                              "- Use scatter plots for relationships between two continuous variables "
                              "- Use gauge charts if there is only one value to display "
                              "Return a complete ECharts option configuration in JSON format. "
                              "Include proper titles, axis labels, and styling. "
                              "Choose the chart type that best answers the user's question and visualizes the data effectively. "
                              "Respond with only the JSON configuration. Do not answer with any explanations -- just the JSON.")

                    # 发送请求获取ECharts配置
                    echarts_response = send_llm_request(system_msg, user_msg)

                    # 提取和清理ECharts配置
                    self.visualization = self._extract_echarts_config(echarts_response)

                    logger.info("成功生成可视化代码")
                    return self.visualization

                except Exception as e:
                    logger.error(f"转换数据为DataFrame时出错: {str(e)}")
                    return None
            else:
                logger.warning("API结果数据格式不适合可视化")
                return None

        except Exception as e:
            logger.error(f"生成可视化时出错: {str(e)}")
            return None

    def _extract_echarts_config(self, response_string):
        """从响应字符串中提取ECharts配置"""
        if not response_string:
            return ""

        # 尝试提取JSON代码块
        json_pattern = r"```[\w\s]*json\n([\s\S]*?)```|```([\s\S]*?)```"
        matches = re.findall(json_pattern, response_string, re.IGNORECASE)

        if matches:
            for match in matches:
                json_content = match[0] if match[0] else match[1]
                json_content = json_content.strip()
                try:
                    # 验证是否为有效JSON
                    json.loads(json_content)
                    return json_content
                except json.JSONDecodeError:
                    continue

        # 如果没有找到代码块，尝试直接解析整个响应
        try:
            # 查找可能的JSON对象
            start_idx = response_string.find('{')
            if start_idx != -1:
                # 找到最后一个}
                end_idx = response_string.rfind('}')
                if end_idx != -1 and end_idx > start_idx:
                    json_content = response_string[start_idx:end_idx+1]
                    json.loads(json_content)  # 验证JSON
                    return json_content
        except json.JSONDecodeError:
            pass

        logger.warning("无法从响应中提取有效的ECharts配置")
        return ""

    def _generate_fallback_echarts_config(self, df, chart_type="bar"):
        """
        生成回退的ECharts配置
        当LLM生成失败时使用
        """
        try:
            numeric_cols = df.select_dtypes(include=["number"]).columns.tolist()
            categorical_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()

            # 基础颜色配置
            colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4']

            if chart_type == "bar" and len(numeric_cols) >= 1 and len(categorical_cols) >= 1:
                # 柱状图配置
                categories = df[categorical_cols[0]].tolist()
                values = df[numeric_cols[0]].tolist()

                config = {
                    "title": {
                        "text": f"{categorical_cols[0]} vs {numeric_cols[0]}",
                        "left": "center"
                    },
                    "tooltip": {
                        "trigger": "axis"
                    },
                    "xAxis": {
                        "type": "category",
                        "data": categories,
                        "name": categorical_cols[0]
                    },
                    "yAxis": {
                        "type": "value",
                        "name": numeric_cols[0]
                    },
                    "series": [{
                        "name": numeric_cols[0],
                        "type": "bar",
                        "data": values,
                        "itemStyle": {
                            "color": colors[0]
                        }
                    }],
                    "color": colors
                }

            elif chart_type == "pie" and len(categorical_cols) >= 1:
                # 饼图配置
                if len(numeric_cols) >= 1:
                    data = [{"name": cat, "value": val} for cat, val in
                           zip(df[categorical_cols[0]], df[numeric_cols[0]])]
                else:
                    # 如果没有数值列，使用计数
                    value_counts = df[categorical_cols[0]].value_counts()
                    data = [{"name": name, "value": count} for name, count in value_counts.items()]

                config = {
                    "title": {
                        "text": f"{categorical_cols[0]}分布",
                        "left": "center"
                    },
                    "tooltip": {
                        "trigger": "item"
                    },
                    "series": [{
                        "name": categorical_cols[0],
                        "type": "pie",
                        "radius": "50%",
                        "data": data,
                        "emphasis": {
                            "itemStyle": {
                                "shadowBlur": 10,
                                "shadowOffsetX": 0,
                                "shadowColor": "rgba(0, 0, 0, 0.5)"
                            }
                        }
                    }],
                    "color": colors
                }

            else:
                # 默认折线图
                if len(numeric_cols) >= 1:
                    x_data = list(range(len(df)))
                    y_data = df[numeric_cols[0]].tolist()

                    config = {
                        "title": {
                            "text": f"{numeric_cols[0]}趋势",
                            "left": "center"
                        },
                        "tooltip": {
                            "trigger": "axis"
                        },
                        "xAxis": {
                            "type": "category",
                            "data": x_data
                        },
                        "yAxis": {
                            "type": "value",
                            "name": numeric_cols[0]
                        },
                        "series": [{
                            "name": numeric_cols[0],
                            "type": "line",
                            "data": y_data,
                            "itemStyle": {
                                "color": colors[0]
                            }
                        }],
                        "color": colors
                    }
                else:
                    return ""

            return json.dumps(config, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"生成回退ECharts配置失败: {e}")
            return ""



    def get_echarts_config(self, echarts_config_str, df):
        """
        获取ECharts配置
        包含智能回退机制

        Args:
            echarts_config_str (str): ECharts配置字符串
            df (pd.DataFrame): 数据DataFrame

        Returns:
            str: ECharts配置JSON字符串，失败时返回回退配置
        """
        if not echarts_config_str:
            logger.warning("ECharts配置为空，使用回退配置")
            return self._generate_fallback_echarts_config(df)

        try:
            # 验证配置是否为有效JSON
            config = json.loads(echarts_config_str)
            return echarts_config_str
        except json.JSONDecodeError as e:
            logger.warning(f"ECharts配置JSON解析失败: {e}，使用回退配置")
            # 智能回退逻辑
            numeric_cols = df.select_dtypes(include=["number"]).columns.tolist()
            categorical_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()

            # 根据数据类型智能选择图表类型
            if len(numeric_cols) >= 2:
                # 散点图（暂时用柱状图代替）
                return self._generate_fallback_echarts_config(df, "bar")
            elif len(numeric_cols) == 1 and len(categorical_cols) >= 1:
                # 柱状图（最常见的对比场景）
                return self._generate_fallback_echarts_config(df, "bar")
            elif len(categorical_cols) >= 1 and df[categorical_cols[0]].nunique() < 10:
                # 饼图
                return self._generate_fallback_echarts_config(df, "pie")
            else:
                # 默认折线图
                return self._generate_fallback_echarts_config(df, "line")

    def execute_visualization_code(self, echarts_config_str, data):
        """
        执行ECharts可视化并返回配置JSON
        优化版本，包含更好的错误处理和回退机制

        Args:
            echarts_config_str (str): ECharts配置字符串
            data: 数据（列表格式）

        Returns:
            str: ECharts配置JSON字符串，失败时返回None
        """
        try:
            # 将数据转换为DataFrame
            if isinstance(data, list) and len(data) > 0:
                df = pd.DataFrame(data)
            else:
                logger.warning("数据格式不支持可视化")
                return None

            # 使用优化的ECharts配置生成方法
            final_config = self.get_echarts_config(echarts_config_str, df)

            if not final_config:
                logger.error("无法生成ECharts配置")
                return None

            # 包装配置为完整的JSON对象
            wrapped_config = {
                "option": json.loads(final_config)
            }

            return json.dumps(wrapped_config, ensure_ascii=False)

        except Exception as e:
            logger.error(f"执行ECharts可视化失败: {e}")
            return None

    def get_table_info(self, scene_config):
        """获取场景对应的表信息"""
        import json
        import os

        table_name = scene_config.get("table_name", "")
        if not table_name:
            return None

        try:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            tables_file = os.path.join(base_dir, "config", "tables.json")

            with open(tables_file, 'r', encoding='utf-8') as file:
                tables_data = json.load(file)

            for table in tables_data.get("tables", []):
                if table.get("table_name") == table_name:
                    return table

            return None
        except Exception as e:
            print(f"获取表信息出错: {e}")
            return None

    def generate_result_prompt(self, user_input, api_result, data_rows=0, has_visualization=False):
        """生成处理API结果的提示词"""
        # 检查是否有markdown表格表示
        markdown_table = api_result.get("markdown_table")
        print("markdown_table", markdown_table)
        if markdown_table:
            data_representation = f"以下是查询结果的表格形式:\n\n{markdown_table}"
        else:
            # 如果没有markdown表格，则使用JSON格式
            data_representation = f"以下是查询结果:\n{json.dumps(api_result, ensure_ascii=False, indent=2)}"

        # 根据数据行数和是否有可视化调整提示词
        visualization_hint = ""
        if data_rows >= 2 and has_visualization:
            visualization_hint = "\n5、在回答后，系统将自动生成数据可视化图表，你无需描述或生成图表代码。"
        elif data_rows == 1:
            visualization_hint = "\n5、由于数据只有一行，请结合用户user_input，简单回答。"

        prompt = f"""你是一个专业的银行数据分析助手。

    用户问题: {user_input}

    {data_representation}

    注意：
    1、请根据这些数据，用专业、简洁的语言回答用户的问题。
    2、如果数据中包含多个项目或日期，请进行适当的对比分析。
    3、输出格式化要求：
       - 保留两位小数
       - 使用千位分隔符（例如：2,115.03）
       - 最后输出内容不要使用加粗，* 等特殊标记
       - 涉及金额时需要明确单位（如：亿元、万元、元等）
    4、如果数据为空则输出 "暂时没有查到相关指标数据"。{visualization_hint}
    """

        return prompt

    def generate_summary(self, question, df):
        """
        生成数据摘要
        参考Vanna的实现，为查询结果生成简洁的摘要

        Args:
            question (str): 用户问题
            df (pd.DataFrame): 查询结果DataFrame

        Returns:
            str: 数据摘要
        """
        try:
            if df is None or df.empty:
                return "查询结果为空"

            system_msg = f"你是一个专业的银行数据分析助手。用户问题: '{question}'\n\n以下是查询结果的pandas DataFrame:\n{df.to_markdown()}\n\n"
            user_msg = "请基于用户问题简要总结数据。不要提供超出摘要范围的额外解释。"

            summary = send_llm_request(system_msg, user_msg)
            return summary

        except Exception as e:
            logger.error(f"生成数据摘要失败: {e}")
            return "无法生成数据摘要"

    def generate_followup_questions(self, question, api_result, n_questions=3):
        """
        生成后续问题
        参考Vanna的实现，基于当前查询结果生成相关的后续问题

        Args:
            question (str): 原始问题
            api_result: API查询结果
            n_questions (int): 生成问题数量

        Returns:
            list: 后续问题列表
        """
        try:
            if not api_result or not api_result.get("data"):
                return []

            data = api_result.get("data")
            if isinstance(data, list) and len(data) > 0:
                df = pd.DataFrame(data)

                system_msg = f"基于用户问题: '{question}' 和以下查询结果:\n{df.to_markdown()}\n\n"
                user_msg = f"请生成{n_questions}个相关的后续问题，这些问题应该是用户可能想要进一步了解的银行数据分析问题。每个问题占一行，不要编号。"

                response = send_llm_request(system_msg, user_msg)

                # 清理响应，移除编号
                import re
                numbers_removed = re.sub(r"^\d+\.\s*", "", response, flags=re.MULTILINE)
                questions = [q.strip() for q in numbers_removed.split("\n") if q.strip()]

                return questions[:n_questions]

        except Exception as e:
            logger.error(f"生成后续问题失败: {e}")
            return []
