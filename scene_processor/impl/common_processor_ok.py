# encoding=utf-8
import json
import logging
import asyncio
import re
import pandas as pd
import plotly
import plotly.express as px
import plotly.graph_objects as go

from scene_config import scene_prompts
from scene_processor.scene_processor import SceneProcessor
from utils.helpers import get_raw_slot, update_slot, format_name_value_for_logging, is_slot_fully_filled, send_message, \
    extract_json_from_string, get_dynamic_example
from utils.prompt_utils import get_slot_update_message, get_slot_query_user_message
from utils.data_api import query_data_by_scene  # 添加导入
# 修正导入路径
from services.llm_service import get_streaming_llm_response, send_llm_request

logger = logging.getLogger(__name__)  # 添加logger定义

class CommonProcessor(SceneProcessor):
    def __init__(self, scene_config):
        parameters = scene_config["parameters"]
        self.scene_config = scene_config
        self.scene_name = scene_config["name"]
        self.slot_template = get_raw_slot(parameters)
        self.slot_dynamic_example = get_dynamic_example(scene_config)
        self.slot = get_raw_slot(parameters)
        self.scene_prompts = scene_prompts
        self.api_result = None
        self.visualization = None

    def set_api_result(self, result):
        """设置API查询结果"""
        logger.info(f"设置API查询结果: {result.get('status', 'unknown')}")
        self.api_result = result
        # 如果API结果成功，尝试生成可视化
        if result.get("status") == "success" and "data" in result:
            self.generate_visualization(result)

    def get_params(self):
        """获取当前参数值"""
        params = {}
        for param in self.parameters:
            if "value" in param and param["value"]:
                params[param["name"]] = param["value"]
        logger.info(f"获取参数: {params}")
        return params
    
    async def extract_params_for_permission_check(self, user_input, context=None):
        """为权限验证提取查询参数
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            dict: 包含belgOrgNm和dataDt等关键参数的字典
        """
        try:
            # 从上下文中获取历史对话和当前日期
            history = context.get("history", "") if context else ""
            current_date = context.get("current_date", "") if context else ""
            
            # 构建参数提取消息
            message = get_slot_update_message(
                self.scene_name,
                self.slot_dynamic_example,
                self.slot_template,
                user_input,
                history=history,
                current_date=current_date
            )
            
            # 调用LLM提取参数
            new_info_json_raw = await send_message(message, user_input, is_query=True)
            
            if not new_info_json_raw:
                logger.warning("权限验证参数提取失败：LLM无响应")
                return {}
            
            # 提取并转换参数
            extracted_params = extract_json_from_string(new_info_json_raw, self.scene_name)
            
            if not extracted_params:
                logger.warning(f"权限验证参数提取失败：无法解析JSON - {new_info_json_raw}")
                return {}
            
            # 只返回权限验证需要的关键参数
            permission_params = {}
            for param in extracted_params:
                if param.get("name") == "belgOrgNm" and param.get("value"):
                    permission_params["belgOrgNm"] = param["value"]
                elif param.get("name") == "dataDt" and param.get("value"):
                    permission_params["dataDt"] = param["value"]
            
            logger.info(f"权限验证参数提取成功: {permission_params}")
            print("permission_params:", permission_params)
            return permission_params
            
        except Exception as e:
            logger.error(f"权限验证参数提取异常: {str(e)}")
            return {}

    # 修改现有的process方法，添加API结果处理
    # 将process方法改为返回异步迭代器
    async def process(self, user_input, context=None):
        # 获取场景配置
        scene_config = self.scene_config

        # 从上下文中获取历史对话
        history = context.get("history", "")

        # 从上下文中获取当前日期
        current_date = context.get("current_date", "")

        # 获取表信息
        table_info = self.get_table_info(scene_config)
        print("table_info:", table_info)

        # 提取参数
        try:
            # 处理用户输入，更新槽位，检查完整性，以及与用户交互
            # 在消息中加入历史对话和当前日期信息
            message = get_slot_update_message(
                self.scene_name,
                self.slot_dynamic_example,
                self.slot_template,
                user_input,
                history=history,
                current_date=current_date
            )
            print("message:", message)

            new_info_json_raw = await send_message(message, user_input,is_query=True)

            print("new_info_json_raw:", new_info_json_raw)
            if not new_info_json_raw:
                logging.error("Failed to get response from LLM")
                yield "抱歉，我暂时无法处理您的请求，请稍后再试。"
                return

            # 提取并转换参数
            current_values = extract_json_from_string(new_info_json_raw, self.scene_name)

            if not current_values:
                logging.warning("No valid parameters extracted from: %s", new_info_json_raw)
                yield "我没能理解您提供的信息，请尝试更清晰地表述。"
                return

            logging.debug('current_values: %s', current_values)
            logging.debug('slot update before: %s', self.slot)
            update_slot(current_values, self.slot)
            logging.debug('slot update after: %s', self.slot)

            # 判断参数是否已经全部补全
            if is_slot_fully_filled(self.slot):
                print("参数已完整")
                # 使用异步生成器返回结果，传递用户原始问题
                async for chunk in self.respond_with_complete_data_stream(user_input):
                    yield chunk
            else:
                print("参数不完整")
                response = await self.ask_user_for_missing_data(user_input)
                yield response
        except Exception as e:
            logging.exception("Error processing user input: %s", e)
            yield f"处理您的请求时出现了问题: {str(e)}，请稍后再试。"

    # 添加新方法，支持流式返回完整数据的响应
    async def respond_with_complete_data_stream(self, user_input=None):
        print("API请求中1。。")
        # 当所有数据都准备好后的响应
        logging.debug(f'%s ------ 参数已完整，详细参数如下', self.scene_name)
        logging.debug(format_name_value_for_logging(self.slot))
        logging.debug(f'正在请求%sAPI，请稍后……', self.scene_name)

        # 创建参数字典
        params = {}
        for item in self.slot:
            params[item["name"]] = item["value"]
        print("API请求中2。。")

        

        # 调用API获取数据
        try:
            logger.info(f"准备调用API，场景: {self.scene_name}, 参数: {params}")
            # 传递user_input作为user_question参数
            api_result = query_data_by_scene(self.scene_name, self.scene_config, params, user_input)
            print("api_result121",api_result)
            
            logger.info(f"API调用结果: {api_result.get('status', 'unknown')}")

            # 保存API结果
            self.api_result = api_result

            # 如果API调用成功，使用大模型生成回答
            if api_result.get("status") == "success" and "data" in api_result:
                # 修正：正确提取数据列表
                data_list = api_result.get("data", [])
                
                # 处理可能的嵌套结构
                if isinstance(data_list, dict) and "data" in data_list:
                    actual_data = data_list["data"]
                else:
                    actual_data = data_list
                    
                # 获取数据行数
                data_rows = len(actual_data) if isinstance(actual_data, list) else 0
                logger.info(f"API返回数据行数: {data_rows}")
                print("actual_data:", actual_data)
                
                # 尝试生成可视化
                has_visualization = False
                if data_rows >= 4:
                    # 尝试生成可视化，传递用户问题
                    self.generate_visualization(api_result, question=user_input)
                    has_visualization = self.visualization is not None
                    logger.info(f"可视化生成状态: {has_visualization}")

                # 使用用户原始问题或参数字典作为提示词的一部分
                user_question = user_input if user_input else str(params)
                
                # 生成文字说明（包含表格）
                prompt = self.generate_result_prompt(user_question, api_result, data_rows, has_visualization)
                async for chunk in self.stream_llm_response(prompt):
                    yield chunk
                
                # 修正表格展示逻辑
                markdown_table = api_result.get("markdown_table")
                if markdown_table and data_rows >= 4:
                    logger.info("输出HTML表格")
                    yield "\n\n以下是数据的表格展示：\n"
                    html_table = self._format_html_table(actual_data)  # 使用正确的数据
                    yield f"<div class='table-container'>{html_table}</div>\n"
                
                # 修正可视化逻辑中的数据传递
                if data_rows >= 4 and self.visualization:
                    try:
                        logger.info("执行可视化代码")
                        print("actual_data for visualization:", actual_data)
                        # 暂时禁用暗色主题以解决显示问题
                        chart_result = self.execute_visualization_code(self.visualization, actual_data, dark_mode=False)  # 使用正确的数据
                        if chart_result:
                            logger.info("输出图表")
                            yield "\n\n以下是数据的可视化展示：\n"
                            yield f"<div class='chart-container'>{chart_result}</div>"
                    except Exception as e:
                        logger.error(f"执行可视化代码失败: {e}")
            elif api_result.get("error"):
                yield f"抱歉，查询数据时出现错误: {api_result.get('error')}"
            else:
                yield json.dumps(params, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"API调用过程中发生错误: {str(e)}")
            yield f"抱歉，在处理您的请求时发生了错误: {str(e)}"
    
    def _format_html_table(self, data):
        """将数据格式化为HTML表格"""
        if not data or not isinstance(data, list) or len(data) == 0:
            return ""
        
        # 获取所有列名
        columns = list(data[0].keys())
        
        # 创建HTML表格
        html = "<table>\n"
        
        # 添加表头
        html += "<thead>\n<tr>\n"
        for col in columns:
            html += f"<th>{col}</th>\n"
        html += "</tr>\n</thead>\n"
        
        # 添加表格内容
        html += "<tbody>\n"
        for row in data:
            html += "<tr>\n"
            for col in columns:
                html += f"<td>{row.get(col, '')}</td>\n"
            html += "</tr>\n"
        html += "</tbody>\n"
        
        # 关闭表格标签
        html += "</table>"
        
        return html

    # 修改 stream_llm_response 方法，确保它正确实现为异步生成器
    async def stream_llm_response(self, prompt):
        try:
            print("111")
            # 不要直接返回函数调用结果，而是使用 async for 迭代它并 yield 结果
            async for chunk in get_streaming_llm_response(prompt):
                yield chunk
        except Exception as e:
            logger.error(f"流式生成回复时出错: {str(e)}")
            yield f"生成回复时出现错误: {str(e)}"

    # 将ask_user_for_missing_data方法修改为异步方法
    async def ask_user_for_missing_data(self, user_input):
        message = get_slot_query_user_message(self.scene_name, self.slot, user_input)
        # 请求用户填写缺失的数据
        result = await send_message(message, user_input)
        return result

    # stream_llm_response方法已经是异步的，保持不变

    def should_generate_chart(self, df):
        """
        检查是否应该为给定的DataFrame生成图表
        参考Vanna的实现，检查DataFrame是否有多行数据和数值列

        Args:
            df (pd.DataFrame): 要检查的DataFrame

        Returns:
            bool: 如果应该生成图表返回True，否则返回False
        """
        if df is None or df.empty:
            return False

        # 检查是否有多行数据和数值列
        if len(df) > 1 and df.select_dtypes(include=['number']).shape[1] > 0:
            return True

        return False

    def generate_visualization(self, api_result, question=None):
        """
        生成数据可视化代码
        优化版本，参考Vanna的实现方式

        Args:
            api_result: API查询结果
            question: 用户问题（可选）

        Returns:
            str: 生成的Plotly代码，如果失败返回None
        """
        try:
            # 检查API结果是否包含数据
            if not api_result or not api_result.get("data"):
                logger.warning("API结果中没有数据，无法生成可视化")
                return None

            # 尝试将API结果转换为DataFrame
            data = api_result.get("data")
            if isinstance(data, list) and len(data) > 0:
                try:
                    df = pd.DataFrame(data)
                    logger.info(f"DataFrame创建成功，形状: {df.shape}")

                    # 检查是否应该生成图表
                    if not self.should_generate_chart(df):
                        logger.info("数据不适合生成图表")
                        return None

                    # 获取更详细的DataFrame元数据信息（参考Vanna的方式）
                    df_metadata = f"Running df.dtypes gives:\n{df.dtypes}"

                    # 构建系统消息（参考Vanna的prompt结构）
                    if question is not None:
                        system_msg = f"The following is a pandas DataFrame that contains the results of the query that answers the question the user asked: '{question}'"
                    else:
                        system_msg = "The following is a pandas DataFrame"

                    system_msg += f"\n\nThe following is information about the resulting pandas DataFrame 'df': \n{df_metadata}"

                    # 构建用户消息（让LLM根据数据和问题智能选择图表类型）
                    user_msg = ("Can you generate the Python plotly code to chart the results of the dataframe? "
                              "Assume the data is in a pandas dataframe called 'df'. "
                              "Analyze the data structure and user question to choose the most appropriate chart type: "
                              "- Use bar charts (px.bar) for comparing categories, institutions, or discrete values "
                              "- Use line charts (px.line) for time series, trends, or continuous data over time "
                              "- Use pie charts (px.pie) for showing proportions, percentages, or parts of a whole "
                              "- Use scatter plots (px.scatter) for relationships between two continuous variables "
                              "- Use indicators (go.Indicator) if there is only one value to display "
                              "Choose the chart type that best answers the user's question and visualizes the data effectively. "
                              "Respond with only Python code. Do not answer with any explanations -- just the code.")

                    # 发送请求获取可视化代码
                    plotly_code = send_llm_request(system_msg, user_msg)

                    # 提取和清理Python代码
                    self.visualization = self._extract_python_code(plotly_code)
                    self.visualization = self._sanitize_plotly_code(self.visualization)

                    logger.info("成功生成可视化代码")
                    return self.visualization

                except Exception as e:
                    logger.error(f"转换数据为DataFrame时出错: {str(e)}")
                    return None
            else:
                logger.warning("API结果数据格式不适合可视化")
                return None

        except Exception as e:
            logger.error(f"生成可视化时出错: {str(e)}")
            return None

    def _extract_python_code(self, markdown_string):
        """从Markdown字符串中提取Python代码块"""
        if not markdown_string:
            return ""

        pattern = r"```[\w\s]*python\n([\s\S]*?)```|```([\s\S]*?)```"  # 正则表达式模式，匹配Python代码块
        matches = re.findall(pattern, markdown_string, re.IGNORECASE)  # 查找所有匹配项

        python_code = []
        for match in matches:
            python = match[0] if match[0] else match[1]  # 提取Python代码
            python_code.append(python.strip())

        if len(python_code) == 0:
            return markdown_string
        print("python_code[0] ",python_code[0] )
        return python_code[0]  # 返回提取的Python代码

    def _sanitize_plotly_code(self, raw_plotly_code):
        """
        清理Plotly代码
        优化版本，参考Vanna的实现，提供更全面的代码清理
        """
        if not raw_plotly_code:
            return ""

        # 移除fig.show()语句（参考Vanna的实现）
        plotly_code = raw_plotly_code.replace("fig.show()", "")

        # 移除其他可能的显示语句
        plotly_code = plotly_code.replace("plt.show()", "")
        plotly_code = plotly_code.replace("display(fig)", "")

        # 确保代码包含必要的导入语句
        imports_needed = []
        if "px." in plotly_code and "import plotly.express as px" not in plotly_code:
            imports_needed.append("import plotly.express as px")
        if "go." in plotly_code and "import plotly.graph_objects as go" not in plotly_code:
            imports_needed.append("import plotly.graph_objects as go")
        if ("plotly" in plotly_code.lower() and
            "import plotly" not in plotly_code and
            "from plotly" not in plotly_code and
            not imports_needed):
            imports_needed.extend(["import plotly.express as px", "import plotly.graph_objects as go"])

        if imports_needed:
            plotly_code = "\n".join(imports_needed) + "\n\n" + plotly_code

        return plotly_code.strip()

    def _get_chart_colors(self, dark_mode=False):
        """
        获取适合当前主题的图表颜色配置

        Args:
            dark_mode (bool): 是否为暗色主题

        Returns:
            list: 颜色列表
        """
        if dark_mode:
            # 暗色主题下使用更亮的颜色
            return ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
        else:
            # 浅色主题下使用标准颜色
            return ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']

    def get_plotly_figure(self, plotly_code, df, dark_mode=False):
        """
        从DataFrame和Plotly代码获取Plotly图表
        参考Vanna的实现，包含智能回退机制

        Args:
            plotly_code (str): Plotly代码
            df (pd.DataFrame): 数据DataFrame
            dark_mode (bool): 是否使用暗色主题

        Returns:
            plotly.graph_objs.Figure: Plotly图表对象，失败时返回None
        """
        ldict = {"df": df, "px": px, "go": go}
        try:
            exec(plotly_code, globals(), ldict)
            fig = ldict.get("fig", None)
        except Exception as e:
            logger.warning(f"执行生成的Plotly代码失败: {e}，尝试生成默认图表")
            # 参考Vanna的智能回退逻辑
            numeric_cols = df.select_dtypes(include=["number"]).columns.tolist()
            categorical_cols = df.select_dtypes(include=["object", "category"]).columns.tolist()

            # 根据数据类型智能选择图表类型，并使用适合的颜色
            colors = self._get_chart_colors(dark_mode)

            if len(numeric_cols) >= 2:
                # 使用前两个数值列创建散点图
                fig = px.scatter(df, x=numeric_cols[0], y=numeric_cols[1],
                               color_discrete_sequence=colors)
            elif len(numeric_cols) == 1 and len(categorical_cols) >= 1:
                # 使用柱状图显示一个数值列和一个分类列（最常见的对比场景）
                fig = px.bar(df, x=categorical_cols[0], y=numeric_cols[0],
                           color=categorical_cols[0] if len(df[categorical_cols[0]].unique()) <= 10 else None,
                           color_discrete_sequence=colors)
                # 优化柱状图显示
                fig.update_layout(showlegend=len(df[categorical_cols[0]].unique()) <= 5)
                fig.update_xaxes(tickangle=45)  # 倾斜x轴标签避免重叠
            elif len(categorical_cols) >= 1 and df[categorical_cols[0]].nunique() < 10:
                # 对于分类数据且唯一值较少时使用饼图
                fig = px.pie(df, names=categorical_cols[0],
                           color_discrete_sequence=colors)
            else:
                # 默认使用折线图
                fig = px.line(df, color_discrete_sequence=colors)

        if fig is None:
            return None

        # 应用主题配置
        if dark_mode:
            fig.update_layout(
                template="plotly_dark",
                # 确保文字和线条在暗色背景下可见
                font=dict(color="white"),
                plot_bgcolor="rgba(0,0,0,0.1)",
                paper_bgcolor="rgba(0,0,0,0.1)"
            )
            # 更新轴标签颜色
            fig.update_xaxes(gridcolor="rgba(255,255,255,0.2)", tickfont=dict(color="white"))
            fig.update_yaxes(gridcolor="rgba(255,255,255,0.2)", tickfont=dict(color="white"))
        else:
            # 浅色主题配置
            fig.update_layout(
                template="plotly_white",
                font=dict(color="black")
            )

        return fig

    def execute_visualization_code(self, plotly_code, data, dark_mode=False):
        """
        执行可视化代码并返回图表HTML
        优化版本，参考Vanna的实现，包含更好的错误处理和回退机制

        Args:
            plotly_code (str): Plotly代码
            data: 数据（列表格式）
            dark_mode (bool): 是否使用暗色主题

        Returns:
            str: 图表HTML，失败时返回None
        """
        try:
            # 将数据转换为DataFrame
            if isinstance(data, list) and len(data) > 0:
                df = pd.DataFrame(data)
            else:
                logger.warning("数据格式不支持可视化")
                return None

            # 使用优化的图表生成方法
            fig = self.get_plotly_figure(plotly_code, df, dark_mode)

            if fig is None:
                logger.error("无法生成图表对象")
                return None

            # 将图表转换为HTML，配置参考原有实现
            chart_html = fig.to_html(
                include_plotlyjs='cdn',
                div_id=f"chart_{id(fig)}",
                config={
                    'displayModeBar': True,
                    'responsive': True,
                    'modeBarButtonsToRemove': ['pan2d', 'lasso2d', 'select2d']
                }
            )

            return chart_html

        except Exception as e:
            logger.error(f"执行可视化代码失败: {e}")
            return None

    def get_table_info(self, scene_config):
        """获取场景对应的表信息"""
        import json
        import os

        table_name = scene_config.get("table_name", "")
        if not table_name:
            return None

        try:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            tables_file = os.path.join(base_dir, "config", "tables.json")

            with open(tables_file, 'r', encoding='utf-8') as file:
                tables_data = json.load(file)

            for table in tables_data.get("tables", []):
                if table.get("table_name") == table_name:
                    return table

            return None
        except Exception as e:
            print(f"获取表信息出错: {e}")
            return None

    def generate_result_prompt(self, user_input, api_result, data_rows=0, has_visualization=False):
        """生成处理API结果的提示词"""
        # 检查是否有markdown表格表示
        markdown_table = api_result.get("markdown_table")
        print("markdown_table", markdown_table)
        if markdown_table:
            data_representation = f"以下是查询结果的表格形式:\n\n{markdown_table}"
        else:
            # 如果没有markdown表格，则使用JSON格式
            data_representation = f"以下是查询结果:\n{json.dumps(api_result, ensure_ascii=False, indent=2)}"

        # 根据数据行数和是否有可视化调整提示词
        visualization_hint = ""
        if data_rows >= 2 and has_visualization:
            visualization_hint = "\n5、在回答后，系统将自动生成数据可视化图表，你无需描述或生成图表代码。"
        elif data_rows == 1:
            visualization_hint = "\n5、由于数据只有一行，请结合用户user_input，简单回答。"

        prompt = f"""你是一个专业的银行数据分析助手。

    用户问题: {user_input}

    {data_representation}

    注意：
    1、请根据这些数据，用专业、简洁的语言回答用户的问题。
    2、如果数据中包含多个项目或日期，请进行适当的对比分析。
    3、输出格式化要求：
       - 保留两位小数
       - 使用千位分隔符（例如：2,115.03）
       - 最后输出内容不要使用加粗，* 等特殊标记
       - 涉及金额时需要明确单位（如：亿元、万元、元等）
    4、如果数据为空则输出 "暂时没有查到相关指标数据"。{visualization_hint}
    """

        return prompt

    def generate_summary(self, question, df):
        """
        生成数据摘要
        参考Vanna的实现，为查询结果生成简洁的摘要

        Args:
            question (str): 用户问题
            df (pd.DataFrame): 查询结果DataFrame

        Returns:
            str: 数据摘要
        """
        try:
            if df is None or df.empty:
                return "查询结果为空"

            system_msg = f"你是一个专业的银行数据分析助手。用户问题: '{question}'\n\n以下是查询结果的pandas DataFrame:\n{df.to_markdown()}\n\n"
            user_msg = "请基于用户问题简要总结数据。不要提供超出摘要范围的额外解释。"

            summary = send_llm_request(system_msg, user_msg)
            return summary

        except Exception as e:
            logger.error(f"生成数据摘要失败: {e}")
            return "无法生成数据摘要"

    def generate_followup_questions(self, question, api_result, n_questions=3):
        """
        生成后续问题
        参考Vanna的实现，基于当前查询结果生成相关的后续问题

        Args:
            question (str): 原始问题
            api_result: API查询结果
            n_questions (int): 生成问题数量

        Returns:
            list: 后续问题列表
        """
        try:
            if not api_result or not api_result.get("data"):
                return []

            data = api_result.get("data")
            if isinstance(data, list) and len(data) > 0:
                df = pd.DataFrame(data)

                system_msg = f"基于用户问题: '{question}' 和以下查询结果:\n{df.to_markdown()}\n\n"
                user_msg = f"请生成{n_questions}个相关的后续问题，这些问题应该是用户可能想要进一步了解的银行数据分析问题。每个问题占一行，不要编号。"

                response = send_llm_request(system_msg, user_msg)

                # 清理响应，移除编号
                import re
                numbers_removed = re.sub(r"^\d+\.\s*", "", response, flags=re.MULTILINE)
                questions = [q.strip() for q in numbers_removed.split("\n") if q.strip()]

                return questions[:n_questions]

        except Exception as e:
            logger.error(f"生成后续问题失败: {e}")
            return []
