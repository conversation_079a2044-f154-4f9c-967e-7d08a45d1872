# encoding=utf-8
from models.chatbot_model import ChatbotModel
from utils.app_init import before_init
from utils.helpers import load_all_scene_configs
from utils.response_formatter import ResponseFormatter
from fastapi import FastAPI, Request, Response
from fastapi.responses import JSONResponse, FileResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import threading
import subprocess
import sys
import os
import logging  # 添加logging导入
import json
import asyncio  # 添加asyncio导入
from schemas.chat import ChatMessage, SessionRequest, SessionResponse
from services.session_service import SessionService
from services.history_service import HistoryService
from models.session_models import (
    SessionCreateRequest, MessageCreateRequest,
    HistoryQueryRequest, MessageRole, MessageType
)

# 配置日志
logger = logging.getLogger(__name__)  # 添加logger定义

# 定义请求模型
class QuestionRequest(BaseModel):
    question: str
    format: Optional[str] = "html"
    history: Optional[List[Dict[str, Any]]] = None
    user_id: Optional[str] = "default"  # 添加用户ID字段
    session_id: Optional[str] = None  # 添加会话ID字段

# 使用新的生命周期事件处理
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 应用启动时初始化资源
    try:
        from services.connection_manager import ConnectionManager
        await ConnectionManager.get_instance()
        from utils.helpers import _init_semaphores
        _init_semaphores()

        # 初始化会话服务并加载会话
        from services.session_service import SessionService
        session_service = SessionService.get_instance()

        # 尝试从文件加载会话
        sessions_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "sessions.json")
        if session_service.load_sessions_from_file(sessions_file):
            logger.info("已从文件加载会话数据")

        # 清理过期会话
        cleaned_count = session_service.cleanup_old_sessions()
        if cleaned_count > 0:
            logger.info(f"已清理 {cleaned_count} 个过期会话")

        logger.info("应用启动，连接池已初始化")
        yield
    finally:
        # 应用关闭时释放资源
        try:
            # 保存会话到文件
            from services.session_service import SessionService
            session_service = SessionService.get_instance()
            sessions_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "sessions.json")
            if session_service.save_sessions_to_file(sessions_file):
                logger.info("已保存会话数据到文件")

            from services.connection_manager import ConnectionManager
            conn_manager = await ConnectionManager.get_instance()
            await conn_manager.close()
            logger.info("应用关闭，资源已释放")
        except Exception as e:
            logger.error(f"关闭资源时出错: {e}")

# 使用lifespan参数创建FastAPI应用
app = FastAPI(
    title="IntelliQ API",
    description="智能问答系统API",
    lifespan=lifespan
)

# 删除旧的on_event处理函数
# @app.on_event("startup") 和 @app.on_event("shutdown") 已移除

# 添加CORS中间件，支持跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 实例化ChatbotModel
chatbot_model = ChatbotModel(load_all_scene_configs())



# 删除重复的POST /路由

# 修复登录端点
@app.post('/login')
async def login(request: Request):
    """用户登录验证"""
    import json
    import os
    
    data = await request.json()
    print("login_data:",data)
    user_id = data.get('user_id')
    belgOrgId = data.get('belgOrgId')
    userName = data.get('userName')
    belgOrgNm = data.get('belgOrgNm')
    fstLvlBrchOrgId = data.get('fstLvlBrchOrgId')

    if not user_id or not belgOrgId:
        return JSONResponse(content={"error": "用户ID和机构代码不能为空"}, status_code=400)

    # 加载机构映射配置
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'mapping_config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            mapping_config = json.load(f)
            institution_mapping = mapping_config.get('institution_mapping', {})
    except Exception as e:
        logger.error(f"加载机构映射配置失败: {e}")
        institution_mapping = {}

    # 处理分行信息
    if not fstLvlBrchOrgId:
        # 如果分行ID为空，填充为总行
        fstLvlBrchOrgId = "711000"
        fstLvlBrchOrgNm = "总行"
    else:
        # 根据分行ID获取分行名称
        fstLvlBrchOrgNm = institution_mapping.get(fstLvlBrchOrgId, "未知分行")
    print("fstLvlBrchOrgNm:",fstLvlBrchOrgNm)
    # 创建会话服务实例
    session_service = SessionService.get_instance()

    # 创建新会话，传入完整的用户信息
    session_data = await session_service.create_session(
        user_id=user_id,
        belgOrgId=belgOrgId,
        userName=userName,
        belgOrgNm=belgOrgNm,
        fstLvlBrchOrgId=fstLvlBrchOrgId,
        fstLvlBrchOrgNm=fstLvlBrchOrgNm
    )
    print("session_data ",session_data)

    return JSONResponse(content={
        "user_id": user_id,
        "userName": userName,
        "belgOrgId": belgOrgId,
        "belgOrgNm": belgOrgNm,
        "fstLvlBrchOrgId": fstLvlBrchOrgId,
        "fstLvlBrchOrgNm": fstLvlBrchOrgNm,
        "session_id": session_data["session_id"],
        "status": "success"
    })

# 修复会话创建端点
@app.post('/sessions', response_model=SessionResponse)
async def create_or_get_session(request: Request):
    """创建或获取会话"""
    data = await request.json()
    user_id = data.get('user_id')
    belgOrgId = data.get('belgOrgId')
    session_id = data.get('session_id')
    userName = data.get('userName')
    belgOrgNm = data.get('belgOrgNm')
    fstLvlBrchOrgId = data.get('fstLvlBrchOrgId')

    if not user_id or not belgOrgId:
        return JSONResponse(content={"error": "用户ID和机构代码不能为空"}, status_code=400)

    # 加载机构映射配置
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'mapping_config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            mapping_config = json.load(f)
            institution_mapping = mapping_config.get('institution_mapping', {})
    except Exception as e:
        logger.error(f"加载机构映射配置失败: {e}")
        institution_mapping = {}

    # 处理分行信息
    if not fstLvlBrchOrgId:
        # 如果分行ID为空，填充为总行
        fstLvlBrchOrgId = "711000"
        fstLvlBrchOrgNm = "总行"
    else:
        # 根据分行ID获取分行名称
        fstLvlBrchOrgNm = institution_mapping.get(fstLvlBrchOrgId, "未知分行")


    if not user_id:
        return JSONResponse(content={"error": "用户ID不能为空"}, status_code=400)

    session_service = SessionService.get_instance()

    if session_id:
        # 获取现有会话
        session_data = await session_service.get_session(session_id)
        if not session_data:
            return JSONResponse(content={"error": "会话不存在"}, status_code=404)

        # 验证用户是否有权限访问该会话
        if session_data.get("user_id") != user_id:
            return JSONResponse(content={"error": "无权访问该会话"}, status_code=403)

        return session_data
    else:
        # 创建新会话 - 使用完整参数
        session_data = await session_service.create_session(
            user_id=user_id,
            belgOrgId=belgOrgId,
            userName=userName,  # 需要从上下文获取
            belgOrgNm=belgOrgNm,  # 需要从上下文获取
            fstLvlBrchOrgId=fstLvlBrchOrgId,  # 需要从上下文获取
            fstLvlBrchOrgNm=fstLvlBrchOrgNm   # 需要从上下文获取
        )
        return session_data

# 修改获取用户会话列表端点
@app.get('/sessions/{user_id}', response_model=List[SessionResponse])
async def get_user_sessions(user_id: str):
    """获取用户的所有会话"""
    session_service = SessionService.get_instance()
    sessions = await session_service.get_user_sessions(user_id)
    return sessions

# 添加支持查询参数的会话列表端点
@app.get('/sessions')
async def get_sessions_by_query(user_id: str, belgOrgId: str = None):
    """通过查询参数获取用户会话列表"""
    session_service = SessionService.get_instance()

    try:
        # 获取用户的所有会话
        sessions = await session_service.get_user_sessions(user_id)

        # 如果指定了机构代码，进行过滤
        if belgOrgId:
            sessions = [s for s in sessions if s.get('belgOrgId') == belgOrgId]

        return sessions
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        return JSONResponse(content={"error": "获取会话列表失败"}, status_code=500)

# 添加删除会话端点
@app.delete('/sessions/{session_id}')
async def delete_session(session_id: str):
    """删除指定的会话"""
    session_service = SessionService.get_instance()
    history_service = HistoryService.get_instance()

    # 检查会话是否存在
    session = await session_service.get_session(session_id)
    if not session:
        return JSONResponse(content={"error": "会话不存在"}, status_code=404)

    # 删除会话（同时使用两个服务确保数据一致性）
    success1 = await session_service.delete_session(session_id)
    success2 = await history_service.delete_session(session_id)

    if success1 or success2:
        return JSONResponse(content={"message": "会话已成功删除", "session_id": session_id})
    else:
        return JSONResponse(content={"error": "删除会话失败"}, status_code=500)

# 添加历史记录相关API接口
@app.post('/history/sessions', response_model=List[SessionResponse])
async def get_user_history(request: HistoryQueryRequest):
    """获取用户历史会话列表"""
    history_service = HistoryService.get_instance()

    try:
        sessions = await history_service.get_user_sessions(
            user_id=request.user_id,
            limit=request.limit,
            include_messages=request.include_messages
        )
        return sessions
    except Exception as e:
        logger.error(f"获取用户历史失败: {e}")
        return JSONResponse(content={"error": "获取历史记录失败"}, status_code=500)

@app.get('/history/sessions/{session_id}/messages')
async def get_session_messages(session_id: str, limit: int = 50):
    """获取会话的所有消息"""
    history_service = HistoryService.get_instance()

    try:
        messages = await history_service.get_session_messages(session_id, limit)
        return [msg.dict() for msg in messages]
    except Exception as e:
        logger.error(f"获取会话消息失败: {e}")
        return JSONResponse(content={"error": "获取会话消息失败"}, status_code=500)

@app.post('/history/messages')
async def add_message(request: MessageCreateRequest):
    """添加消息到会话"""
    history_service = HistoryService.get_instance()

    try:
        message = await history_service.add_message(
            session_id=request.session_id,
            role=request.role,
            content=request.content,
            message_type=request.message_type,
            metadata=request.metadata
        )

        if message:
            return message.dict()
        else:
            return JSONResponse(content={"error": "添加消息失败"}, status_code=500)

    except Exception as e:
        logger.error(f"添加消息失败: {e}")
        return JSONResponse(content={"error": "添加消息失败"}, status_code=500)

@app.get('/history/stats/{user_id}')
async def get_user_stats(user_id: str):
    """获取用户的会话统计信息"""
    history_service = HistoryService.get_instance()

    try:
        stats = await history_service.get_session_stats(user_id)
        return stats
    except Exception as e:
        logger.error(f"获取用户统计失败: {e}")
        return JSONResponse(content={"error": "获取统计信息失败"}, status_code=500)

# 修改问答端点，减少冗余信息传递
@app.post('/multi_question')
async def api_multi_question(request: Request):
    """传统的非流式响应端点"""
    data = await request.json()
    question = data.get("question")
    output_format = data.get("format", "html")
    session_id = data.get("session_id")

    if not question:
        return JSONResponse(content={"error": "请输入查询的问题"}, status_code=400)

    if not session_id:
        return JSONResponse(content={"error": "会话ID不能为空"}, status_code=400)

    # 处理会话
    session_service = SessionService.get_instance()

    # 使用指定会话
    session = await session_service.get_session(session_id)
    if not session:
        return JSONResponse(content={"error": "指定的会话不存在"}, status_code=404)

    # 从会话中获取用户ID和机构代码
    user_id = session.get("user_id")
    belgOrgId = session.get("belgOrgId")

    # 添加用户消息到会话
    await session_service.add_message(session_id, ChatMessage(role="user", content=question))

    # 处理问题
    full_response = ""
    async for chunk in chatbot_model.process_multi_question(question, user_id, belgOrgId, session_id):
        if chunk:
            full_response += chunk

    # 添加助手回复到会话
    await session_service.add_message(session_id, ChatMessage(role="assistant", content=full_response))

    # 格式化响应
    formatted_response = ResponseFormatter.format_response(full_response, output_format)

    return {
        "answer": full_response,  # 原始响应
        "formatted_answer": formatted_response,  # 格式化后的响应
        "format": output_format,  # 使用的格式
        "session_id": session_id  # 返回会话ID
    }

# 同样修改流式响应端点
@app.post('/multi_question/stream')
async def api_multi_question_stream(request: Request):
    """流式响应端点"""
    data = await request.json()
    question = data.get("question")
    output_format = data.get("format", "html")
    session_id = data.get("session_id")

    if not question:
        return JSONResponse(content={"error": "请输入查询的问题"}, status_code=400)

    if not session_id:
        return JSONResponse(content={"error": "会话ID不能为空"}, status_code=400)

    # 处理会话
    session_service = SessionService.get_instance()

    # 使用指定会话
    session = await session_service.get_session(session_id)
    if not session:
        return JSONResponse(content={"error": "指定的会话不存在"}, status_code=404)

    # 从会话中获取用户ID和机构代码
    user_id = session.get("user_id")
    belgOrgId = session.get("belgOrgId")

    # 添加用户消息到会话
    await session_service.add_message(session_id, ChatMessage(role="user", content=question))

    async def response_generator():
        try:
            # 使用async for迭代异步生成器
            full_response = ""
            async for chunk in chatbot_model.process_multi_question(question, user_id, belgOrgId, session_id):
                if chunk:
                    full_response += chunk
                    encoded_chunk = json.dumps({"content": chunk, "session_id": session_id})
                    yield f"data: {encoded_chunk}\n\n"

            # 添加助手回复到会话
            await session_service.add_message(session_id, ChatMessage(role="assistant", content=full_response))

            # 发送结束标记
            yield f"data: [DONE]\n\n"
        except Exception as e:
            logger.error(f"流式响应生成错误: {e}")
            error_message = json.dumps({"error": str(e)})
            yield f"data: {error_message}\n\n"

    return StreamingResponse(
        response_generator(),
        media_type="text/event-stream"
    )


@app.get('/')
async def index():
    # 修改为使用绝对路径
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "demo", "test_login.html")
    return FileResponse(file_path)

@app.get('/user_input')
async def user_input():
    # 简单的问答界面
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "demo", "user_input.html")
    return FileResponse(file_path)

# 添加会话消息端点
@app.get('/sessions/{session_id}/messages')
async def get_session_messages(session_id: str):
    """获取会话的所有消息"""
    session_service = SessionService.get_instance()

    try:
        # 获取会话 - 添加 await 关键字
        session = await session_service.get_session(session_id)
        if not session:
            return JSONResponse(content={"error": "会话不存在"}, status_code=404)

        # 直接从会话数据中获取消息
        messages = session.get('messages', [])

        # 确保消息格式正确
        formatted_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                formatted_messages.append({
                    'role': msg.get('role', 'user'),
                    'content': msg.get('content', '')
                })
            else:
                # 如果是其他格式，尝试转换
                try:
                    formatted_messages.append({
                        'role': getattr(msg, 'role', 'user'),
                        'content': getattr(msg, 'content', str(msg))
                    })
                except:
                    formatted_messages.append({
                        'role': 'user',
                        'content': str(msg)
                    })

        return formatted_messages

    except Exception as e:
        logger.error(f"获取会话消息失败: {e}")
        return JSONResponse(content={"error": "获取消息失败"}, status_code=500)



# 添加用户反馈API端点
@app.post('/feedback')
async def add_feedback(request: Request):
    """添加用户反馈信息"""
    data = await request.json()
    user_id = data.get('user_id')
    user_name = data.get('user_name', '')
    org_id = data.get('org_id')
    content = data.get('content', '')
    mark = data.get('mark')  # 'like'表示点赞，'dislike'表示踩
    session_id = data.get('session_id')
    
    if not user_id or not org_id or mark not in ['like', 'dislike', 'feedback']:
        return JSONResponse(content={"error": "参数不完整或不正确"}, status_code=400)
    
    try:
        # 获取MySQL服务实例
        from services.mysql_service import MysqlService
        mysql_service = MysqlService.get_instance()
        
        # 创建用户反馈记录
        feedback_id = mysql_service.create_user_feedback(
            userid=user_id,
            username=user_name,
            orgid=org_id,
            content=content,
            mark=mark
        )
        
        if feedback_id > 0:
            return {"status": "success", "feedback_id": feedback_id}
        else:
            return JSONResponse(content={"error": "保存反馈失败"}, status_code=500)
    
    except Exception as e:
        logger.error(f"添加用户反馈失败: {e}")
        return JSONResponse(content={"error": "添加反馈失败"}, status_code=500)

# 获取用户反馈列表
@app.get('/feedback/{user_id}')
async def get_user_feedbacks(user_id: str):
    """获取用户的所有反馈"""
    try:
        # 获取MySQL服务实例
        from services.mysql_service import MysqlService
        mysql_service = MysqlService.get_instance()
        
        # 获取用户反馈列表
        feedbacks = mysql_service.get_user_feedbacks(user_id)
        return feedbacks
    
    except Exception as e:
        logger.error(f"获取用户反馈失败: {e}")
        return JSONResponse(content={"error": "获取反馈失败"}, status_code=500)


if __name__ == '__main__':
    before_init()

    # 启动API服务器作为单独的进程
    api_server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "api_server.py")

    # 使用subprocess启动API服务器
    api_process = subprocess.Popen([sys.executable, api_server_path],
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE)

    print("API服务器已在后台启动")

    # 启动主应用服务器
    #uvicorn.run("app:app", host="127.0.0.1", port=5000,reload=True)
    uvicorn.run(app, host="127.0.0.1", port=5002)
    # uvicorn.run(app, host="127.0.0.1", port=5000,workers=4)   如果需要多线程，需结合异步代码（如 async def 端点）






    """
    1、如果我想查询贷款数据，应该怎么做？
    2、昨天南京分行对公一般性贷款的余额



    """
    # 启动自动保存会话
    sessions_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "sessions.json")
    session_service.start_auto_save(interval=300, file_path=sessions_file)

    """
    1、如果我想查询贷款数据，应该怎么做？
    2、昨天南京分行对公一般性贷款的余额



    """
