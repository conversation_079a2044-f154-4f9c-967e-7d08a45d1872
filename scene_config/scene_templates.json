{"bank_branch_query": {"name": "银行总行、分行经营查询", "description": "查询总行或者分行经营数据服务，不能包含板块条线字段，比如南京分行昨天对公存款余额、对公一般性贷款余额、收益率等指标，比如南京分行对公存款余额、对公一般性贷款余额、收益率等指标（不包含具体客户数据）", "required_dimensions": "日期、总行或者分行名称、项目名称", "example": "JSON：[{'name': 'belgOrgNm', 'desc': '机构名称', 'value': ''}]\n输入：查询南京分行2024年6月的对公存款余额\n答：{'name': 'belgOrgNm', 'value': '南京分行', 'name': 'dataDt', 'value': '********', 'name': 'projNm', 'value': '对公存款',   'name': 'metric_type', 'value': '余额'}", "table_name": "fin_t_eps_fetr_jyfx_indx_org_pefm", "key_words": "总行，分行，", "api_config": {"endpoint": "/api/bank_data/branch_performance", "method": "GET", "param_mapping": {"branch": "belgOrgNm", "date": "dataDt", "projNm": "projNm"}}, "parameters": [{"name": "belgOrgNm", "desc": "机构名称，比如：中信银行，总行，南京分行、上海分行,上海南京路支行，可以提取多个，每个“，”分割，如果提到各个分行，则为所有分行的名称,南京分行,合肥分行,福州分行,北京分行,大连分行,沈阳分行,天津分行,石家庄分行,西安分行,太原分行,呼和浩特分行,南昌分行,南宁分行,昆明分行,上海分行,苏州分行,宁波分行,杭州分行,厦门分行,青岛分行,济南分行。用户可能使用简称如'北分'，需要转换、提取为'北京分行'", "type": "string", "required": true, "transformers": ["branch_code"], "field_type": "dimension"}, {"name": "belgOrgId", "desc": "机构ID与机构名称的映射关系，比如：701100-中信银行，711000-总行，703220-南京分行、703400-福州分行,735751-泰州靖江支行,可提取多个，如果提到各个分行，则为所有分行的名称，与名称匹配不上可以为空，默认为空", "type": "string", "required": false, "field_type": "dimension"}, {"name": "fstLvlBrchOrgNm", "desc": "机构附属的一级分行的名称，比如：中信银行，总行，南京分行、上海分行,可以提取多个，每个“，”分割。用户可能使用简称如'北分'，需要转换、提取为'北京分行'", "type": "string", "required": false, "field_type": "dimension"}, {"name": "fstLvlBrchOrgId", "desc": "机构附属的一级分行的id，fstLvlBrchOrgNm 匹配比如：701100 为中信银行，703220，703260，703400，,可以提取多个，每个“，”分割,与", "type": "string", "required": false, "field_type": "dimension"}, {"name": "pag<PERSON><PERSON>d", "desc": "分行分组，如'中信银行-第一组'、'中信银行-第二组'、'中信银行-第三组'、'中信银行-第四组'等，可选参数", "type": "string", "required": false, "field_type": "dimension"}, {"name": "dataDt", "desc": "所需日期，时间格式：yyyyMMdd，可以为多个日期，每个日期“，”分割，针对于月末，年末，季末等需要提取相关日期，对近一年，近一个季度等，需要提取相关每个的月末日期，比如当前是20250424 近一个季度指的是20250330 的日期，如果没有提到时间（如今天，昨天，或者20250609等具体时间）则默认为系统日期减去2天，系统只能查询截止昨天的数据", "type": "string", "required": true, "field_type": "dimension"}, {"name": "projNm", "desc": "项目名称，如对公一般性贷款、对公存款，营业净收入，利息净收入，非息净收入等，其他非息收入，手续费净收入可以提取多个项目，每个“，”分割，如果没具体说哪个则根据用户意图来多个提取还是单个提取", "type": "string", "required": true, "field_type": "dimension"}, {"name": "curCd", "desc": "币种，0A 本外币折人民币，156为人民币，0B为 外币，用户input中没有提及默认为0A ", "type": "string", "required": true, "field_type": "dimension"}, {"name": "statsObj", "desc": "统计口径，1-会计口径，0-绩效口径，默认为1，会计口径，用户input中没有提及默认为1", "type": "string", "required": true, "field_type": "dimension"}, {"name": "metric_type", "desc": "查询指标的类型，如，余额、日均余额、平均利率、时点余额，余额上月环比，余额同比，余额比上月贡献度等，也可以多个，每个“,”分割，可以模糊匹配可以提取一个，注意如果没具体说哪个如果没说指标类型或者说了多个指标，则空着，意味着查询所有指标,比如用户查询南京分行对公一般性贷款情况，则此处空着", "type": "string", "required": false, "field_type": "metric"}]}, "bank_kpi_query": {"name": "银行总行、分行绩效考核指标查询", "description": "查询总行或者分行经营数据服务，不能包含板块条线字段，比如南京分行昨天对公存款余额、对公一般性贷款余额、收益率等指标，比如南京分行对公存款余额、对公一般性贷款余额、收益率等指标（不包含具体客户数据）", "required_dimensions": "日期、总行或者分行名称、项目名称", "example": "JSON：[{'name': 'belgOrgNm', 'desc': '机构名称', 'value': ''}]\n输入：查询南京分行2024年6月的对公存款余额\n答：{'name': 'belgOrgNm', 'value': '南京分行', 'name': 'dataDt', 'value': '********', 'name': 'projNm', 'value': '对公存款',   'name': 'metric_type', 'value': '余额'}", "table_name": "fin_t_eps_fetr_jyfx_indx_org_kpi", "key_words": "总行，分行，", "api_config": {"endpoint": "/api/bank_data/branch_kpi", "method": "GET", "param_mapping": {"branch": "belgOrgNm", "date": "dataDt", "projNm": "projNm"}}, "parameters": [{"name": "belgOrgNm", "desc": "机构名称，比如：中信银行，总行，南京分行、上海分行,上海南京路支行，可以提取多个，每个“，”分割，如果提到各个分行，则为所有分行的名称,南京分行,合肥分行,福州分行,北京分行,大连分行,沈阳分行,天津分行,石家庄分行,西安分行,太原分行,呼和浩特分行,南昌分行,南宁分行,昆明分行,上海分行,苏州分行,宁波分行,杭州分行,厦门分行,青岛分行,济南分行。用户可能使用简称如'北分'，需要转换、提取为'北京分行'", "type": "string", "required": true, "transformers": ["branch_code"], "field_type": "dimension"}, {"name": "belgOrgId", "desc": "机构ID与机构名称的映射关系，比如：701100-中信银行，711000-总行，703220-南京分行、703400-福州分行,735751-泰州靖江支行,可提取多个，如果提到各个分行，则为所有分行的名称，与名称匹配不上可以为空，默认为空", "type": "string", "required": false, "field_type": "dimension"}, {"name": "fstLvlBrchOrgNm", "desc": "机构附属的一级分行的名称，比如：中信银行，总行，南京分行、上海分行,可以提取多个，每个“，”分割。用户可能使用简称如'北分'，需要转换、提取为'北京分行'", "type": "string", "required": false, "field_type": "dimension"}, {"name": "fstLvlBrchOrgId", "desc": "机构附属的一级分行的id，fstLvlBrchOrgNm 匹配比如：701100 为中信银行，703220，703260，703400，,可以提取多个，每个“，”分割,与", "type": "string", "required": false, "field_type": "dimension"}, {"name": "paGrpId", "desc": "分行分组，如'中信银行-第一组'、'中信银行-第二组'、'中信银行-第三组'、'中信银行-第四组'等，可选参数", "type": "string", "required": false, "field_type": "dimension"}, {"name": "dataDt", "desc": "所需日期，时间格式：yyyyMMdd，可以为多个日期，每个日期“，”分割，针对于月末，年末，季末等需要提取相关日期，对近一年，近一个季度等，需要提取相关日期，比如当前是20250424 近一个季度指的是20250330 的日期，如果没有提到时间，则默认为系统日期减去2天，系统只能查询截止昨天的数据", "type": "string", "required": true, "field_type": "dimension"}, {"name": "indxNm", "desc": "指标名称，如，贵宾客户，对公客户数，零售贵宾客户数，对公民营企业贷款余额等，可以提取多个项目，每个“，”分割，如果没具体说哪个则根据用户意图来多个提取还是单个提取", "type": "string", "required": false, "field_type": "dimension"}, {"name": "indxAlias", "desc": "指标别名，指标名称的简称，贵宾客户，零售贵宾客户数，对公民营企业贷款余额等，可以提取多个项目，每个“，”分割，如果没具体说哪个则根据用户意图来多个提取还是单个提取", "type": "string", "required": true, "field_type": "dimension"}, {"name": "curCd", "desc": "币种，0A 本外币折人民币，156为人民币，0B为 外币，用户input中没有提及默认为0A ", "type": "string", "required": false, "field_type": "dimension"}, {"name": "statsObj", "desc": "统计口径，1-会计口径，0-绩效口径，默认为1，会计口径，用户input中没有提及默认为1", "type": "string", "required": false, "field_type": "dimension"}, {"name": "metric_type", "desc": "查询指标的类型，如，余额、日均余额、平均利率、时点余额，余额上月环比，余额同比，余额比上月贡献度等，也可以多个，每个“,”分割，可以模糊匹配可以提取一个，注意如果没具体说哪个如果没说指标类型或者说了多个指标，则空着，意味着查询所有指标,比如用户查询南京分行对公一般性贷款情况，则此处空着", "type": "string", "required": false, "field_type": "metric"}]}, "bank_segment_query": {"name": "银行板块条线经营查询", "description": "查询银行总行、分行板块、条线经营数据服务，包括余额、收益率等指标，需要有条线或者板块字段，案例：查询南京分行对公业务条线2024年6月30的存款余额", "required_dimensions": "日期、总行或者分行名称、项目名称、板块名称或者条线名称", "example": "JSON：[{'name': 'belgOrgNm', 'desc': '银行机构名称', 'value': ''}]\n输入：查询南京分行对公业务板块，对公业务条线2024年6月的存款余额\n答：{'name': 'belgOrgNm', 'value': '南京分行', 'name': 'date', 'value': '********', 'name': 'projNm', 'value': '对公存款', 'name': 'segment', 'value': '对公业务板块', 'name': 'line', 'value': '对公业务条线', 'name': 'metirc_type', 'value': '余额'}", "table_name": "search_bank_board_indx_info", "key_words": "总行，分行，板块，条线", "api_config": {"endpoint": "/api/bank_data/segment_performance", "method": "GET", "param_mapping": {"branch": "branch_name", "date": "query_date", "projNm": "projNm", "segment": "segment_name", "line": "line_name", "query_type": "metric_type"}}, "parameters": [{"name": "fstLvlBrchOrgNm", "desc": "机构附属的一级分行的名称，比如：中信银行，总行，南京分行、上海分行,可以提取多个，每个“，”分割。用户可能使用简称如'北分'，需要转换、提取为'北京分行'", "type": "string", "required": false, "field_type": "dimension"}, {"name": "fstLvlBrchOrgId", "desc": "机构附属的一级分行的id，fstLvlBrchOrgNm 匹配比如：701100 为中信银行，703220，703260，703400，,可以提取多个，每个“，”分割,与", "type": "string", "required": false, "field_type": "dimension"}, {"name": "belgOrgNm", "desc": "机构名称，比如：中信银行，总行，南京分行、上海分行,上海南京路支行，可以提取多个，每个“，”分割。用户可能使用简称如'北分'，需要转换、提取为'北京分行'", "type": "string", "required": true, "transformers": ["branch_code"], "field_type": "dimension"}, {"name": "belgOrgId", "desc": "机构ID与机构名称的映射关系，比如：701100-中信银行，711000-总行，703220-南京分行、703400-福州分行,735751-泰州靖江支行,可提取多个，与名称匹配不上可以为空，默认为空", "type": "string", "required": false, "field_type": "dimension"}, {"name": "pag<PERSON><PERSON>d", "desc": "分行分组，如'中信银行-第一组'、'中信银行-第二组'、'中信银行-第三组'、'中信银行-第四组'等，可选参数", "type": "string", "required": false, "field_type": "dimension"}, {"name": "dataDt", "desc": "所需日期，时间格式：yyyyMMdd，可以为多个日期，每个日期“，”分割，针对于月末，年末，季末等需要提取相关日期，对近一年，近一个季度等，需要提取相关日期，比如当前是20250424 近一个季度指的是20250330 的日期，", "type": "string", "required": true, "transformers": ["date_format"], "field_type": "dimension"}, {"name": "projNm", "desc": "项目名称，如对公一般性贷款、对公存款等，营业净收入，利息净收入，非息净收入，其他非息收入，手续费净收入可以提取多个类型，每个“，”分割，如果没具体说哪个则根据用户意图来多个提取还是单个提取", "type": "string", "required": true, "field_type": "dimension"}, {"name": "segment", "desc": "板块名称，如对公板块、零售板块等", "type": "string", "required": false, "field_type": "dimension"}, {"name": "bigNm", "desc": "条线名称，如对公业务条线、零售业务条线等，用户提到公司业务，零售业务，指的是对公业务条线、零售业务条线等", "type": "string", "required": false, "field_type": "dimension"}, {"name": "curCd", "desc": "币种，0A 本外币折人民币，156为人民币，0B为 外币，用户input中没有提及默认为0A ", "type": "string", "required": true, "field_type": "dimension"}, {"name": "statsObj", "desc": "统计口径，1-会计口径，0-绩效口径，默认为1，会计口径，用户input中没有提及默认为1", "type": "string", "required": true, "field_type": "dimension"}, {"name": "metric_type", "desc": "查询指标的类型，如日均余额、平均利率、时点余额，余额上月环比，余额同比，余额比上月贡献度等，可以模糊匹配可以提取一个，注意如果没具体说哪个如果没说指标类型或者说了多个指标，则空着，意味着查询所有指标,比如用户查询南京分行对公一般性贷款情况，则此处空着", "type": "string", "required": false, "field_type": "metric"}]}, "bank_customer_query": {"name": "对公客户各个项目经营指标查询", "description": "查询对公客户经营数据指标值，需要有客户公司名称，查询特定对公客户的经营数据指标值，案例：深圳分行比亚迪公司今天的对公存款余额等", "required_dimensions": "客户名称、项目名称", "example": "JSON：[{\"name\": \"custNm\", \"desc\": \"客户名称\", \"value\": \"\"}]\n输入：深圳分行比亚迪公司今天的对公存款余额\n答：[\n  {\"name\": \"custNm\", \"value\": \"比亚迪公司\"},\n  {\"name\": \"belgOrgNm\", \"value\": \"深圳分行\"},\n  {\"name\": \"dataDt\", \"value\": \"********\"},\n  {\"name\": \"projNm\", \"value\": \"对公存款\"},\n  {\"name\": \"metric_type\", \"value\": \"余额\"}\n]", "examples": ["深圳分行比亚迪公司今天的对公存款余额", "南京分行华为公司的贷款余额是多少", "上海分行阿里巴巴的存款余额"], "table_name": "cust_rslrc_search_cust_indx_info", "api_config": {"endpoint": "/api/bank_data/customer_info", "method": "GET", "param_mapping": {"customer_name": "customer_name", "branch": "branch_name", "query_type": "metric_type", "date": "query_date", "projNm": "projNm"}}, "parameters": [{"name": "custNm", "desc": "客户名称", "type": "string", "required": true, "field_type": "dimension"}, {"name": "belgOrgNm", "desc": "机构名称，比如：中信银行，总行，南京分行、上海分行,上海南京路支行，可以提取多个，每个“，”分割。用户可能使用简称如'北分'，需要转换、提取为'北京分行'", "type": "string", "required": true, "transformers": ["branch_code"], "field_type": "dimension"}, {"name": "fstLvlBrchOrgId", "desc": "机构附属的一级分行的id，fstLvlBrchOrgNm 匹配比如：701100 为中信银行，703220，703260，703400，,可以提取多个，每个“，”分割,与", "type": "string", "required": false, "field_type": "dimension"}, {"name": "belgOrgId", "desc": "机构ID与机构名称的映射关系，比如：701100-中信银行，711000-总行，703220-南京分行、703400-福州分行,735751-泰州靖江支行,可提取多个，与名称匹配不上可以为空，默认为空", "type": "string", "required": false, "field_type": "dimension"}, {"name": "fstLvlBrchOrgNm", "desc": "客户所在机构隶属的一级分行名称，中信银行，总行，南京分行、上海分行,可以提取多个等。用户可能使用简称如'北分'，需要转换为'北京分行'", "type": "string", "required": false, "field_type": "dimension"}, {"name": "dataDt", "desc": "所需日期，时间格式：yyyyMMdd，可以为多个日期，每个日期“，”分割，针对于月末，年末，季末等需要提取相关日期，对近一年，近一个季度等，需要提取相关日期，比如当前是20250424 近一个季度指的是20250330 的日期，", "type": "string", "required": true, "transformers": ["date_format"], "field_type": "dimension"}, {"name": "projNm", "desc": "项目名称，如对公一般性贷款、对公存款等，营业净收入，利息净收入，非息净收入，手续费净收入等可以提取多个类型，每个“，”分割，如果没具体说哪个则根据用户意图来多个提取还是单个提取", "type": "string", "required": true, "field_type": "dimension"}, {"name": "curCd", "desc": "币种，0A 本外币折人民币，156为人民币，0B为 外币，用户input中没有提及默认为0A ", "type": "string", "required": true, "field_type": "dimension"}, {"name": "statsObj", "desc": "统计口径，1-会计口径，0-绩效口径，默认为1，会计口径，用户input中没有提及默认为1", "type": "string", "required": true, "field_type": "dimension"}, {"name": "metric_type", "desc": "查询指标的类型，如日均余额、平均利率、时点余额，余额上月环比，余额同比，余额比上月贡献度等，可以模糊匹配可以提取一个，也可以多个，每个“，”分割，注意如果没具体说哪个如果没说指标类型或者说了多个指标，则空着，意味着查询所有指标,比如用户查询南京分行对公一般性贷款情况，则此处空着", "type": "string", "required": false, "field_type": "metric"}]}}