0425 
前端方案 
聊天界面，支持多轮问答、流式输出、会话管理和历史记录查看
1、聊天界面
   前端将用户信息和机构信息传入后端服务
   - 提供一个包含聊天记录显示区域和用户输入区域 的主界面。
   - 用户可以在输入框 输入问题，通过点击“发送”按钮或按回车键发送消息
   
2、 消息发送与接收
   - 提供用户对话界面，聊天记录会区分用户消息和大模型消息 ，调用 sendMessageToAPI()接口（现有api名称） 将问题发送到后端。
   - 满足流式输出的数据处理响应处理,实时接收并显示后端返回的数据块，实现打字机效果，
   - 在一个请求还没完成前，禁止用户发送新的请求功能，在等待后端响应时，应有明确的加载指示（例如，在最新消息下方显示“查询中...”，或一个加载动画）
   
   
3、会话管理
   - 创建新会话 : 用户点击“新建对话”按钮 时，向后端 /sessions 发送 POST 请求创建一个新的会话，获取 session_id，
   创建成功后，自动切换到新的空聊天界面
   - 加载会话 :
   页面加载时会从后端redis 中获取该用户的所有会话列表。
   默认加载最新的会话记录到聊天界面。
   - 用户在历史记录面板点击某个会话时，向后端 /sessions/{sessionId} 发送 GET 请求获取该会话的详细消息，并显示在聊天界面
   - 前端有用户对话的和会话历史记录显示，并按照会话进行分类，按照日期进行分组排序，（今天、昨天、7 天内、更早），显示内容: 每个列表项应显示会话的标题（如第一条用户消息的缩略）
用户信息传输：
  - 信息传递: 应用启动后或每次与后端交互时，需将获取到的用户 ID 和机构信息传递给后端服务端。