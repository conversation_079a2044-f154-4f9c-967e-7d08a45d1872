sequenceDiagram
    participant 用户
    participant 前端
    participant 后端
    participant 模型
    
    用户->>前端: 输入问题
    前端->>后端: POST /multi_question (question)
    后端->>模型: process_multi_question()
    模型-->>后端: 返回澄清问题
    后端-->>前端: {"answer":"澄清问题"}
    前端->>用户: 显示澄清问题
    用户->>前端: 输入"是/否"
    前端->>后端: POST /multi_question (answer)
    后端->>模型: process_multi_question()
    模型->>模型: 处理确认/否认
    模型-->>后端: 最终响应
    后端-->>前端: {"answer":"结果"}