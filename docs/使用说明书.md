
          
# IntelliQ 智能数据查询系统使用说明书

## 系统概述

IntelliQ 是一个基于大型语言模型（LLM）的智能数据查询系统，专门用于银行业务数据的查询和分析。系统结合了先进的意图识别和词槽填充技术，支持多轮对话和自然语言查询。

## 主要功能特性

### 1. 多轮对话管理
- 支持复杂的对话场景和连续多轮交互
- 基于历史对话上下文进行智能理解
- 自动补全缺失参数

### 2. 智能意图识别
- 自动判断用户查询意图（数据查询、帮助、系统信息、闲聊）
- 支持场景自动识别和分类
- 基于业务场景的精准匹配

### 3. 参数提取与处理
- 智能提取查询关键参数（时间、机构、项目等）
- 支持模糊匹配和自动转换
- 参数验证和默认值填充

### 4. 权限控制
- 基于用户机构的数据访问权限控制
- 总行用户可查询所有数据
- 分行用户只能查询本分行及非分行机构数据

## 支持的查询场景

### 1. 银行总行、分行经营查询 (bank_branch_query)
**功能描述：** 查询总行或分行的经营数据

**支持查询：**
- 对公存款余额、对公一般性贷款余额
- 营业净收入、利息净收入、非息净收入
- 各类收益率指标

**查询示例：**
```
南京分行2024年6月的对公存款余额
北京分行昨天的对公一般性贷款余额是多少
总行本月的营业净收入情况
```

### 2. 对公客户各项目经营指标查询 (bank_customer_query)
**功能描述：** 查询特定客户的经营指标数据

**查询示例：**
```
深圳比亚迪公司的对公存款余额
某客户的贷款余额情况
```

### 3. 银行板块条线经营查询 (bank_segment_query)
**功能描述：** 查询不同业务板块或条线的经营数据

### 4. 银行机构ID查询 (bank_org_id_query)
**功能描述：** 查询机构信息和ID映射关系

### 5. 银行总行、分行绩效考核指标查询 (bank_kpi_query)
**功能描述：** 查询绩效考核相关指标

## 使用方法

### 1. 系统访问

#### Web界面访问
1. 打开浏览器访问系统地址
2. 在登录界面输入用户信息：
   - **用户ID：** 您的用户标识
   - **机构代码：** 您所属的机构代码
   - **分行代码：** 您所属的分行代码（可选）

#### API接口访问
**基础URL：** `http://your-server-address`

**主要接口：**
- `POST /multi_question` - 数据查询接口
- `POST /create_session` - 创建会话
- `GET /chat_history/{session_id}` - 获取聊天历史

### 2. 查询语法

#### 基本查询格式
```
[机构名称] + [时间] + [项目名称] + [指标类型]
```

#### 参数说明

**机构名称：**
- 支持全称：南京分行、北京分行、上海分行等
- 支持简称：北分（北京分行）、沪分（上海分行）等
- 特殊机构：总行、中信银行

**时间格式：**
- 具体日期：********、2024年6月30日
- 相对时间：昨天、今天、本月、上月
- 时间段：近一年、近一个季度、上季度

**项目名称：**
- 对公存款、对公一般性贷款、对公一般性贷款
- 营业净收入、利息净收入、非息净收入
- 手续费净收入等

**指标类型：**
- 余额、日均余额、时点余额
- 平均利率、收益率
- 同比、环比、贡献度等

### 3. 查询示例

#### 简单查询
```
南京分行昨天的对公存款余额
北京分行2024年6月的营业净收入
总行本季度的利息净收入
```

#### 复杂查询
```
查询南京分行和上海分行2024年第一季度的对公存款余额对比
北京分行近一年的对公一般性贷款余额变化趋势
各分行本月营业净收入排名情况
```

#### 多维度查询
```
南京分行对公存款的余额、日均余额和平均利率
总行各业务条线的收入情况
```

### 4. 权限说明

#### 总行用户权限
- 可以查询所有机构的数据
- 可以查询所有业务指标
- 可以进行跨机构对比分析

#### 分行用户权限
- 只能查询本分行的数据
- 可以查询非分行机构的数据（如总行数据）
- 无法查询其他分行的数据

### 5. 系统响应

#### 成功响应
系统会返回包含以下内容的结果：
- 查询结果表格（Markdown格式）
- 数据可视化图表（如适用）
- 查询参数确认信息

#### 参数补全
如果查询参数不完整，系统会智能提示：
```
请问客户所在机构名称是？（例如：中信银行、总行、南京分行、上海分行等）
请问所需日期是？（时间格式：yyyyMMdd）
```

#### 错误处理
- 权限不足：提示用户权限范围
- 参数错误：提示正确的参数格式
- 数据不存在：提示调整查询条件

## 技术架构

### 1. 系统组件
- **意图识别模块：** 识别用户查询意图
- **场景匹配模块：** 匹配合适的业务场景
- **参数提取模块：** 提取和处理查询参数
- **权限控制模块：** 验证用户访问权限
- **数据查询模块：** 调用后端API获取数据
- **结果处理模块：** 格式化和展示查询结果

### 2. 数据流程
1. **用户输入** → 意图识别
2. **意图分类** → 场景匹配
3. **参数提取** → 参数处理
4. **权限验证** → API调用
5. **数据获取** → 结果格式化
6. **结果展示** → 用户反馈

## 常见问题

### Q1: 如何查询多个分行的数据？
A: 总行用户可以在查询中指定多个分行，如"南京分行和上海分行的对公存款余额"。分行用户只能查询本分行数据。

### Q2: 支持哪些时间格式？
A: 支持yyyyMMdd格式、中文日期描述（如"昨天"、"本月"）、相对时间（如"近一年"、"上季度"）。

### Q3: 如何获取历史查询记录？
A: 系统自动保存会话历史，可以通过聊天界面查看历史对话记录。

### Q4: 查询结果可以导出吗？
A: 目前支持表格格式展示，可以复制表格内容进行二次处理。

### Q5: 系统支持哪些可视化图表？
A: 支持柱状图、折线图、饼图等多种图表类型，根据数据特点自动选择合适的展示方式。

## 联系支持

如需技术支持或功能建议，请联系系统管理员或查看项目文档获取更多信息。

---

**注意：** 本系统仅供内部使用，请确保查询的数据符合相关合规要求。
        