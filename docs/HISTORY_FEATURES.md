# 历史记录功能说明

## 概述

本项目已经实现了完整的历史记录保存和管理功能，包括前端界面展示和后端数据库存储。用户可以在前端界面查看、管理和删除历史对话记录。

## 功能特性

### 1. 前端功能

- **历史记录面板**: 点击"历史记录"按钮打开侧边栏面板
- **按日期分组**: 自动将对话按"今天"、"昨天"、"7天内"、"更早"分组显示
- **对话标题**: 自动提取每个对话的第一条用户消息作为标题
- **删除功能**: 每个对话项都有删除按钮，支持单独删除
- **加载对话**: 点击历史记录项可以重新加载该对话
- **空状态提示**: 当没有历史记录时显示友好的提示信息

### 2. 后端功能

- **数据库存储**: 使用MySQL数据库持久化存储会话和消息
- **缓存优化**: 使用Redis缓存提高查询性能
- **API接口**: 提供完整的RESTful API接口
- **数据模型**: 使用Pydantic模型确保数据类型安全
- **异步处理**: 全异步实现，提高并发性能

## 数据库结构

### 会话表 (chat_sessions)
```sql
CREATE TABLE chat_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    belg_org_id VARCHAR(255),
    title VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    message_count INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSON
);
```

### 消息表 (chat_messages)
```sql
CREATE TABLE chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    role ENUM('user', 'assistant', 'system') NOT NULL,
    content TEXT NOT NULL,
    message_type ENUM('text', 'visualization', 'error', 'system') DEFAULT 'text',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSON
);
```

### 可视化数据表 (visualization_data)
```sql
CREATE TABLE visualization_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    message_id INT,
    chart_type VARCHAR(100) NOT NULL,
    chart_data JSON NOT NULL,
    plotly_code TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API接口

### 1. 会话管理

#### 获取用户会话列表
```http
GET /sessions/{user_id}
```

#### 创建新会话
```http
POST /sessions
Content-Type: application/json

{
    "user_id": "user_123",
    "belg_org_id": "org_456",
    "title": "新对话"
}
```

#### 删除会话
```http
DELETE /sessions/{session_id}
```

### 2. 历史记录

#### 获取用户历史记录
```http
POST /history/sessions
Content-Type: application/json

{
    "user_id": "user_123",
    "limit": 50,
    "include_messages": true
}
```

#### 获取会话消息
```http
GET /history/sessions/{session_id}/messages?limit=50
```

#### 添加消息
```http
POST /history/messages
Content-Type: application/json

{
    "session_id": "session_123",
    "role": "user",
    "content": "查询南京分行对公一般性贷款余额",
    "message_type": "text",
    "metadata": {}
}
```

#### 获取用户统计
```http
GET /history/stats/{user_id}
```

## 使用方法

### 1. 初始化数据库

首先运行数据库初始化脚本：

```bash
python scripts/init_database.py
```

### 2. 启动应用

```bash
python app.py
```

### 3. 前端使用

1. 打开浏览器访问 `http://127.0.0.1:5000`
2. 登录系统
3. 开始对话
4. 点击右上角的"历史记录"按钮查看历史对话
5. 可以点击历史记录项重新加载对话
6. 可以点击删除按钮删除不需要的对话

## 可视化功能

系统集成了数据可视化功能：

1. **自动生成**: 当API返回数据时，系统会自动尝试生成可视化代码
2. **Plotly支持**: 使用Plotly库生成交互式图表
3. **代码提供**: 在回答后提供完整的Python可视化代码
4. **数据库存储**: 可视化数据和代码会保存到数据库中

## 配置说明

### 数据库配置

在 `scripts/init_database.py` 中修改数据库连接配置：

```python
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'password',
    'db': 'user_chat',
    'charset': 'utf8mb4'
}
```

### 缓存配置

在 `services/history_service.py` 中可以调整缓存TTL：

```python
self.session_cache_ttl = 3600  # 会话缓存1小时
self.message_cache_ttl = 1800  # 消息缓存30分钟
self.user_sessions_cache_ttl = 7200  # 用户会话列表缓存2小时
```

## 注意事项

1. **数据库依赖**: 确保MySQL服务正在运行
2. **Redis依赖**: 如果启用缓存，确保Redis服务正在运行
3. **权限管理**: 用户只能查看和删除自己的会话
4. **数据备份**: 建议定期备份数据库数据
5. **性能优化**: 大量历史数据时建议启用分页和缓存

## 故障排除

### 常见问题

1. **数据库连接失败**: 检查数据库配置和服务状态
2. **历史记录不显示**: 检查用户ID是否正确
3. **删除失败**: 检查会话是否存在和权限
4. **缓存问题**: 重启Redis服务或清除缓存

### 日志查看

系统会记录详细的日志信息，可以通过以下方式查看：

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 扩展功能

### 1. 导出功能

可以添加导出历史记录的功能：

```python
@app.get('/history/export/{user_id}')
async def export_history(user_id: str):
    # 导出用户的所有历史记录
    pass
```

### 2. 搜索功能

可以添加历史记录搜索功能：

```python
@app.post('/history/search')
async def search_history(query: str, user_id: str):
    # 在历史记录中搜索关键词
    pass
```

### 3. 标签功能

可以为会话添加标签分类功能：

```python
@app.post('/sessions/{session_id}/tags')
async def add_session_tags(session_id: str, tags: List[str]):
    # 为会话添加标签
    pass
```
