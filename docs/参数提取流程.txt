用户输入 → 场景识别 → 参数提取 → 参数转换 → 槽位填充 → API调用

### 参数模板配置复杂
问题描述 ：从 `scene_templates.json` 可以看出配置过于复杂：

```
{
  "name": "belgOrgNm",
  "desc": "机构名称，比如：中信银行，总行，南京分行、上海分
  行...",
  "type": "string",
  "required": true,
  "transformers": ["branch_code"],
  "field_type": "dimension"
}
```
影响 ：

- 配置维护成本高
- 新场景添加困难
- 参数描述冗长影响LLM理解


### 3. 提示词工程问题
问题描述 ： `get_slot_update_message` 中的提示词设计存在问题：

```
message = f"""根据用户输入和场景，提取相关参数。
{history_context}{date_context}

场景: {scene_name}

参数模板:
{json.dumps(slot_template, ensure_ascii=False, 
indent=2)}
"""
```
影响 ：

- 提示词过长，影响LLM性能
- 缺乏表结构和字段描述信息
- 示例不够动态和精准

### 4. 错误处理机制不完善
问题描述 ：从 `questions_resolve.txt` 和 `产品记录.txt` 可以看出：

- 参数提取失败时缺乏有效的重试机制
- 错误信息不够详细，难以调试
- 缺乏参数验证机制


### 多轮对话上下文处理问题
问题描述 ：

- 参数合并逻辑简单，新参数直接覆盖旧参数
- 缺乏意图变化检测
- 无法智能保留相关上下文信息

###  性能和扩展性问题
问题描述 ：

- 参数处理器按优先级顺序执行，效率较低
- 缺乏缓存机制，重复计算较多
- 硬编码的特殊处理逻辑，不利于扩展


### 改进提示词工程
- 根据 `resoving_stage.txt` 建议，在提示词中包含表结构和字段描述
- 使用更精准的动态示例
- 优化提示词长度和结构