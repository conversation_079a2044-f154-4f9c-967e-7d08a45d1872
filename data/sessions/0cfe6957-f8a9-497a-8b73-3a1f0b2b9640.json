{"session_id": "0cfe6957-f8a9-497a-8b73-3a1f0b2b9640", "user_id": "123456", "belgOrgId": "701111", "created_at": "2025-04-28T13:55:44.942445", "updated_at": "2025-06-03T15:19:40.205323", "last_active": "2025-06-03T15:19:40.205323", "messages": [{"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-04-28T13:55:44.975103"}, {"role": "assistant", "content": "根据查询结果，2025年4月28日南京分行对公一般性贷款的余额为5,661.77万元。", "timestamp": "2025-04-28T13:55:55.406002"}, {"role": "user", "content": "北京分行呢", "timestamp": "2025-04-28T13:56:07.042199"}, {"role": "assistant", "content": "根据查询结果，2025年4月28日北京分行（机构代码711100）对公一般性贷款项目的余额为5,710.63万元。", "timestamp": "2025-04-28T13:56:18.747365"}, {"role": "user", "content": "你是谁", "timestamp": "2025-05-06T14:57:59.154565"}, {"role": "assistant", "content": "我是数据查询智能助手，版本1.0.0，主要用于总分行经营数据和绩效数据的查询。当前日期是2025年5月6日。如有任何数据查询需求，请随时告诉我。", "timestamp": "2025-05-06T14:58:01.892786"}, {"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-06T17:32:35.636871"}, {"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-06T17:57:13.653486"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败，状态码: 502, 响应: ...", "timestamp": "2025-05-06T17:59:31.768180"}, {"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-06T18:21:33.611022"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败，状态码: 502, 响应: ...", "timestamp": "2025-05-06T18:23:49.921863"}, {"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-06T18:30:02.365348"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败，状态码: 502, 响应: ...", "timestamp": "2025-05-06T18:32:19.871523"}, {"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-07T08:13:58.438075"}, {"role": "assistant", "content": "处理您的请求时出现了问题: Invalid format specifier ' \"参数1\", \"value\": \"值1\"' for object of type 'str'，请稍后再试。", "timestamp": "2025-05-07T08:14:01.162934"}, {"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-07T08:19:16.732933"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败，状态码: 502, 响应: ...", "timestamp": "2025-05-07T08:21:32.016648"}, {"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-07T08:31:44.513815"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败，状态码: 502, 响应: ...", "timestamp": "2025-05-07T08:34:07.690696"}, {"role": "user", "content": "南京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-07T08:35:56.653424"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败，状态码: 502, 响应: ...", "timestamp": "2025-05-07T08:38:18.028171"}, {"role": "user", "content": "北京分行今天对公一般性贷款余额是多少", "timestamp": "2025-05-07T16:47:47.511818"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败，状态码: 502, 响应: ...", "timestamp": "2025-05-07T16:50:15.405201"}, {"role": "user", "content": "请分析各个分行的营收占比，并重点关注前五大分行", "timestamp": "2025-06-03T15:18:20.042306"}, {"role": "assistant", "content": "根据提供的数据，以下是各分行的营收占比分析以及前五大分行的具体情况：\n\n### 总体营收情况\n总营业净收入为 **103,894.27万元**。\n\n### 各分行营收占比\n以下是各分行的营业净收入及其占总收入的比例（按金额从高到低排序）：\n\n1. 太原分行：9,283.05万元，占比 8.94%  \n2. 宁波分行：8,782.49万元，占比 8.46%  \n3. 南宁分行：8,611.59万元，占比 8.30%  \n4. 呼和浩特分行：8,561.19万元，占比 8.24%  \n5. 上海分行：8,256.29万元，占比 7.95%  \n6. 西安分行：7,075.75万元，占比 6.81%  \n7. 杭州分行：7,596.58万元，占比 7.31%  \n8. 昆明分行：5,544.00万元，占比 5.34%  \n9. 南京分行：5,137.99万元，占比 4.95%  \n10. 苏州分行：5,393.79万元，占比 5.19%  \n11. 福州分行：4,912.65万元，占比 4.73%  \n12. 青岛分行：3,881.67万元，占比 3.74%  \n13. 北京分行：2,993.73万元，占比 2.88%  \n14. 南昌分行：3,641.82万元，占比 3.51%  \n15. 济南分行：3,524.73万元，占比 3.39%  \n16. 天津分行：2,329.04万元，占比 2.24%  \n17. 沈阳分行：2,086.74万元，占比 2.01%  \n18. 合肥分行：1,724.41万元，占比 1.66%  \n19. 石家庄分行：1,729.64万元，占比 1.67%  \n20. 厦门分行：1,214.03万元，占比 1.17%  \n\n### 前五大分行分析\n以下为前五大分行的具体表现及占比：\n\n1. **太原分行**：以9,283.05万元的营收位居首位，占比8.94%，显示出其在业务拓展和盈利能力方面的突出表现。  \n2. **宁波分行**：营收为8,782.49万元，占比8.46%，紧随其后，表明其在区域市场中具有较强的竞争力。  \n3. **南宁分行**：营收为8,611.59万元，占比8.30%，展现了其在华南地区的业务优势。  \n4. **呼和浩特分行**：营收为8,561.19万元，占比8.24%，体现了其在华北地区的良好发展态势。  \n5. **上海分行**：营收为8,256.29万元，占比7.95%，作为经济中心城市的分行，其业绩表现符合预期。\n\n### 结论\n从整体来看，前五大分行贡献了总收入的 **42.89%**，是银行的主要营收来源。建议重点关注这些分行的业务模式和成功经验，并将其推广至其他分行，以进一步提升整体业绩。同时，对于营收较低的分行（如厦门分行、石家庄分行等），可以分析其具体原因并制定针对性改进措施。\n\n以下是数据的表格展示：\n<div class='table-container'><table>\n<thead>\n<tr>\n<th>belgOrgId</th>\n<th>belgOrgNm</th>\n<th>date</th>\n<th>projNm</th>\n<th>余额</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>5137.99</td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>1724.41</td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>4912.65</td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>2993.73</td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>6221.48</td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>2086.74</td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>2329.04</td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>1729.64</td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>7075.75</td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>9283.05</td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>8561.19</td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>3641.82</td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>8611.59</td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>5544.0</td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>8256.29</td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>5393.79</td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>8782.49</td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>7596.58</td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>1214.03</td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>3881.67</td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250602</td>\n<td>营业净收入</td>\n<td>3524.73</td>\n</tr>\n</tbody>\n</table></div>\n\n\n以下是数据的可视化展示：\n<div class='chart-container'><html>\n<head><meta charset=\"utf-8\" /></head>\n<body>\n    <div>                        <script type=\"text/javascript\">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>\n        <script src=\"https://cdn.plot.ly/plotly-2.12.1.min.js\"></script>                <div id=\"chart_2501515502992\" class=\"plotly-graph-div\" style=\"height:100%; width:100%;\"></div>            <script type=\"text/javascript\">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"chart_2501515502992\")) {                    Plotly.newPlot(                        \"chart_2501515502992\",                        [{\"alignmentgroup\":\"True\",\"customdata\":[[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"],[\"20250602\"]],\"hovertemplate\":\"projNm=营业净收入<br>分行名称=%{x}<br>余额=%{y}<br>date=%{customdata[0]}<extra></extra>\",\"legendgroup\":\"营业净收入\",\"marker\":{\"color\":\"#636efa\",\"pattern\":{\"shape\":\"\"}},\"name\":\"营业净收入\",\"offsetgroup\":\"营业净收入\",\"orientation\":\"v\",\"showlegend\":true,\"textposition\":\"auto\",\"x\":[\"南京分行\",\"合肥分行\",\"福州分行\",\"北京分行\",\"大连分行\",\"沈阳分行\",\"天津分行\",\"石家庄分行\",\"西安分行\",\"太原分行\",\"呼和浩特分行\",\"南昌分行\",\"南宁分行\",\"昆明分行\",\"上海分行\",\"苏州分行\",\"宁波分行\",\"杭州分行\",\"厦门分行\",\"青岛分行\",\"济南分行\"],\"xaxis\":\"x\",\"y\":[5137.99,1724.41,4912.65,2993.73,6221.48,2086.74,2329.04,1729.64,7075.75,9283.05,8561.19,3641.82,8611.59,5544.0,8256.29,5393.79,8782.49,7596.58,1214.03,3881.67,3524.73],\"yaxis\":\"y\",\"type\":\"bar\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"分行名称\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"余额\"}},\"legend\":{\"title\":{\"text\":\"projNm\"},\"tracegroupgap\":0},\"title\":{\"text\":\"各分行项目余额对比\"},\"barmode\":\"relative\"},                        {\"displayModeBar\": true, \"responsive\": true, \"modeBarButtonsToRemove\": [\"pan2d\", \"lasso2d\", \"select2d\"]}                    )                };                            </script>        </div>\n</body>\n</html></div>", "timestamp": "2025-06-03T15:19:40.205323"}]}