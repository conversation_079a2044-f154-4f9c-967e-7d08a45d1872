{"session_id": "f926baaa-1412-4d2d-9682-d47681533f77", "user_id": "250604", "userName": null, "belgOrgId": "711000", "belgOrgNm": null, "fstLvlBrchOrgId": "711000", "fstLvlBrchOrgNm": "总行", "created_at": "2025-06-04T08:25:40.567534", "updated_at": "2025-06-06T16:16:02.417298", "last_active": "2025-06-06T16:16:02.417298", "messages": [{"role": "user", "content": "南京分行对公存款余额", "timestamp": "2025-06-04T08:25:54.736157"}, {"role": "assistant", "content": "截至2025年6月4日，南京分行的对公存款余额为1,939.05万元。", "timestamp": "2025-06-04T08:26:12.734578"}, {"role": "user", "content": "南京分行对公存款余额", "timestamp": "2025-06-04T08:32:22.813882"}, {"role": "assistant", "content": "截至2025年6月2日，南京分行的对公存款余额为4,133.14万元。", "timestamp": "2025-06-04T08:32:53.934192"}, {"role": "user", "content": "南京分行对公存款余额", "timestamp": "2025-06-06T11:11:37.052207"}, {"role": "assistant", "content": "截至2025年6月4日，南京分行的对公存款余额为1,939.05万元。", "timestamp": "2025-06-06T11:12:00.350204"}, {"role": "user", "content": "请分析各个分行的营收占比，并重点关注前五大分行", "timestamp": "2025-06-06T13:40:53.480923"}, {"role": "assistant", "content": "根据提供的数据，以下是各个分行的营收占比分析以及前五大分行的重点分析：\n\n### 总体营收情况\n1. 数据中提供了各分行的“余额”和“新增”等信息，但并未明确总营收的具体数值。因此，我们以“余额（万元）”作为主要参考指标进行分析。\n2. 所有分行的总余额为 **94,570.36 万元**。\n\n### 各分行营收占比\n以下是各分行的营收占比计算结果（基于余额排名）：\n- 宁波分行：余额为 **9,453.32 万元**，占比为 **10.00%**\n- 沈阳分行：余额为 **9,537.40 万元**，占比为 **10.09%**\n- 大连分行：余额为 **7,545.21 万元**，占比为 **8.00%**\n- 天津分行：余额为 **6,867.76 万元**，占比为 **7.27%**\n- 南昌分行：余额为 **6,807.74 万元**，占比为 **7.20%**\n- 杭州分行：余额为 **6,684.45 万元**，占比为 **7.07%**\n- 济南分行：余额为 **5,920.12 万元**，占比为 **6.26%**\n- 南京分行：余额为 **3,579.61 万元**，占比为 **3.78%**\n- 西安分行：余额为 **3,488.29 万元**，占比为 **3.69%**\n- 太原分行：余额为 **3,009.93 万元**，占比为 **3.18%**\n- 其他分行的占比均低于 **3.00%**\n\n### 前五大分行重点分析\n根据余额排名，前五大分行及其特点如下：\n1. **宁波分行**\n   - 余额：**9,453.32 万元**\n   - 新增：**428.62 万元**\n   - 营收贡献占比：**2.82%**\n   - 特点：宁波分行在所有分行中余额最高，且新增金额也较为显著，显示出较强的业务增长能力。\n\n2. **沈阳分行**\n   - 余额：**9,537.40 万元**\n   - 新增：**493.56 万元**\n   - 营收贡献占比：**0.58%**\n   - 特点：沈阳分行虽然余额排名第一，但其新增金额较高，显示了良好的扩张潜力。\n\n3. **大连分行**\n   - 余额：**7,545.21 万元**\n   - 新增：**181.25 万元**\n   - 营收贡献占比：**0.68%**\n   - 特点：大连分行在余额和新增方面表现稳健，属于重要收入来源之一。\n\n4. **天津分行**\n   - 余额：**6,867.76 万元**\n   - 新增：**-140.94 万元**\n   - 营收贡献占比：**0.90%**\n   - 特点：天津分行虽然余额较高，但新增为负值，可能需要关注业务下滑的原因。\n\n5. **南昌分行**\n   - 余额：**6,807.74 万元**\n   - 新增：**-494.34 万元**\n根据提供的数据，我们对各个分行的营收占比进行了分析，并重点关注了前五大分行的表现。以下是具体分析结果：\n\n### 1. 总体营收情况\n从表格中的“余额(万元)”列可以看出，各分行的营业净收入总和为 **78,469.50万元**。基于此，我们计算了各分行的营收贡献占比。\n\n### 2. 前五大分行营收占比分析\n按照“余额排名”列的顺序，前五大分行及其营收占比分别为：\n\n1. **宁波分行**\n   - 营业净收入：9,453.32万元\n   - 营收贡献占比：12.05%\n   \n2. **沈阳分行**\n   - 营业净收入：9,537.40万元\n   - 营收贡献占比：12.16%\n   \n3. **大连分行**\n   - 营业净收入：7,545.21万元\n   - 营收贡献占比：9.62%\n   \n4. **天津分行**\n   - 营业净收入：6,867.76万元\n   - 营收贡献占比：8.75%\n   \n5. **南昌分行**\n   - 营业净收入：6,807.74万元\n   - 营收贡献占比：8.68%\n\n### 3. 前五大分行新增及收益率表现\n以下是对前五大分行在新增和收益率方面的进一步分析：\n\n- **宁波分行**\n  - 新增：428.62万元\n  - 收益率：4.22%\n\n- **沈阳分行**\n  - 新增：493.56万元\n  - 收益率：5.32%\n\n- **大连分行**\n  - 新增：181.25万元\n  - 收益率：2.18%\n\n- **天津分行**\n  - 新增：-140.94万元（负增长）\n  - 收益率：4.13%\n\n- **南昌分行**\n  - 新增：-494.34万元（负增长）\n  - 收益率：5.18%\n\n### 4. 其他分行表现\n除前五大分行外，其他分行的营收贡献相对较低。例如：\n- **昆明分行**：营收贡献占比为5.17%（4,057.55万元）\n- **济南分行**：营收贡献占比为7.55%（5,920.12万元）\n\n### 5. 总结\n- 前五大分行（宁波分行、沈阳分行、大连分行、天津分行、南昌分行）合计根据提供的数据，以下是各个分行的营收占比分析，并重点关注前五大分行：\n\n### 整体营收情况\n从数据中可以看出，各分行的营业净收入（单位：万元）存在显著差异。总营收为 **85,401.39万元**。\n\n### 各分行营收占比\n以下为各分行的营收及其占总营收的比例（保留两位小数）：\n\n| 分行名称   | 营收（万元） | 占比（%） |\n|------------|--------------|-----------|\n| 沈阳分行   | 9,537.40     | 11.17     |\n| 宁波分行   | 9,453.32     | 11.07     |\n| 南昌分行   | 6,807.74     | 7.97      |\n| 大连分行   | 7,545.21     | 8.83      |\n| 杭州分行   | 6,684.45     | 7.83      |\n| 天津分行   | 6,867.76     | 8.04      |\n| 济南分行   | 5,920.12     | 6.93      |\n| 南京分行   | 3,579.61     | 4.20      |\n| 福州分行   | 1,500.36     | 1.76      |\n| 北京分行   | 1,351.92     | 1.58      |\n\n### 前五大分行分析\n根据营收金额排名，前五大分行如下：\n\n1. **沈阳分行**：营收为 **9,537.40万元**，占比 **11.17%**。该分行在新增、收益率和营收贡献占比方面表现突出，新增金额为 **493.56万元**，收益率为 **5.32%**，营收贡献占比为 **0.58%**。\n   \n2. **宁波分行**：营收为 **9,453.32万元**，占比 **11.07%**。该分行新增金额为 **428.62万元**，收益率为 **4.22%**，营收贡献占比为 **2.82%**。\n\n3. **大连分行**：营收为 **7,545.21万元**，占比 **8.83%**。该分行新增金额为 **181.25万元**，收益率为 **2.18%**，营收贡献占比为 **0.68%**。\n\n4. **天津分行**：营收为 **6,867.76万元**，占比 **8.04%**。尽管其新增金额为负值（-140.94万元），但收益率仍保持在 **4.13%**，营收贡献占比为 **0.90%**。\n\n5. **南昌分行**：营收为 **6,807.74万元**，占比 **7.97%**。虽然新增金额为负值（-494.34万元），但收益率为 **5.18%**，营收贡献占比为 **1.52%**。\n\n### 其他分行简要分析\n- **南京分行**：营收为 **3,579.61万元**，占比 **4.20%**。新增金额为负值（-155.60万元），收益率为 **5.22%**，营收贡献占比为 **2.51%**。\n- **福州分行**：营收为 **1,500.36万元**，占比 **1.76%**。新增金额为正值（371.74万元），根据提供的数据，以下是各个分行的营收占比分析，并重点关注前五大分行的表现：\n\n### 总体营收分布\n1. **总营收**：所有分行的营业净收入总计为 85,234.69 万元。\n2. **营收占比排名**：\n   - 宁波分行以 9,453.32 万元的营收排名第一，占总营收的 11.09%。\n   - 沈阳分行以 9,537.40 万元的营收排名第二，占总营收的 11.19%。\n   - 宁波分行和沈阳分行的营收占比显著高于其他分行，显示出其在整体营收中的重要性。\n   - 大连分行、杭州分行和南昌分行分别位列第三至第五名。\n\n### 前五大分行具体分析\n\n#### 1. 宁波分行\n- **营收金额**：9,453.32 万元\n- **营收占比**：11.09%\n- **新增金额**：428.62 万元（新增排名第二）\n- **收益率**：4.22%（收益率排名第 8）\n- **营收贡献占比**：2.82%（营收贡献占比排名第 3）\n\n#### 2. 沈阳分行\n- **营收金额**：9,537.40 万元\n- **营收占比**：11.19%\n- **新增金额**：493.56 万元（新增排名第一）\n- **收益率**：5.32%（收益率排名第三）\n- **营收贡献占比**：0.58%（营收贡献占比排名倒数第三）\n\n#### 3. 大连分行\n- **营收金额**：7,545.21 万元\n- **营收占比**：8.85%\n- **新增金额**：181.25 万元（新增排名第七）\n- **收益率**：2.18%（收益率排名倒数第四）\n- **营收贡献占比**：0.68%（营收贡献占比排名倒数第四）\n\n#### 4. 杭州分行\n- **营收金额**：6,684.45 万元\n- **营收占比**：7.84%\n- **新增金额**：341.44 万元（新增排名第四）\n- **收益率**：2.92%（收益率排名第六）\n- **营收贡献占比**：0.34%（营收贡献占比排名倒数第一）\n\n#### 5. 南昌分行\n- **营收金额**：6,807.74 万元\n- **营收占比**：7.99%\n- **新增金额**：-494.34 万元（新增排名最后）\n- **收益率**：5.18%（收益率排名第五）\n- **营收贡献占比**：1.52%（营收贡献占比排名第 13）\n\n### 其他分行表现概览\n- **南京分行**：营收金额为 3,579.61 万元，占比 4.20%，新增金额为 -155.60 万元（负增长），收益率为 5.22%，营收贡献占比为 2.51%。\n- **合肥分行**：营收金额为 2,563.30 万元，占比 3.01%，新增金额为 288.63 万元，收益率为 4.02%，营收贡献占比为 2.29%。\n- **福州分行**：营收金额为 1,500.36 万元，占比 1.76%，新增金额为 371.74 万元，收益率为 4.54%，营收贡献占比为 1.68%。\n\n### 总结\n1. 前五大分行（宁波分行、沈阳分行、大连分行、杭州分行、南昌分行）的营收合计为 39,987.12 万元，占总营收的 46.92%，是网络连接错误: \n\n以下是数据的表格展示：\n<div class='table-container'><table>\n<thead>\n<tr>\n<th>belgOrgId</th>\n<th>belgOrgNm</th>\n<th>date</th>\n<th>projNm</th>\n<th>余额排名</th>\n<th>余额</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>15</td>\n<td>3579.61</td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>19</td>\n<td>2563.3</td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>22</td>\n<td>1500.36</td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>23</td>\n<td>1351.92</td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>3</td>\n<td>7545.21</td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>1</td>\n<td>9537.4</td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>4</td>\n<td>6867.76</td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>9</td>\n<td>4753.94</td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>16</td>\n<td>3488.29</td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>17</td>\n<td>3009.93</td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>11</td>\n<td>4389.35</td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>6</td>\n<td>6807.74</td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>10</td>\n<td>4417.8</td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>13</td>\n<td>4057.55</td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>20</td>\n<td>1918.03</td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>12</td>\n<td>4341.08</td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>2</td>\n<td>9453.32</td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>7</td>\n<td>6684.45</td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>21</td>\n<td>1589.23</td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>18</td>\n<td>2609.74</td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td>8</td>\n<td>5920.12</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3579.61</td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>2563.3</td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1500.36</td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1351.92</td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>7545.21</td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>9537.4</td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6867.76</td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4753.94</td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3488.29</td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3009.93</td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4389.35</td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6807.74</td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4417.8</td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4057.55</td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1918.03</td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4341.08</td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>9453.32</td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6684.45</td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1589.23</td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>2609.74</td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>5920.12</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3579.61</td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>2563.3</td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1500.36</td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1351.92</td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>7545.21</td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>9537.4</td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6867.76</td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4753.94</td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3488.29</td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3009.93</td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4389.35</td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6807.74</td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4417.8</td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4057.55</td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1918.03</td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4341.08</td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>9453.32</td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6684.45</td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1589.23</td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>2609.74</td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>5920.12</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3579.61</td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>2563.3</td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1500.36</td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1351.92</td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>7545.21</td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>9537.4</td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6867.76</td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4753.94</td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3488.29</td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>3009.93</td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4389.35</td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6807.74</td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4417.8</td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4057.55</td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1918.03</td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>4341.08</td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>9453.32</td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>6684.45</td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>1589.23</td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>2609.74</td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250604</td>\n<td>营业净收入</td>\n<td></td>\n<td>5920.12</td>\n</tr>\n</tbody>\n</table></div>\n\n\n以下是数据的可视化展示：\n<div class='chart-container'><html>\n<head><meta charset=\"utf-8\" /></head>\n<body>\n    <div>                        <script type=\"text/javascript\">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>\n        <script src=\"https://cdn.plot.ly/plotly-2.12.1.min.js\"></script>                <div id=\"chart_2719597666832\" class=\"plotly-graph-div\" style=\"height:100%; width:100%;\"></div>            <script type=\"text/javascript\">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"chart_2719597666832\")) {                    Plotly.newPlot(                        \"chart_2719597666832\",                        [{\"alignmentgroup\":\"True\",\"customdata\":[[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"],[\"20250604\",\"营业净收入\"]],\"hovertemplate\":\"分行名称=%{x}<br>余额=%{y}<br>date=%{customdata[0]}<br>projNm=%{customdata[1]}<br>余额排名=%{marker.color}<extra></extra>\",\"legendgroup\":\"\",\"marker\":{\"color\":[null,1.0,null,null,2.0,null,null,null,null,3.0],\"coloraxis\":\"coloraxis\",\"pattern\":{\"shape\":\"\"}},\"name\":\"\",\"offsetgroup\":\"\",\"orientation\":\"v\",\"showlegend\":false,\"textposition\":\"auto\",\"x\":[\"沈阳分行\",\"沈阳分行\",\"沈阳分行\",\"沈阳分行\",\"宁波分行\",\"宁波分行\",\"宁波分行\",\"宁波分行\",\"大连分行\",\"大连分行\"],\"xaxis\":\"x\",\"y\":[9537.4,9537.4,9537.4,9537.4,9453.32,9453.32,9453.32,9453.32,7545.21,7545.21],\"yaxis\":\"y\",\"type\":\"bar\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"分行名称\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"余额\"}},\"coloraxis\":{\"colorbar\":{\"title\":{\"text\":\"余额排名\"}},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]},\"legend\":{\"tracegroupgap\":0},\"title\":{\"text\":\"各分行余额排名（前10）\"},\"barmode\":\"relative\"},                        {\"displayModeBar\": true, \"responsive\": true, \"modeBarButtonsToRemove\": [\"pan2d\", \"lasso2d\", \"select2d\"]}                    )                };                            </script>        </div>\n</body>\n</html></div>", "timestamp": "2025-06-06T13:46:33.370438"}, {"role": "user", "content": "北京分行，南京分行，上海分行，沈阳分行的对公存款", "timestamp": "2025-06-06T15:21:22.952316"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "timestamp": "2025-06-06T15:22:13.784291"}, {"role": "user", "content": "北京分行，南京分行，上海分行，沈阳分行的对公存款余额", "timestamp": "2025-06-06T15:23:39.227170"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "timestamp": "2025-06-06T15:24:39.433075"}, {"role": "user", "content": "南京分行对公存款余额", "timestamp": "2025-06-06T15:26:05.326299"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "timestamp": "2025-06-06T15:26:56.797251"}, {"role": "user", "content": "南京分行对公存款余额", "timestamp": "2025-06-06T15:29:49.198135"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "timestamp": "2025-06-06T15:30:42.602835"}, {"role": "user", "content": "南京分行对公存款余额", "timestamp": "2025-06-06T15:54:27.009771"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "timestamp": "2025-06-06T15:55:19.810851"}, {"role": "user", "content": "南京分行对公存款余额", "timestamp": "2025-06-06T16:15:07.969315"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)", "timestamp": "2025-06-06T16:16:02.417298"}]}