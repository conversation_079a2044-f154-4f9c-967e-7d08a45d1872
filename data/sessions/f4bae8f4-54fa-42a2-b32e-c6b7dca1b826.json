{"session_id": "f4bae8f4-54fa-42a2-b32e-c6b7dca1b826", "user_id": "123456", "userName": null, "belgOrgId": "703220", "belgOrgNm": null, "fstLvlBrchOrgId": "703220", "fstLvlBrchOrgNm": "南京分行", "created_at": "2025-06-10T08:47:10.184505", "updated_at": "2025-06-10T11:00:57.541484", "last_active": "2025-06-10T11:00:57.541484", "messages": [{"role": "user", "content": "南京分行昨天的对公存款余额", "timestamp": "2025-06-10T08:47:20.400563"}, {"role": "assistant", "content": "南京分行昨天的对公存款余额为 5,234.73 万元。", "timestamp": "2025-06-10T08:47:45.420929"}, {"role": "user", "content": "北京分行昨天的对公存款余额", "timestamp": "2025-06-10T08:48:39.604197"}, {"role": "assistant", "content": "🚫 权限不足：您只能查询包含 '南京' 的机构数据，无法查询 '北京分行' 的数据。请换其他查询信息。\n\n💡 没有权限，提示：您可以查询您所属分行的相关数据。", "timestamp": "2025-06-10T08:48:47.055073"}, {"role": "user", "content": "南京分行近一年的对公一般性贷款余额变化趋势", "timestamp": "2025-06-10T09:35:58.565263"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: 400 Client Error: Bad Request for url: http://localhost:8001/api/bank_data/branch_performance?belgOrgNm=%E5%8D%97%E4%BA%AC%E5%88%86%E8%A1%8C&belgOrgId=&fstLvlBrchOrgNm=&fstLvlBrchOrgId=&pagrpgid=&dataDt=********&projNm=%E5%AF%B9%E5%85%AC%E8%B4%B7%E6%AC%BE&curCd=0A&statsObj=1&metric_type=%E4%BD%99%E9%A2%9D", "timestamp": "2025-06-10T09:37:37.431442"}, {"role": "user", "content": "南京分行近一年的对公存款余额变化趋势", "timestamp": "2025-06-10T09:38:09.982313"}, {"role": "assistant", "content": "根据提供的数据，南京分行近一年的对公存款余额变化情况如下：\n\n- 截至2024年6月10日，南京分行的对公存款余额为5,196.78万元。\n- 截至2025年6月10日，南京分行的对公存款余额为5,115.84万元。\n\n对比两个时间点的数据可以发现，南京分行的对公存款余额在近一年内有所下降，减少了80.94万元，降幅约为1.56%。", "timestamp": "2025-06-10T09:39:00.587549"}, {"role": "user", "content": "南京分行近一年的对公存款余额变化趋势", "timestamp": "2025-06-10T10:57:11.165011"}, {"role": "assistant", "content": "根据提供的数据，南京分行近一年的对公存款余额变化趋势如下：\n\n南京分行的对公存款余额在近一年内呈现较大的波动。从2024年6月10日到2025年5月10日，余额在1,365.07万元至9,245.35万元之间波动。\n\n具体来看：\n- 2024年6月10日的余额为5,196.78万元。\n- 2024年7月10日达到阶段性高点8,546.73万元。\n- 随后在2024年11月10日降至最低点1,365.07万元。\n- 在2025年4月10日再次回升至最高点9,245.35万元，并在2025年5月10日略微下降至9,152.91万元。\n\n整体而言，南京分行的对公存款余额经历了明显的起伏，尤其是在2024年11月出现显著下降后，于2025年4月恢复并超越前期高点。最终，截至2025年5月10日，余额保持在较高水平9,152.91万元。\n\n以下是数据的表格展示：\n<div class='table-container'><table>\n<thead>\n<tr>\n<th>belgOrgId</th>\n<th>belgOrgNm</th>\n<th>date</th>\n<th>projNm</th>\n<th>余额</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>********</td>\n<td>对公存款</td>\n<td>5196.78</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20240710</td>\n<td>对公存款</td>\n<td>8546.73</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20240810</td>\n<td>对公存款</td>\n<td>7292.47</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20240910</td>\n<td>对公存款</td>\n<td>5709.2</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20241010</td>\n<td>对公存款</td>\n<td>4837.45</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20241110</td>\n<td>对公存款</td>\n<td>1365.07</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20241210</td>\n<td>对公存款</td>\n<td>2924.65</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250110</td>\n<td>对公存款</td>\n<td>3787.09</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250210</td>\n<td>对公存款</td>\n<td>4703.28</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250310</td>\n<td>对公存款</td>\n<td>3004.05</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250410</td>\n<td>对公存款</td>\n<td>9245.35</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250510</td>\n<td>对公存款</td>\n<td>9152.91</td>\n</tr>\n</tbody>\n</table></div>\n\n\n以下是数据的可视化展示：\n<div class='chart-container'><html>\n<head><meta charset=\"utf-8\" /></head>\n<body>\n    <div>                        <script type=\"text/javascript\">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>\n        <script src=\"https://cdn.plot.ly/plotly-2.12.1.min.js\"></script>                <div id=\"chart_1257228147728\" class=\"plotly-graph-div\" style=\"height:100%; width:100%;\"></div>            <script type=\"text/javascript\">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"chart_1257228147728\")) {                    Plotly.newPlot(                        \"chart_1257228147728\",                        [{\"hovertemplate\":\"项目名称=对公存款<br>日期=%{x}<br>余额=%{y}<extra></extra>\",\"legendgroup\":\"对公存款\",\"line\":{\"color\":\"#636efa\",\"dash\":\"solid\"},\"marker\":{\"symbol\":\"circle\"},\"mode\":\"lines\",\"name\":\"对公存款\",\"orientation\":\"v\",\"showlegend\":true,\"x\":[\"2024-06-10T00:00:00\",\"2024-07-10T00:00:00\",\"2024-08-10T00:00:00\",\"2024-09-10T00:00:00\",\"2024-10-10T00:00:00\",\"2024-11-10T00:00:00\",\"2024-12-10T00:00:00\",\"2025-01-10T00:00:00\",\"2025-02-10T00:00:00\",\"2025-03-10T00:00:00\",\"2025-04-10T00:00:00\",\"2025-05-10T00:00:00\"],\"xaxis\":\"x\",\"y\":[5196.78,8546.73,7292.47,5709.2,4837.45,1365.07,2924.65,3787.09,4703.28,3004.05,9245.35,9152.91],\"yaxis\":\"y\",\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"日期\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"余额\"}},\"legend\":{\"title\":{\"text\":\"项目名称\"},\"tracegroupgap\":0},\"title\":{\"text\":\"余额随时间变化\"}},                        {\"displayModeBar\": true, \"responsive\": true, \"modeBarButtonsToRemove\": [\"pan2d\", \"lasso2d\", \"select2d\"]}                    )                };                            </script>        </div>\n</body>\n</html></div>", "timestamp": "2025-06-10T10:58:35.315265"}, {"role": "user", "content": "南京分行近一年每个月末的对公存款余额变化趋势", "timestamp": "2025-06-10T10:59:41.178536"}, {"role": "assistant", "content": "根据提供的数据，南京分行近一年每个月末的对公存款余额变化趋势如下：\n\n- 数据覆盖时间为2024年6月30日至2025年5月31日，共计12个时间点。\n- 对公存款余额在这一年内波动较大，最高值出现在2025年1月31日，为9,818.78万元；最低值出现在2024年9月30日，为1,030.71万元。\n- 整体趋势可以分为三个阶段：\n  1. **下降阶段**：从2024年6月30日的9,562.25万元降至2024年9月30日的1,030.71万元，降幅达8,531.54万元，下降幅度显著。\n  2. **回升阶段**：从2024年9月30日开始逐步回升，至2025年1月31日达到峰值9,818.78万元，增长了8,788.07万元。\n  3. **再次波动阶段**：自2025年1月31日后，余额有所回落，但整体仍保持在较高水平，截至2025年5月31日，余额为6,315.42万元。\n\n综上所述，南京分行的对公存款余额在过去一年内经历了显著的波动，尤其是在2024年第三季度出现明显下滑后，第四季度迅速回升，并在2025年初达到高点。后续几个月则呈现小幅回落的趋势。\n\n以下是数据的表格展示：\n<div class='table-container'><table>\n<thead>\n<tr>\n<th>belgOrgId</th>\n<th>belgOrgNm</th>\n<th>date</th>\n<th>projNm</th>\n<th>余额</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20240630</td>\n<td>对公存款</td>\n<td>9562.25</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20240731</td>\n<td>对公存款</td>\n<td>4992.49</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20240831</td>\n<td>对公存款</td>\n<td>7005.15</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20240930</td>\n<td>对公存款</td>\n<td>1030.71</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20241031</td>\n<td>对公存款</td>\n<td>9568.66</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20241130</td>\n<td>对公存款</td>\n<td>8362.04</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20241231</td>\n<td>对公存款</td>\n<td>4313.81</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250131</td>\n<td>对公存款</td>\n<td>9818.78</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250228</td>\n<td>对公存款</td>\n<td>3925.16</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250331</td>\n<td>对公存款</td>\n<td>8314.17</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250430</td>\n<td>对公存款</td>\n<td>9632.17</td>\n</tr>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250531</td>\n<td>对公存款</td>\n<td>6315.42</td>\n</tr>\n</tbody>\n</table></div>\n\n\n以下是数据的可视化展示：\n<div class='chart-container'><html>\n<head><meta charset=\"utf-8\" /></head>\n<body>\n    <div>                        <script type=\"text/javascript\">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>\n        <script src=\"https://cdn.plot.ly/plotly-2.12.1.min.js\"></script>                <div id=\"chart_1257273349968\" class=\"plotly-graph-div\" style=\"height:100%; width:100%;\"></div>            <script type=\"text/javascript\">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById(\"chart_1257273349968\")) {                    Plotly.newPlot(                        \"chart_1257273349968\",                        [{\"hovertemplate\":\"项目名称=对公存款<br>日期=%{x}<br>余额=%{y}<extra></extra>\",\"legendgroup\":\"对公存款\",\"line\":{\"color\":\"#636efa\",\"dash\":\"solid\"},\"marker\":{\"symbol\":\"circle\"},\"mode\":\"lines\",\"name\":\"对公存款\",\"orientation\":\"v\",\"showlegend\":true,\"x\":[\"2024-06-30T00:00:00\",\"2024-07-31T00:00:00\",\"2024-08-31T00:00:00\",\"2024-09-30T00:00:00\",\"2024-10-31T00:00:00\",\"2024-11-30T00:00:00\",\"2024-12-31T00:00:00\",\"2025-01-31T00:00:00\",\"2025-02-28T00:00:00\",\"2025-03-31T00:00:00\",\"2025-04-30T00:00:00\",\"2025-05-31T00:00:00\"],\"xaxis\":\"x\",\"y\":[9562.25,4992.49,7005.15,1030.71,9568.66,8362.04,4313.81,9818.78,3925.16,8314.17,9632.17,6315.42],\"yaxis\":\"y\",\"type\":\"scatter\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmapgl\":[{\"type\":\"heatmapgl\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"xaxis\":{\"anchor\":\"y\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"日期\"}},\"yaxis\":{\"anchor\":\"x\",\"domain\":[0.0,1.0],\"title\":{\"text\":\"余额\"}},\"legend\":{\"title\":{\"text\":\"项目名称\"},\"tracegroupgap\":0},\"title\":{\"text\":\"余额随时间变化\"}},                        {\"displayModeBar\": true, \"responsive\": true, \"modeBarButtonsToRemove\": [\"pan2d\", \"lasso2d\", \"select2d\"]}                    )                };                            </script>        </div>\n</body>\n</html></div>", "timestamp": "2025-06-10T11:00:57.541484"}]}