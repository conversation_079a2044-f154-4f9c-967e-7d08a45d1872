{"session_id": "9202a99a-d105-4ab1-aaed-b0f9d3c3c3da", "user_id": "123456", "userName": null, "belgOrgId": "711000", "belgOrgNm": null, "fstLvlBrchOrgId": "711000", "fstLvlBrchOrgNm": "总行", "created_at": "2025-06-18T06:51:33.929204", "updated_at": "2025-06-18T06:53:40.070211", "last_active": "2025-06-18T06:53:40.070211", "messages": [{"role": "user", "content": "各个分行昨天的对公存款余额", "timestamp": "2025-06-18T06:52:42.786604"}, {"role": "assistant", "content": "根据查询结果，以下是各个分行在2025年6月17日的对公存款余额（单位：万元）：\n\n- 北京分行：9,675.63万元\n- 南京分行：8,341.56万元\n- 大连分行：8,358.38万元\n- 沈阳分行：8,471.68万元\n- 西安分行：9,555.97万元\n- 宁波分行：9,528.66万元\n- 石家庄分行：9,802.06万元\n- 昆明分行：8,804.03万元\n- 合肥分行：2,693.49万元\n- 福州分行：1,214.51万元\n- 天津分行：6,794.60万元\n- 太原分行：5,016.27万元\n- 呼和浩特分行：2,868.28万元\n- 南昌分行：3,627.66万元\n- 南宁分行：1,379.57万元\n- 上海分行：6,519.46万元\n- 苏州分行：2,864.06万元\n- 杭州分行：4,388.89万元\n- 厦门分行：3,045.13万元\n- 青岛分行：2,241.23万元\n- 济南分行：3,269.69万元\n\n从数据中可以看出，石家庄分行的对公存款余额最高，为9,802.06万元；而福州分行的余额最低，为1,214.51万元。整体来看，各分行的对公存款余额存在较大差异，部分分行的余额超过9,000万元，而少数分行的余额不足2,000万元。\n\n以下是数据的表格展示：\n<div class='table-container'><table>\n<thead>\n<tr>\n<th>belgOrgId</th>\n<th>belgOrgNm</th>\n<th>date</th>\n<th>projNm</th>\n<th>余额</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>703220</td>\n<td>南京分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>8341.56</td>\n</tr>\n<tr>\n<td>703260</td>\n<td>合肥分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>2693.49</td>\n</tr>\n<tr>\n<td>703400</td>\n<td>福州分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>1214.51</td>\n</tr>\n<tr>\n<td>711100</td>\n<td>北京分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>9675.63</td>\n</tr>\n<tr>\n<td>721100</td>\n<td>大连分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>8358.38</td>\n</tr>\n<tr>\n<td>722100</td>\n<td>沈阳分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>8471.68</td>\n</tr>\n<tr>\n<td>723000</td>\n<td>天津分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>6794.6</td>\n</tr>\n<tr>\n<td>724100</td>\n<td>石家庄分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>9802.06</td>\n</tr>\n<tr>\n<td>725100</td>\n<td>西安分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>9555.97</td>\n</tr>\n<tr>\n<td>726100</td>\n<td>太原分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>5016.27</td>\n</tr>\n<tr>\n<td>727100</td>\n<td>呼和浩特分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>2868.28</td>\n</tr>\n<tr>\n<td>728100</td>\n<td>南昌分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>3627.66</td>\n</tr>\n<tr>\n<td>729100</td>\n<td>南宁分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>1379.57</td>\n</tr>\n<tr>\n<td>730100</td>\n<td>昆明分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>8804.03</td>\n</tr>\n<tr>\n<td>731109</td>\n<td>上海分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>6519.46</td>\n</tr>\n<tr>\n<td>732300</td>\n<td>苏州分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>2864.06</td>\n</tr>\n<tr>\n<td>733600</td>\n<td>宁波分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>9528.66</td>\n</tr>\n<tr>\n<td>733990</td>\n<td>杭州分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>4388.89</td>\n</tr>\n<tr>\n<td>734200</td>\n<td>厦门分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>3045.13</td>\n</tr>\n<tr>\n<td>737001</td>\n<td>青岛分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>2241.23</td>\n</tr>\n<tr>\n<td>737200</td>\n<td>济南分行</td>\n<td>20250617</td>\n<td>对公存款</td>\n<td>3269.69</td>\n</tr>\n</tbody>\n</table></div>\n\n\n📊 正在生成数据可视化图表，请稍候...\n\n以下是数据的可视化展示：\n<div class='chart-container'>{\n    \"title\": {\n        \"text\": \"各个分行昨天的对公存款余额\"\n    },\n    \"tooltip\": {\n        \"trigger\": \"axis\",\n        \"axisPointer\": {\n            \"type\": \"shadow\"\n        }\n    },\n    \"xAxis\": {\n        \"type\": \"category\",\n        \"data\": [\"南京分行\", \"合肥分行\", \"福州分行\", \"北京分行\", \"大连分行\", \"沈阳分行\", \"天津分行\", \"石家庄分行\", \"西安分行\", \"太原分行\"]\n    },\n    \"yAxis\": {\n        \"type\": \"value\",\n        \"name\": \"余额 (万元)\"\n    },\n    \"series\": [\n        {\n            \"data\": [8341.56, 2693.49, 1214.51, 9675.63, 8358.38, 8471.68, 6794.6, 9802.06, 9555.97, 5016.27],\n            \"type\": \"bar\",\n            \"label\": {\n                \"show\": true,\n                \"position\": \"top\",\n                \"formatter\": \"{c} 万元\"\n            }\n        }\n    ]\n}</div>", "timestamp": "2025-06-18T06:53:40.070211"}]}