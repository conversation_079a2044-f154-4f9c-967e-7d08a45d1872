{"session_id": "36f044cf-37d2-48c0-a960-35f2baf5894b", "user_id": "123456", "belgOrgId": "701111", "created_at": "2025-05-26T18:17:59.638704", "updated_at": "2025-05-26T18:26:13.196182", "last_active": "2025-05-26T18:26:13.196182", "messages": [{"role": "user", "content": "南京分行今天的对公一般性贷款余额是多少", "timestamp": "2025-05-26T18:19:03.139123"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: 400 Client Error: Bad Request for url: http://localhost:8000/api/bank_data/branch_performance?belgOrgNm=%E5%8D%97%E4%BA%AC%E5%88%86%E8%A1%8C&belgOrgId=&fstLvlBrchOrgNm=&pagrpgid=&dataDt=********&projNm=%E5%AF%B9%E5%85%AC%E8%B4%B7%E6%AC%BE&curCd=0A&statsObj=1&metric_type=%E4%BD%99%E9%A2%9D", "timestamp": "2025-05-26T18:19:33.802002"}, {"role": "user", "content": "南京分行今天的对公一般性贷款余额是多少", "timestamp": "2025-05-26T18:22:40.361029"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: 400 Client Error: Bad Request for url: http://localhost:8000/api/bank_data/branch_performance?belgOrgNm=%E5%8D%97%E4%BA%AC%E5%88%86%E8%A1%8C&belgOrgId=&fstLvlBrchOrgNm=&pagrpgid=&dataDt=********&projNm=%E5%AF%B9%E5%85%AC%E8%B4%B7%E6%AC%BE&curCd=0A&statsObj=1&metric_type=%E4%BD%99%E9%A2%9D", "timestamp": "2025-05-26T18:22:59.707602"}, {"role": "user", "content": "南京分行今天的对公一般性贷款余额是多少", "timestamp": "2025-05-26T18:25:53.985252"}, {"role": "assistant", "content": "抱歉，查询数据时出现错误: API请求失败: 400 Client Error: Bad Request for url: http://localhost:8000/api/bank_data/branch_performance?belgOrgNm=%E5%8D%97%E4%BA%AC%E5%88%86%E8%A1%8C&belgOrgId=&fstLvlBrchOrgNm=&pagrpgid=&dataDt=********&projNm=%E5%AF%B9%E5%85%AC%E8%B4%B7%E6%AC%BE&curCd=0A&statsObj=1&metric_type=%E4%BD%99%E9%A2%9D", "timestamp": "2025-05-26T18:26:13.196182"}]}