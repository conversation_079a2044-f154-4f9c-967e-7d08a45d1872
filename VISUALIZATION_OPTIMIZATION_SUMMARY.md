# 可视化方法优化总结

## 概述
参考 `vanna-main/src/vanna/base/base.py` 中的可视化方法，对 `scene_processor/impl/common_processor.py` 中的可视化方法进行了全面优化。

## 主要优化内容

### 1. 新增 `should_generate_chart()` 方法
- **功能**: 智能判断是否应该为给定的DataFrame生成图表
- **参考**: Vanna的同名方法
- **逻辑**: 检查DataFrame是否有多行数据和数值列
- **优势**: 避免为不适合可视化的数据生成图表

```python
def should_generate_chart(self, df):
    if df is None or df.empty:
        return False
    if len(df) > 1 and df.select_dtypes(include=['number']).shape[1] > 0:
        return True
    return False
```

### 2. 优化 `generate_visualization()` 方法
- **改进点**:
  - 添加了 `question` 参数，支持基于用户问题生成更相关的可视化
  - 使用 `should_generate_chart()` 预检查数据适用性
  - 改进了DataFrame元数据的获取方式
  - 优化了LLM提示词结构，参考Vanna的prompt模式
  - 增强了错误处理和日志记录

### 3. 增强 `_sanitize_plotly_code()` 方法
- **新功能**:
  - 移除更多类型的显示语句（`plt.show()`, `display(fig)`等）
  - 智能检测和添加必要的导入语句
  - 更精确的导入语句管理
  - 代码清理更加全面

### 4. 新增 `get_plotly_figure()` 方法
- **功能**: 从DataFrame和Plotly代码生成图表对象
- **参考**: Vanna的同名方法
- **核心特性**:
  - 智能回退机制：当生成的代码执行失败时，自动生成默认图表
  - 支持暗色主题
  - 根据数据类型智能选择图表类型（散点图、柱状图、饼图、折线图）

### 5. 重构 `execute_visualization_code()` 方法
- **改进**:
  - 使用新的 `get_plotly_figure()` 方法
  - 添加暗色主题支持
  - 更好的错误处理和回退机制
  - 保持原有的HTML配置

### 6. 新增辅助方法

#### `generate_summary()` 方法
- **功能**: 为查询结果生成简洁的数据摘要
- **参考**: Vanna的数据摘要功能
- **用途**: 提供专业的银行数据分析摘要

#### `generate_followup_questions()` 方法
- **功能**: 基于当前查询结果生成相关的后续问题
- **参考**: Vanna的后续问题生成功能
- **特点**: 
  - 生成银行数据分析相关的后续问题
  - 自动清理编号格式
  - 可配置问题数量

## 技术优势

### 1. 智能回退机制
当LLM生成的可视化代码执行失败时，系统会：
- 分析数据的数值列和分类列
- 根据列的类型和数量智能选择合适的图表类型
- 确保始终能生成可用的可视化结果

### 2. 更好的错误处理
- 多层次的异常捕获和处理
- 详细的日志记录
- 优雅的降级处理

### 3. 主题支持
- 支持暗色主题（`plotly_dark`）
- 提升用户体验

### 4. 代码质量提升
- 更清晰的方法结构
- 更好的参数传递
- 更完善的文档注释

## 使用示例

```python
# 检查是否应该生成图表
if processor.should_generate_chart(df):
    # 生成可视化（传入用户问题）
    processor.generate_visualization(api_result, question=user_input)
    
    # 执行可视化代码（支持暗色主题）
    chart_html = processor.execute_visualization_code(
        processor.visualization, 
        data, 
        dark_mode=True
    )

# 生成数据摘要
summary = processor.generate_summary(user_question, df)

# 生成后续问题
followup_questions = processor.generate_followup_questions(
    user_question, 
    api_result, 
    n_questions=3
)
```

## 兼容性
- 保持了与现有代码的完全兼容性
- 所有原有功能继续正常工作
- 新功能为可选增强功能

## 总结
通过参考Vanna的最佳实践，显著提升了可视化系统的：
- **可靠性**: 智能回退机制确保始终能生成图表
- **智能性**: 根据数据特征自动选择最佳图表类型
- **用户体验**: 暗色主题和更好的错误处理
- **扩展性**: 新增的摘要和后续问题功能
