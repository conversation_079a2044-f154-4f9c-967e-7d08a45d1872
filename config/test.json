{"dataDt": "数据日期", "bankNum": "银行号", "belgOrgId": "机构号", "belgOrgNm": "机构名称", "fstLvlBrchOrgId": "一级分行号", "fstLvlBrchOrgNm": "一级分行名称", "lvl1FstBrchOrgId": "分行一级机构号", "lvl1FstBrchOrgNm": "分行一级机构名称", "lvl1SecBrchOrgId": "分行二级机构号", "lvl1SecBrchOrgNm": "分行二级机构名称", "lvl1ThirBrchOrgId": "分行三级机构号", "lvl1ThirBrchOrgNm": "分行三级机构名称", "belgLevel": "机构级别", "bizBrchInd": "机构营业网点标识", "paGrpId": "考核分组", "paGrpBelgNum": "考核分组内成员数", "indxNum": "指标编号", "indxNm": "指标名称", "indxAlias": "指标别名", "curCd": "币种", "statsObj": "统计对象", "statsMethd": "统计方式", "adjustCd": "是否包含调整项", "indxTyp": "指标类型", "orderTyp": "排名方式", "indxResInd": "是否经营结果汇总指标", "indxYesInd": "规模批是否取前一天效益批结果指标", "indxVal": "指标值", "indxValLastD": "指标值_上日", "indxValLastM": "指标值_上月", "indxValLastY": "指标值_上年", "indxValCmtBeginY": "指标值_增量_上年", "indxValAvgCmtBeginY": "指标值_每日平均增量_上年", "indxValSameLastY": "指标值_增量_上年同期", "acctAmtLastD": "指标值_上日发生额", "acctAmtLastM": "指标值_上月发生额", "v1CmtPlan": "年度增量计划", "comprRate": "完成率", "planGap": "计划差额", "surAvgBalReqD": "剩余期间日平均余额要求", "timeRate": "序时进度", "comprTimeRate": "超序时进度", "ach1TimeRateInd": "是否达成序时进度", "ach1IndxNums": "达成序时进度指标数", "ach1IndxNumsRank": "达成序时进度指标数_组内排名", "ach1IndxList": "达成序时进度指标列表", "beh1TimeRateInd": "是否落后序时进度", "beh1IndxNums": "落后序时进度指标数", "beh1IndxNumsRank": "落后序时进度指标数_组内排名", "beh1IndxList": "落后序时进度指标列表", "seriBehindTimeRateInd": "是否严重落后序时进度", "seriBehindIndxNums": "严重落后序时进度指标数", "seriBehindIndxNumsRank": "严重落后序时进度指标数_组内排名", "seriBehindIndxList": "严重落后序时进度指标列表", "totWhrRank": "指标值_全行(有分组)内排名", "totBrnRank": "指标值_机构级别内排名", "totGrpRank": "指标值_组内排名", "totGrpAvg": "指标值_组内平均", "m1CmtWhrRank": "指标值_月增量_全行(有分组)内排名", "m1CmtBrnRank": "指标值_月增量_机构级别内排名", "m1CmtGrpRank": "指标值_月增量_组内排名", "m1CmtGrpAvg": "指标值_月增量_组内平均", "y1CmtWhrRank": "指标值_年增量_全行(有分组)内排名", "y1CmtBrnRank": "指标值_年增量_机构级别内排名", "y1CmtGrpRank": "指标值_年增量_组内排名", "y1CmtGrpAvg": "指标值_年增量_组内平均", "ySame1CmtWhrRank": "指标值_上年同期增量_全行(有分组)内排名", "ySame1CmtBrnRank": "指标值_上年同期增量_机构级别内排名", "ySame1CmtGrpRank": "指标值_上年同期增量_组内排名", "ySame1CmtGrpAvg": "指标值_上年同期增量_组内平均", "m1CmtRankChgLastM": "指标值_月增量_组内排名_比上月变化", "m1CmtRankChgLastY": "指标值_月增量_组内排名_比上年同期变化", "y1CmtRankChgLastM": "指标值_年增量_组内排名_比上月变化", "y1CmtRankChgLastY": "指标值_年增量_组内排名_比上年同期变化", "ySame1CmtRankChgLastM": "指标值_上年同期增量_组内排名_比上月变化", "ySame1CmtRankChgLastY": "指标值_上年同期增量_组内排名_比上年同期变化"}