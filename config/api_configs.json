{"bank_branch_query": {"url": "http://29.225.225.253:8081/query/FANL/data-bds_fian_t_epms_fetr_jyfx_indx_org_pefm_sum", "headers": {"Content-Type": "application/json; charset=UTF-8", "stdBankNum": "001", "stdTransId": "PBNK001", "stdAuthToken": "TEST123456", "stdRqtsYt": "MASA", "stdRspSys": "FANL", "stdApplySystTmtp": "2024-03-11 18:30:20:000"}, "body_template": {"authToken": "TEST123456", "bankNum": "001", "chgFlag": "false", "pageNum": 1, "pageSize": 30, "queryParams": {"dataDt": ********, "belgOrgId": 703220, "belgOrgNm": "南京分行", "fstLvlBrchOrgNm": "南京分行", "curCd": "0A", "statsObj": 1, "projNm": "对公一般性贷款", "dt": ********}, "sysCode": "FANL", "tranId": "CAQP001"}}, "bank_kpi_query": {"url": "http://29.225.225.253:8081/query/FANL/data-bds_fian_t_epms_fetr_jyrb_indx_org_overview_merge", "headers": {"Content-Type": "application/json; charset=UTF-8", "stdBankNum": "001", "stdTransId": "PBNK001", "stdAuthToken": "TEST123456", "stdRqtsYt": "MASA", "stdRspSys": "FANL", "stdApplySystTmtp": "2024-03-11 18:30:20:000"}, "body_template": {"authToken": "TEST123456", "bankNum": "001", "chgFlag": "false", "pageNum": 1, "pageSize": 30, "queryParams": {"dataDt": ********, "belgOrgId": 703220, "belgOrgNm": "南京分行", "fstLvlBrchOrgNm": "南京分行", "indxAlias": "对公一般性贷款", "dt": ********}, "sysCode": "FANL", "tranId": "CAQP001"}}, "bank_org_id_query": {"url": "http://29.225.225.253:8081/query/FANL/data-d_mdasa_001_khdx_jg_zcdd_a", "headers": {"Content-Type": "application/json; charset=UTF-8", "stdBankNum": "001", "stdTransId": "PBNK001", "stdAuthToken": "TEST123456", "stdRqtsYt": "MASA", "stdRspSys": "FANL", "stdApplySystTmtp": "2024-03-11 18:30:20:000"}, "body_template": {"authToken": "TEST123456", "bankNum": "001", "chgFlag": "false", "pageNum": 1, "pageSize": 30, "queryParams": {"belgOrgId": 711000, "belgOrgNm": "总行", "dt": ********}, "sysCode": "FANL", "tranId": "CAQP001"}}, "bank_segment_query": {"url": "http://29.225.225.253:8081/query/FANL/data-bds_fian_t_epms_fetr_jyfx_indx_org_pefm_sum", "headers": {"Content-Type": "application/json; charset=UTF-8", "stdBankNum": "001", "stdTransId": "PBNK001", "stdAuthToken": "TEST123456", "stdRqtsYt": "MASA", "stdRspSys": "FANL", "stdApplySystTmtp": "2024-03-11 18:30:20:000"}, "body_template": {"authToken": "TEST123456", "bankNum": "001", "chgFlag": "false", "pageNum": 1, "pageSize": 30, "queryParams": {"dataDt": ********, "belgOrgId": 703220, "belgOrgNm": "南京分行", "fstLvlBrchOrgNm": "南京分行", "segment": "板块", "bigNm": "业务条线", "curCd": "0A", "statsObj": 1, "projNm": "对公一般性贷款", "dt": ********}, "sysCode": "FANL", "tranId": "CAQP001"}}, "bank_customer_query": {"url": "http://29.225.225.253:8081/query/FANL/data-bds_fian_base_t_pro_cust_sum_did_verif", "headers": {"Content-Type": "application/json; charset=UTF-8", "stdBankNum": "001", "stdTransId": "CAQP001", "stdAuthToken": "TEST123456", "stdRqtsYt": "FANL", "stdRspSys": "FANL", "stdApplySystTmtp": "2025-04-09 17:36:11:691"}, "body_template": {"authToken": "TEST123456", "bankNum": "001", "chgFlag": "false", "pageNum": 1, "pageSize": 30, "queryParams": {"dataDt": "********", "belgOrgId": "703220", "dt": "********", "paOrgNm": "南京城南支行", "bigNm": "公司业务", "coreProdNm": "单位人民币结算账户存款"}, "sysCode": "MASA", "tranId": "CAQP001"}}}