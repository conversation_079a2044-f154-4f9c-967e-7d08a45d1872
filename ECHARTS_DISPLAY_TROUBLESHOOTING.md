# ECharts显示问题排查指南

## 问题诊断

图表不显示可能的原因：

### 1. 前端问题
- ? ECharts库未正确加载
- ? JSON配置解析失败
- ? DOM元素未正确创建
- ? 图表初始化时机错误

### 2. 后端问题
- ? ECharts配置生成失败
- ? JSON格式错误
- ? 数据格式不正确
- ? 回退机制未触发

### 3. 数据传输问题
- ? 配置在传输过程中损坏
- ? 编码问题导致JSON解析失败

## 优化措施

### 1. 前端优化 (demo/user_input.html)

#### 增强调试信息
```javascript
// 添加详细的控制台日志
console.log('图表容器内容:', content);
console.log('解析的图表配置:', chartConfig);
console.log('ECharts选项:', option);
```

#### 改进错误处理
```javascript
try {
    const chart = echarts.init(chartDiv);
    chart.setOption(option);
    console.log('? ECharts图表初始化成功');
} catch (chartError) {
    console.error('? ECharts初始化失败:', chartError);
    container.innerHTML = `<div style="color: red;">图表初始化失败: ${chartError.message}</div>`;
}
```

#### 延迟初始化
```javascript
// 确保DOM已渲染
setTimeout(() => {
    // 初始化ECharts
}, 100);
```

### 2. 后端优化 (scene_processor/impl/common_processor.py)

#### 增强配置生成
```python
def _generate_fallback_echarts_config(self, df, chart_type="bar"):
    # 确保数据类型正确
    categories = df[categorical_cols[0]].astype(str).tolist()
    values = [float(v) if pd.notna(v) else 0 for v in df[numeric_cols[0]].tolist()]
    
    # 添加更多配置选项
    config = {
        "title": {"text": "图表标题", "left": "center"},
        "tooltip": {"trigger": "axis"},
        "grid": {"left": "3%", "right": "4%", "bottom": "3%", "containLabel": True},
        "xAxis": {"type": "category", "data": categories},
        "yAxis": {"type": "value"},
        "series": [{"type": "bar", "data": values}]
    }
```

#### 改进错误处理
```python
def execute_visualization_code(self, echarts_config_str, data):
    try:
        # 主要逻辑
        pass
    except Exception as e:
        logger.error(f"执行ECharts可视化失败: {e}")
        # 最后的回退尝试
        try:
            df = pd.DataFrame(data)
            fallback_config = self._generate_fallback_echarts_config(df, "bar")
            if fallback_config:
                return fallback_config
        except:
            pass
        return None
```

## 调试步骤

### 步骤1: 运行调试脚本
```bash
python debug_echarts_display.py
```

这将测试：
- 基础配置生成
- 处理器集成
- 完整流程
- HTML文件生成

### 步骤2: 检查生成的文件
- `debug_chart_test.html` - 后端生成的配置测试
- `test_echarts_display.html` - 前端渲染测试
- `test_chart_config.json` - 配置文件

### 步骤3: 浏览器调试
1. 打开浏览器开发者工具
2. 查看Console标签页的日志
3. 检查Network标签页的资源加载
4. 验证Elements标签页的DOM结构

### 步骤4: 后端日志检查
查看应用日志中的ECharts相关信息：
```
[时间] - INFO - 数据转换成功，DataFrame形状: (4, 2)
[时间] - INFO - ECharts配置验证成功
[时间] - INFO - ? 配置JSON格式验证通过
```

## 常见问题解决

### 问题1: 图表容器为空
**症状**: 页面上看不到任何图表内容

**解决方案**:
1. 检查后端是否正确生成配置
2. 验证前端是否正确解析JSON
3. 确认DOM元素是否正确创建

### 问题2: ECharts初始化失败
**症状**: 控制台显示ECharts相关错误

**解决方案**:
1. 确认ECharts库正确加载
2. 检查配置格式是否正确
3. 验证DOM元素尺寸是否正确

### 问题3: 数据不显示
**症状**: 图表框架显示但没有数据

**解决方案**:
1. 检查数据格式是否正确
2. 验证series配置是否正确
3. 确认数据类型转换是否成功

### 问题4: JSON解析失败
**症状**: 控制台显示JSON解析错误

**解决方案**:
1. 检查后端生成的JSON格式
2. 验证特殊字符是否正确转义
3. 确认编码格式是否一致

## 测试用例

### 测试用例1: 基础柱状图
```json
{
  "title": {"text": "测试柱状图", "left": "center"},
  "xAxis": {"type": "category", "data": ["A", "B", "C"]},
  "yAxis": {"type": "value"},
  "series": [{"type": "bar", "data": [1, 2, 3]}]
}
```

### 测试用例2: 银行数据
```json
{
  "title": {"text": "各分行余额对比", "left": "center"},
  "xAxis": {"type": "category", "data": ["北京分行", "上海分行"]},
  "yAxis": {"type": "value", "name": "余额(万元)"},
  "series": [{"type": "bar", "data": [7731.07, 9892.58]}]
}
```

## 验证清单

- [ ] ECharts库正确加载
- [ ] 后端配置生成成功
- [ ] JSON格式验证通过
- [ ] 前端解析无错误
- [ ] DOM元素正确创建
- [ ] 图表初始化成功
- [ ] 数据正确显示
- [ ] 交互功能正常

## 下一步优化

1. **性能优化**: 按需加载ECharts组件
2. **主题支持**: 添加暗色/浅色主题切换
3. **交互增强**: 添加更多交互功能
4. **错误恢复**: 改进错误处理和恢复机制
5. **监控告警**: 添加图表渲染监控

## 联系支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. 后端日志中的相关信息
3. 生成的JSON配置文件
4. 具体的数据样例

这将帮助快速定位和解决问题。
