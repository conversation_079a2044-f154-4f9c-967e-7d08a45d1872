<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>��ͼ�����</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            min-height: 400px;
        }
        
        #chart {
            width: 100%;
            height: 400px;
        }
        
        .test-area {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
        
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>��ͼ�����</h1>
        <p>���ҳ�����ڲ���EChartsͼ����Ⱦ�Ƿ�����</p>
        
        <div class="test-area">
            <h3>����1: ֱ��ECharts��Ⱦ</h3>
            <button onclick="testDirectChart()">����ֱ����Ⱦ</button>
            <div class="chart-container">
                <div id="chart"></div>
            </div>
        </div>
        
        <div class="test-area">
            <h3>����2: ģ��JSON���ý���</h3>
            <button onclick="testJsonParsing()">����JSON����</button>
            <div class="chart-container" id="jsonTestContainer">
                <!-- JSON���û�������� -->
            </div>
        </div>
        
        <div class="test-area">
            <h3>����3: ģ������ʵ������</h3>
            <button onclick="testRealData()">����ʵ������</button>
            <div class="chart-container" id="realDataContainer">
                <!-- ʵ������ͼ�����ʾ������ -->
            </div>
        </div>
        
        <div id="status" class="status info">׼�������������ť��ʼ����</div>
        
        <div class="test-area">
            <h3>������Ϣ</h3>
            <div id="configDisplay" class="json-display">������԰�ť�鿴����</div>
        </div>
    </div>

    <script>
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function showConfig(config) {
            document.getElementById('configDisplay').textContent = JSON.stringify(config, null, 2);
        }
        
        // ����1: ֱ��ECharts��Ⱦ
        function testDirectChart() {
            setStatus('��ʼ����ֱ��ECharts��Ⱦ...', 'info');
            
            try {
                if (typeof echarts === 'undefined') {
                    throw new Error('ECharts��δ����');
                }
                
                const chartDiv = document.getElementById('chart');
                const chart = echarts.init(chartDiv);
                
                const option = {
                    title: {
                        text: '�����жԹ�������Ա�',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: '{b}: {c}��Ԫ'
                    },
                    xAxis: {
                        type: 'category',
                        data: ['��������', '�Ϻ�����', '�Ͼ�����', '�Ϸʷ���', '���ݷ���']
                    },
                    yAxis: {
                        type: 'value',
                        name: '���(��Ԫ)'
                    },
                    series: [{
                        name: '���(��Ԫ)',
                        type: 'bar',
                        data: [7731.07, 9892.58, 5368.80, 2693.49, 1214.51],
                        itemStyle: {
                            color: '#5470c6'
                        },
                        label: {
                            show: true,
                            position: 'top'
                        }
                    }]
                };
                
                chart.setOption(option);
                showConfig(option);
                setStatus('? ֱ����Ⱦ�ɹ���', 'success');
                
            } catch (error) {
                setStatus(`? ֱ����Ⱦʧ��: ${error.message}`, 'error');
            }
        }
        
        // ����2: JSON���ý���
        function testJsonParsing() {
            setStatus('��ʼ����JSON���ý���...', 'info');
            
            const jsonConfig = {
                "title": {
                    "text": "�����жԹ�������Ա�",
                    "left": "center"
                },
                "tooltip": {
                    "trigger": "axis",
                    "formatter": "{b}: {c}��Ԫ"
                },
                "xAxis": {
                    "type": "category",
                    "data": ["��������", "�Ϻ�����", "�Ͼ�����", "�Ϸʷ���", "���ݷ���"]
                },
                "yAxis": {
                    "type": "value",
                    "name": "���(��Ԫ)"
                },
                "series": [{
                    "name": "���(��Ԫ)",
                    "type": "bar",
                    "data": [7731.07, 9892.58, 5368.80, 2693.49, 1214.51],
                    "itemStyle": {
                        "color": "#91cc75"
                    },
                    "label": {
                        "show": true,
                        "position": "top"
                    }
                }]
            };
            
            try {
                const container = document.getElementById('jsonTestContainer');
                
                // ģ���˷��ص�JSON�ַ���
                const jsonString = JSON.stringify(jsonConfig);
                container.innerHTML = jsonString;
                
                // ģ��ǰ�˽�������
                setTimeout(() => {
                    processJsonContainer(container, jsonConfig);
                }, 100);
                
                showConfig(jsonConfig);
                setStatus('? JSON�������óɹ����ȴ�����...', 'info');
                
            } catch (error) {
                setStatus(`? JSON���ò���ʧ��: ${error.message}`, 'error');
            }
        }
        
        // ����3: ʵ������
        function testRealData() {
            setStatus('��ʼ����ʵ������...', 'info');
            
            // ���Ǵ����Ľ�ͼ����ȡ��ʵ������
            const realConfig = {
                "title": {
                    "text": "�����жԹ�������Ա�",
                    "left": "center",
                    "textStyle": {
                        "fontSize": 16
                    }
                },
                "tooltip": {
                    "trigger": "axis",
                    "axisPointer": {
                        "type": "shadow"
                    },
                    "formatter": "{b}: {c}��Ԫ"
                },
                "grid": {
                    "left": "10%",
                    "right": "10%",
                    "bottom": "15%",
                    "top": "15%",
                    "containLabel": true
                },
                "xAxis": {
                    "type": "category",
                    "data": ["��������", "�Ϻ�����", "�Ͼ�����", "�Ϸʷ���", "���ݷ���"],
                    "axisLabel": {
                        "rotate": 0,
                        "interval": 0,
                        "fontSize": 12
                    }
                },
                "yAxis": {
                    "type": "value",
                    "name": "���(��Ԫ)",
                    "nameTextStyle": {
                        "fontSize": 12
                    }
                },
                "series": [{
                    "name": "���(��Ԫ)",
                    "type": "bar",
                    "data": [7731.07, 9892.58, 5368.80, 2693.49, 1214.51],
                    "itemStyle": {
                        "color": "#5470c6",
                        "borderRadius": [4, 4, 0, 0]
                    },
                    "label": {
                        "show": true,
                        "position": "top",
                        "fontSize": 11,
                        "formatter": "{c}"
                    },
                    "barWidth": "60%"
                }]
            };
            
            try {
                const container = document.getElementById('realDataContainer');
                
                // ����ͼ������
                const chartDiv = document.createElement('div');
                chartDiv.style.width = '100%';
                chartDiv.style.height = '400px';
                chartDiv.id = 'realDataChart';
                
                container.innerHTML = '';
                container.appendChild(chartDiv);
                
                // ��ʼ��ͼ��
                const chart = echarts.init(chartDiv);
                chart.setOption(realConfig);
                
                showConfig(realConfig);
                setStatus('? ʵ��������Ⱦ�ɹ���', 'success');
                
            } catch (error) {
                setStatus(`? ʵ�����ݲ���ʧ��: ${error.message}`, 'error');
            }
        }
        
        // ����JSON������ģ��ǰ�˽����߼���
        function processJsonContainer(container, expectedConfig) {
            try {
                let content = container.innerHTML.trim();
                
                // ����HTML��ǩ
                content = content.replace(/<[^>]*>/g, '').trim();
                
                // ����JSON
                const parsedConfig = JSON.parse(content);
                
                // ����ͼ��
                const chartDiv = document.createElement('div');
                chartDiv.style.width = '100%';
                chartDiv.style.height = '400px';
                chartDiv.id = 'jsonTestChart';
                
                container.innerHTML = '';
                container.appendChild(chartDiv);
                
                const chart = echarts.init(chartDiv);
                chart.setOption(parsedConfig);
                
                setStatus('? JSON��������Ⱦ�ɹ���', 'success');
                
            } catch (error) {
                setStatus(`? JSON����ʧ��: ${error.message}`, 'error');
            }
        }
        
        // ҳ��������
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof echarts !== 'undefined') {
                setStatus(`? ECharts�Ѽ��أ��汾: ${echarts.version || 'δ֪'}`, 'success');
            } else {
                setStatus('? EChartsδ����', 'error');
            }
        });
    </script>
</body>
</html>
