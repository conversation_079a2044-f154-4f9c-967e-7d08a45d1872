# encoding=utf-8
from models.chatbot_model import ChatbotModel
from utils.app_init import before_init
from utils.helpers import load_all_scene_configs
from utils.response_formatter import ResponseFormatter
from flask import Flask, request, jsonify, send_file, render_template
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# 实例化ChatbotModel
chatbot_model = ChatbotModel(load_all_scene_configs())


@app.route('/multi_question', methods=['POST'])
def api_multi_question():
    data = request.json
    question = data.get('question')
    output_format = data.get('format', 'html')  # 默认输出HTML格式
    
    if not question:
        return jsonify({"error": "请输入查询的问题"}), 400

    # 获取原始响应
    response = chatbot_model.process_multi_question(question)
    
    # 格式化响应
    formatted_response = ResponseFormatter.format_response(response, output_format)
    
    return jsonify({
        "answer": response,  # 原始响应
        "formatted_answer": formatted_response,  # 格式化后的响应
        "format": output_format  # 使用的格式
    })


@app.route('/', methods=['GET'])
def index():
    return send_file('./demo/user_input.html')


if __name__ == '__main__':
    before_init()
    app.run(port=5000, debug=True)



    """
    1、如果我想查询贷款数据，应该怎么做？
    
    
    
    """
