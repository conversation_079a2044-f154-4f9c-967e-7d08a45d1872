# 智能图表选择优化

## 问题理解
您指出了一个重要问题：应该让大模型根据数据特点和用户问题来智能选择图表类型（柱状图、折线图、饼图等），而不是硬编码规则。

## 修正方案

### 1. 优化LLM提示词
让LLM根据数据结构和用户问题智能选择最合适的图表类型：

```python
user_msg = ("Can you generate the Python plotly code to chart the results of the dataframe? "
          "Analyze the data structure and user question to choose the most appropriate chart type: "
          "- Use bar charts (px.bar) for comparing categories, institutions, or discrete values "
          "- Use line charts (px.line) for time series, trends, or continuous data over time "
          "- Use pie charts (px.pie) for showing proportions, percentages, or parts of a whole "
          "- Use scatter plots (px.scatter) for relationships between two continuous variables "
          "- Use indicators (go.Indicator) if there is only one value to display "
          "Choose the chart type that best answers the user's question and visualizes the data effectively.")
```

### 2. 智能决策逻辑
LLM会根据以下因素选择图表类型：

#### 数据特征分析
- **分类对比**：机构名称 vs 余额 → 柱状图
- **时间趋势**：日期 vs 数值 → 折线图  
- **比例分布**：各部分占总体比例 → 饼图
- **关系分析**：两个连续变量 → 散点图

#### 用户问题分析
- **"各分行对比"** → 柱状图
- **"趋势变化"** → 折线图
- **"占比情况"** → 饼图
- **"相关性"** → 散点图

### 3. 回退机制保持通用
当LLM生成的代码执行失败时，使用通用的智能回退：

```python
if len(numeric_cols) == 1 and len(categorical_cols) >= 1:
    # 一个数值列和一个分类列：使用柱状图（最常见的对比场景）
    fig = px.bar(df, x=categorical_cols[0], y=numeric_cols[0],
               color=categorical_cols[0],
               color_discrete_sequence=colors)
    # 优化显示
    fig.update_layout(showlegend=len(df[categorical_cols[0]].unique()) <= 5)
    fig.update_xaxes(tickangle=45)  # 倾斜标签避免重叠
```

## 实际应用示例

### 您的数据场景
```
数据：机构名称 + 余额(万元)
用户问题：各分行昨天对公存款余额趋势
```

**LLM分析过程：**
1. 识别数据结构：分类变量（机构名称）+ 数值变量（余额）
2. 理解用户意图：对比各分行的余额
3. 选择图表类型：柱状图最适合展示分类对比
4. 生成代码：`px.bar(df, x='机构名称', y='余额(万元)')`

### 其他可能场景

#### 时间序列数据
```
数据：日期 + 余额
问题：最近一周的余额变化趋势
LLM选择：折线图 (px.line)
```

#### 比例分析
```
数据：机构名称 + 余额
问题：各分行余额占总体的比例
LLM选择：饼图 (px.pie)
```

#### 多维分析
```
数据：存款余额 + 贷款余额
问题：存贷款之间的关系
LLM选择：散点图 (px.scatter)
```

## 优势

### 1. 智能适应
- 根据具体数据和问题选择最佳图表
- 不受硬编码规则限制
- 能处理各种业务场景

### 2. 上下文理解
- 考虑用户问题的语义
- 理解数据的业务含义
- 选择最能回答问题的可视化方式

### 3. 灵活性
- 支持新的数据类型和问题类型
- 无需修改代码即可适应新场景
- LLM能力提升时自动受益

## 工作流程

```mermaid
graph TD
    A[用户问题 + 数据] --> B[LLM分析]
    B --> C{数据类型判断}
    C -->|分类+数值| D[考虑对比需求]
    C -->|时间+数值| E[考虑趋势需求]
    C -->|比例关系| F[考虑分布需求]
    D --> G[生成柱状图代码]
    E --> H[生成折线图代码]
    F --> I[生成饼图代码]
    G --> J[执行代码]
    H --> J
    I --> J
    J -->|成功| K[显示图表]
    J -->|失败| L[智能回退]
    L --> K
```

## 总结

现在系统能够：
- ? 让LLM根据数据和问题智能选择图表类型
- ? 提供详细的图表选择指导
- ? 保持通用的回退机制
- ? 优化图表显示效果

这样的设计更加灵活和智能，能够适应各种银行业务场景的可视化需求。
